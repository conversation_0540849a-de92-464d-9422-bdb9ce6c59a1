# Production Environment Variables for MyBinder
# Copy this file to .env.production and fill in your actual values

# Database Configuration (Supabase)
# Get this from Supabase Dashboard > Settings > Database > Connection string
DATABASE_URL="postgresql://postgres:[YOUR_PASSWORD]@[YOUR_HOST]:5432/postgres"

# JWT Secret for Authentication
# Generate a secure random string (32+ characters)
# You can use: openssl rand -base64 32
JWT_SECRET="your-super-secure-jwt-secret-for-production-32chars+"

# NextAuth Configuration
# Generate a secure random string (32+ characters)
NEXTAUTH_SECRET="your-nextauth-secret-32chars+"

# Your production URL (Vercel will provide this)
NEXTAUTH_URL="https://your-app-name.vercel.app"

# Optional: Node Environment
NODE_ENV="production"

# Optional: Logging Level
LOG_LEVEL="info"

# Optional: Database Connection Pool
DATABASE_CONNECTION_LIMIT="10"

# Instructions:
# 1. Copy this file: cp .env.production.example .env.production
# 2. Fill in your actual values
# 3. Add these same variables to Vercel dashboard
# 4. Never commit .env.production to git
