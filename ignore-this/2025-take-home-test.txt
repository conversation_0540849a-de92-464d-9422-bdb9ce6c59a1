﻿Take Home Assignment

Group Chat Application Development

Overview

Kandidat diminta untuk mengembangkan sebuah aplikasi group chat dengan fitur note-taking
terintegrasi

Instruksi Awal

1. Registrasi dan Eksplorasi

◦ Buat akun di aplikasi https://heybinder.com
◦ Eksplorasi seluruh fitur dan fungsionalitas aplikasi tersebut
◦ Pahami user experience dan workflow yang ada

2. Development Requirements

Kembangkan aplikasi serupa (tampilan tidak harus sama) dengan persyaratan fungsional berikut:

Core Requirements

Group Management

• User dapat membuat multiple groups
• User dapat melihat semua groups yang ada

Messaging System

• User dapat mengirimkan pesan ke dalam group
• User dapat melihat riwayat pesan dalam group

Notes Management

• User dapat menambahkan multiple notes dalam setiap group
• User dapat mengubah dan mengedit notes yang sudah ada
• Content notes disimpan dalam format "block-based" seperti Notion
• Notes hanya mendukung konten berupa teks

Technical Stack

Gunakan teknologi berikut dalam pengembangan:

• Frontend Framework: Next.js
• Database: PostgreSQL
• Styling: Tailwind CSS

Deliverables

Kandidat harus menyerahkan:

1. Source Code

• Repository lengkap dengan kode aplikasi
• README.md dengan instruksi instalasi dan menjalankan aplikasi
• Clean, well-documented code

2. System Design Documentation

• Dokumen yang menjelaskan arsitektur sistem secara ringkas beserta rationale pemilihan
design patterns dan teknologi
• bisa berupa dokumen tertulis (max 3 halaman) ATAU video penjelasan (max 5 menit) (bisa pilih salah satu)
• Database schema dan relationship design
• API design (jika applicable)

3. Live Application

• Aplikasi yang dapat diakses secara online untuk testing
• 2 akun demo yang sudah disiapkan untuk keperluan evaluasi
• Aplikasi harus fully functional dan stable

Evaluation Criteria

Penilaian akan fokus pada aspek-aspek berikut:

Primary Assessment Areas

• System Design: Arsitektur aplikasi, scalability considerations
• Database Design: Schema design, relationships, optimization
• Source Code Quality: Code organization, readability, best practices
• Implementation Results: Functionality, performance, reliability
• Additional Initiative: Fitur tambahan, creative solutions

Non-Assessment Areas

• UI/UX Design: Tampilan visual tidak menjadi fokus penilaian utama

Frequently Asked Questions

Q: Apakah perlu menambahkan fitur X dalam group chat?

A: Silakan menambahkan fitur yang dirasa relevan dan berguna dalam sebuah "group chat
application". Inisiatif tambahan akan menjadi nilai plus dalam penilaian.

Q: Apakah diperbolehkan menggunakan AI dalam pengerjaan tugas?

A: Penggunaan AI tools diperbolehkan. Namun, kandidat sebagai developer expert harus
memastikan kualitas dan validitas output yang dihasilkan.

Q: Apakah boleh menggunakan library atau teknologi tambahan?

A: Diperbolehkan menggunakan library tambahan selama tidak berkonflik dengan tech stack yang
telah ditentukan dan dapat dipertanggungjawabkan pemilihan penggunaannya.

Submission Guidelines

• Pastikan semua deliverables telah lengkap sebelum submission
• Test aplikasi secara menyeluruh sebelum diserahkan

Good luck and happy coding!
