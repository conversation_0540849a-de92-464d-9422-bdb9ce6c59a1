'use client'

import React, { createContext, useContext, useState } from 'react'
import { NoteWithBlocks, CreateNoteData, NoteBlockWithAuthor } from '@/types'

interface NotesContextType {
  notes: NoteWithBlocks[]
  selectedNote: NoteWithBlocks | null
  loading: boolean
  loadingNote: boolean
  createNote: (groupId: string, data: CreateNoteData) => Promise<NoteWithBlocks>
  updateNote: (noteId: string, data: Partial<CreateNoteData>) => Promise<NoteWithBlocks>
  deleteNote: (noteId: string) => Promise<void>
  selectNote: (note: NoteWithBlocks | null) => void
  loadNotes: (groupId: string) => Promise<void>
  loadNote: (noteId: string) => Promise<void>
  clearNotes: () => void
  // Block operations
  createBlock: (noteId: string, type: string, content: string, order: number) => Promise<NoteBlockWithAuthor>
  updateBlock: (blockId: string, data: { content?: string; type?: string }) => Promise<NoteBlockWithAuthor>
  deleteBlock: (blockId: string) => Promise<void>
  reorderBlocks: (noteId: string, blocks: { id: string; order: number }[]) => Promise<void>
}

const NotesContext = createContext<NotesContextType | undefined>(undefined)

export function NotesProvider({ children }: { children: React.ReactNode }) {
  const [notes, setNotes] = useState<NoteWithBlocks[]>([])
  const [selectedNote, setSelectedNote] = useState<NoteWithBlocks | null>(null)
  const [loading, setLoading] = useState(false)
  const [loadingNote, setLoadingNote] = useState(false)

  const loadNotes = async (groupId: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/groups/${groupId}/notes`)
      if (response.ok) {
        const data = await response.json()
        setNotes(data.notes)
      } else {
        console.error('Failed to load notes')
      }
    } catch (error) {
      console.error('Failed to load notes:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadNote = async (noteId: string) => {
    setLoadingNote(true)
    try {
      const response = await fetch(`/api/notes/${noteId}`)
      if (response.ok) {
        const data = await response.json()
        setSelectedNote(data.note)
      } else {
        console.error('Failed to load note')
      }
    } catch (error) {
      console.error('Failed to load note:', error)
    } finally {
      setLoadingNote(false)
    }
  }

  const createNote = async (groupId: string, data: CreateNoteData): Promise<NoteWithBlocks> => {
    const response = await fetch(`/api/groups/${groupId}/notes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create note')
    }

    const result = await response.json()
    await loadNotes(groupId)
    return result.note
  }

  const updateNote = async (noteId: string, data: Partial<CreateNoteData>): Promise<NoteWithBlocks> => {
    const response = await fetch(`/api/notes/${noteId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update note')
    }

    const result = await response.json()
    
    // Update selected note if it's the one being updated
    if (selectedNote?.id === noteId) {
      setSelectedNote(result.note)
    }
    
    // Update notes list
    setNotes(prev => prev.map(note => 
      note.id === noteId ? { ...note, ...data } : note
    ))
    
    return result.note
  }

  const deleteNote = async (noteId: string): Promise<void> => {
    const response = await fetch(`/api/notes/${noteId}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete note')
    }

    // Clear selected note if it's the one being deleted
    if (selectedNote?.id === noteId) {
      setSelectedNote(null)
    }
    
    // Remove from notes list
    setNotes(prev => prev.filter(note => note.id !== noteId))
  }

  const selectNote = (note: NoteWithBlocks | null) => {
    setSelectedNote(note)
    // Load full note details with blocks if note is provided
    if (note?.id) {
      loadNote(note.id)
    }
  }

  const clearNotes = () => {
    setNotes([])
    setSelectedNote(null)
  }

  // Block operations
  const createBlock = async (noteId: string, type: string, content: string, order: number): Promise<NoteBlockWithAuthor> => {
    const response = await fetch(`/api/notes/${noteId}/blocks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ type, content, order }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create block')
    }

    const result = await response.json()
    
    // Refresh the selected note to get updated blocks
    if (selectedNote?.id === noteId) {
      await loadNote(noteId)
    }
    
    return result.block
  }

  const updateBlock = async (blockId: string, data: { content?: string; type?: string }): Promise<NoteBlockWithAuthor> => {
    const response = await fetch(`/api/blocks/${blockId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update block')
    }

    const result = await response.json()
    
    // Update the block in selected note
    if (selectedNote) {
      setSelectedNote(prev => prev ? {
        ...prev,
        blocks: prev.blocks.map(block => 
          block.id === blockId ? { ...block, ...data } : block
        )
      } : null)
    }
    
    return result.block
  }

  const deleteBlock = async (blockId: string): Promise<void> => {
    const response = await fetch(`/api/blocks/${blockId}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete block')
    }

    // Remove block from selected note
    if (selectedNote) {
      setSelectedNote(prev => prev ? {
        ...prev,
        blocks: prev.blocks.filter(block => block.id !== blockId)
      } : null)
    }
  }

  const reorderBlocks = async (noteId: string, blocks: { id: string; order: number }[]): Promise<void> => {
    const response = await fetch(`/api/notes/${noteId}/blocks`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ blocks }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to reorder blocks')
    }

    // Refresh the selected note to get updated order
    if (selectedNote?.id === noteId) {
      await loadNote(noteId)
    }
  }

  return (
    <NotesContext.Provider value={{
      notes,
      selectedNote,
      loading,
      loadingNote,
      createNote,
      updateNote,
      deleteNote,
      selectNote,
      loadNotes,
      loadNote,
      clearNotes,
      createBlock,
      updateBlock,
      deleteBlock,
      reorderBlocks,
    }}>
      {children}
    </NotesContext.Provider>
  )
}

export function useNotes() {
  const context = useContext(NotesContext)
  if (context === undefined) {
    throw new Error('useNotes must be used within a NotesProvider')
  }
  return context
}
