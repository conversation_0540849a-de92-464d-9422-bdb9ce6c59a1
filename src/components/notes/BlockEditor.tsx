'use client'

import { useState, useRef, useEffect } from 'react'
import { useNotes } from '@/contexts/NotesContext'
import { NoteBlockWithAuthor } from '@/types'

interface BlockEditorProps {
  block: NoteBlockWithAuthor
  noteId: string
  isLast: boolean
  onAddBlock: (type?: string) => void
}

export default function BlockEditor({ block, noteId, isLast, onAddBlock }: BlockEditorProps) {
  const { updateBlock, deleteBlock } = useNotes()
  const [content, setContent] = useState(block.content)
  const [isEditing, setIsEditing] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    setContent(block.content)
  }, [block.content])

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus()
      // Auto-resize textarea
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
    }
  }, [isEditing])

  const handleSave = async () => {
    try {
      await updateBlock(block.id, { content })
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update block:', error)
      alert('Failed to update block')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (isLast && content.trim()) {
        onAddBlock('TEXT')
      } else {
        handleSave()
      }
    } else if (e.key === 'Escape') {
      setContent(block.content)
      setIsEditing(false)
    }
  }

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this block?')) {
      try {
        await deleteBlock(block.id)
      } catch (error) {
        console.error('Failed to delete block:', error)
        alert('Failed to delete block')
      }
    }
  }

  const handleTypeChange = async (newType: string) => {
    try {
      await updateBlock(block.id, { type: newType })
      setShowMenu(false)
    } catch (error) {
      console.error('Failed to update block type:', error)
      alert('Failed to update block type')
    }
  }

  const getBlockIcon = (type: string) => {
    switch (type) {
      case 'HEADING': return '📰'
      case 'BULLET_LIST': return '•'
      case 'NUMBERED_LIST': return '1.'
      case 'CODE': return '💻'
      case 'QUOTE': return '💬'
      default: return '📝'
    }
  }

  const getBlockStyle = (type: string) => {
    switch (type) {
      case 'HEADING':
        return 'text-xl font-bold text-gray-900'
      case 'CODE':
        return 'code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto'
      case 'QUOTE':
        return 'border-l-4 border-blue-400 pl-4 italic text-gray-700 bg-blue-50 py-2 rounded-r'
      default:
        return 'text-gray-900'
    }
  }

  const renderContent = () => {
    if (isEditing) {
      const editingStyle = block.type === 'CODE'
        ? 'w-full border-none outline-none resize-none code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto'
        : `w-full border-none outline-none resize-none ${getBlockStyle(block.type)} bg-transparent`

      return (
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleSave}
          className={editingStyle}
          placeholder={block.type === 'CODE' ? 'Enter your code here...' : 'Type something...'}
          rows={block.type === 'CODE' ? 3 : 1}
        />
      )
    }

    if (!content.trim()) {
      return (
        <div 
          onClick={() => setIsEditing(true)}
          className="text-gray-400 cursor-text py-2"
        >
          Type something...
        </div>
      )
    }

    const formattedContent = block.type === 'BULLET_LIST' 
      ? content.split('\n').map(line => line.trim() ? `• ${line}` : line).join('\n')
      : block.type === 'NUMBERED_LIST'
      ? content.split('\n').map((line, i) => line.trim() ? `${i + 1}. ${line}` : line).join('\n')
      : content

    return (
      <div 
        onClick={() => setIsEditing(true)}
        className={`cursor-text py-2 whitespace-pre-wrap ${getBlockStyle(block.type)}`}
      >
        {formattedContent}
      </div>
    )
  }

  return (
    <div className="group relative flex items-start space-x-2 py-1">
      {/* Block Type Icon & Menu */}
      <div className="relative">
        <button
          onClick={() => setShowMenu(!showMenu)}
          className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
          title="Change block type"
        >
          {getBlockIcon(block.type)}
        </button>
        
        {showMenu && (
          <div className="absolute left-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
            <div className="py-1">
              <button
                onClick={() => handleTypeChange('TEXT')}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                📝 Text
              </button>
              <button
                onClick={() => handleTypeChange('HEADING')}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                📰 Heading
              </button>
              <button
                onClick={() => handleTypeChange('BULLET_LIST')}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                • Bullet List
              </button>
              <button
                onClick={() => handleTypeChange('NUMBERED_LIST')}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                1. Numbered List
              </button>
              <button
                onClick={() => handleTypeChange('CODE')}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                💻 Code
              </button>
              <button
                onClick={() => handleTypeChange('QUOTE')}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                💬 Quote
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Block Content */}
      <div className="flex-1 min-w-0">
        {renderContent()}
      </div>

      {/* Block Actions */}
      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={handleDelete}
          className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500"
          title="Delete block"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
  )
}
