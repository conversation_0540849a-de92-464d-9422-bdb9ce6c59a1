import { POST } from '@/app/api/auth/login/route'

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn().mockImplementation((data, options) => ({
      status: options?.status || 200,
      statusText: options?.statusText || 'OK',
      headers: new Map(Object.entries(options?.headers || {})),
      json: jest.fn().mockResolvedValue(data),
      text: jest.fn().mockResolvedValue(JSON.stringify(data)),
      cookies: {
        set: jest.fn(),
        get: jest.fn(),
        delete: jest.fn(),
      },
    })),
  },
}))

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
  },
}))

// Mock auth utilities
jest.mock('@/lib/auth', () => ({
  verifyPassword: jest.fn(),
  generateToken: jest.fn(),
}))

import { prisma } from '@/lib/prisma'
import { verifyPassword, generateToken } from '@/lib/auth'

const mockPrisma = jest.mocked(prisma)
const mockVerifyPassword = jest.mocked(verifyPassword)
const mockGenerateToken = jest.mocked(generateToken)

describe('/api/auth/login', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should login user with valid credentials', async () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      username: 'testuser',
      password: 'hashedpassword',
      name: 'Test User',
      avatar: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    mockPrisma.user.findUnique.mockResolvedValue(mockUser)
    mockVerifyPassword.mockResolvedValue(true)
    mockGenerateToken.mockReturnValue('mock-jwt-token')

    // Create a proper mock request with json method
    const requestBody = {
      email: '<EMAIL>',
      password: 'password123',
    }

    const request = {
      json: jest.fn().mockResolvedValue(requestBody),
    } as any

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.message).toBe('Login successful')
    expect(data.user).toEqual({
      id: mockUser.id,
      email: mockUser.email,
      username: mockUser.username,
      name: mockUser.name,
      avatar: mockUser.avatar,
      createdAt: expect.any(Date),
    })
  })

  it('should return 401 for non-existent user', async () => {
    mockPrisma.user.findUnique.mockResolvedValue(null)

    const requestBody = {
      email: '<EMAIL>',
      password: 'password123',
    }

    const request = {
      json: jest.fn().mockResolvedValue(requestBody),
    } as any

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.error).toBe('Invalid email or password')
  })

  it('should return 401 for invalid password', async () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      username: 'testuser',
      password: 'hashedpassword',
      name: 'Test User',
      avatar: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    mockPrisma.user.findUnique.mockResolvedValue(mockUser)
    mockVerifyPassword.mockResolvedValue(false)

    const requestBody = {
      email: '<EMAIL>',
      password: 'wrongpassword',
    }

    const request = {
      json: jest.fn().mockResolvedValue(requestBody),
    } as any

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.error).toBe('Invalid email or password')
  })

  it('should return 400 for invalid input', async () => {
    const requestBody = {
      email: 'invalid-email',
      password: '',
    }

    const request = {
      json: jest.fn().mockResolvedValue(requestBody),
    } as any

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Validation error')
  })

  it('should handle database errors', async () => {
    mockPrisma.user.findUnique.mockRejectedValue(new Error('Database error'))

    const requestBody = {
      email: '<EMAIL>',
      password: 'password123',
    }

    const request = {
      json: jest.fn().mockResolvedValue(requestBody),
    } as any

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Internal server error')
  })
})
