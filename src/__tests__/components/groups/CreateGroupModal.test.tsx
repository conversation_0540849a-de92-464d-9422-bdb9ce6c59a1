import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import CreateGroupModal from '@/components/groups/CreateGroupModal'

// Mock the GroupContext
const mockCreateGroup = jest.fn()
const mockGroupContext = {
  groups: [],
  selectedGroup: null,
  loading: false,
  createGroup: mockCreateGroup,
  updateGroup: jest.fn(),
  deleteGroup: jest.fn(),
  selectGroup: jest.fn(),
  refreshGroups: jest.fn(),
  addMember: jest.fn(),
}

jest.mock('@/contexts/GroupContext', () => ({
  useGroups: () => mockGroupContext,
}))

describe('CreateGroupModal', () => {
  const mockOnClose = jest.fn()

  beforeEach(() => {
    mockCreateGroup.mockClear()
    mockOnClose.mockClear()
  })

  it('does not render when isOpen is false', () => {
    render(<CreateGroupModal isOpen={false} onClose={mockOnClose} />)
    
    expect(screen.queryByText(/create new group/i)).not.toBeInTheDocument()
  })

  it('renders modal when isOpen is true', () => {
    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)
    
    expect(screen.getByText(/create new group/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/group name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/private group/i)).toBeInTheDocument()
  })

  it('handles form submission with valid data', async () => {
    const user = userEvent.setup()
    mockCreateGroup.mockResolvedValue({ id: '1', name: 'Test Group' })
    
    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)
    
    const nameInput = screen.getByLabelText(/group name/i)
    const descriptionInput = screen.getByLabelText(/description/i)
    const privateCheckbox = screen.getByLabelText(/private group/i)
    const submitButton = screen.getByRole('button', { name: /create group/i })
    
    await user.type(nameInput, 'Test Group')
    await user.type(descriptionInput, 'Test Description')
    await user.click(privateCheckbox)
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(mockCreateGroup).toHaveBeenCalledWith({
        name: 'Test Group',
        description: 'Test Description',
        isPrivate: true,
      })
    })
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('shows error message on creation failure', async () => {
    const user = userEvent.setup()
    const errorMessage = 'Failed to create group'
    mockCreateGroup.mockRejectedValue(new Error(errorMessage))
    
    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)
    
    const nameInput = screen.getByLabelText(/group name/i)
    const submitButton = screen.getByRole('button', { name: /create group/i })
    
    await user.type(nameInput, 'Test Group')
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('closes modal when close button is clicked', async () => {
    const user = userEvent.setup()

    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)

    // Find the X button (SVG close icon)
    const closeButton = screen.getByRole('button', { name: '' })
    await user.click(closeButton)

    expect(mockOnClose).toHaveBeenCalled()
  })

  it('closes modal when cancel button is clicked', async () => {
    const user = userEvent.setup()
    
    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('disables submit button when name is empty', () => {
    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)
    
    const submitButton = screen.getByRole('button', { name: /create group/i })
    expect(submitButton).toBeDisabled()
  })

  it('enables submit button when name is provided', async () => {
    const user = userEvent.setup()
    
    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)
    
    const nameInput = screen.getByLabelText(/group name/i)
    const submitButton = screen.getByRole('button', { name: /create group/i })
    
    await user.type(nameInput, 'Test Group')
    
    expect(submitButton).not.toBeDisabled()
  })

  it('resets form when close button is clicked', async () => {
    const user = userEvent.setup()
    render(<CreateGroupModal isOpen={true} onClose={mockOnClose} />)

    const nameInput = screen.getByLabelText(/group name/i)
    await user.type(nameInput, 'Test Group')

    // Click close button
    const closeButton = screen.getByRole('button', { name: '' })
    await user.click(closeButton)

    // Verify onClose was called and form would be reset
    expect(mockOnClose).toHaveBeenCalled()
  })
})
