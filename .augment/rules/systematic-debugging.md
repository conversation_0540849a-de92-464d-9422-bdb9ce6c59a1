---
type: "manual"
---

Let's approach this systematically using a modified Fagan Inspection methodology:

    Initial Overview: First, explain the problem/bug in plain language and what the expected vs actual behaviour is.

    Systematic Inspection (Fagan-style):
        Line-by-line walkthrough as the "Reader" role
        Identify defects without fixing yet (pure inspection phase)
        Check against common defect categories: logic errors, boundary conditions, error handling, data flow issues

    Root Cause Analysis: After identifying issues, trace back to find the fundamental cause, not just symptoms.

    Solution & Verification: Propose fixes and explicitly verify each one would resolve the identified issues.

Think aloud through each phase. If you spot assumptions, state them explicitly. If something seems unclear, flag it rather than guessing.
