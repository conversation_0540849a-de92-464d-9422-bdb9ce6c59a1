---
type: "agent_requested"
description: "Example description"
---

Initial Context Gathering

ROLE: You are a senior software architect analysing requirements. TASK: Before writing any code, thoroughly understand the project context and requirements.

INSTRUCTIONS:

    Read all relevant files in the project directory
    Examine existing documentation (README.md, docs/ etc.)
    Analyse the codebase structure and dependencies
    Identify coding conventions and patterns used
    Review any existing tests to understand expected behaviour

THINKING MODE: Think step by step about the project landscape.

OUTPUT FORMAT:

    Project overview summary
    Key technologies and frameworks identified
    Existing patterns and conventions
    Potential challenges or constraints
    Questions that need clarification

Do NOT write any code yet. Focus only on understanding.