# Panduan Deployment MyBinder

Dokumen ini berisi panduan lengkap untuk deploy aplikasi MyBinder ke production menggunakan platform gratis.

## 🎯 Overview Deployment

- **Frontend**: Vercel (gratis)
- **Database**: Supabase PostgreSQL (gratis)
- **Domain**: Subdomain Vercel (gratis)

## 📋 Persiapan

### 1. <PERSON><PERSON><PERSON> yang <PERSON>

1. **GitHub Account** - untuk repository
2. **Vercel Account** - untuk hosting frontend
3. **Supabase Account** - untuk database PostgreSQL

### 2. Repository Setup

```bash
# Push ke GitHub repository
git add .
git commit -m "Ready for deployment"
git push origin main
```

## 🗄️ Setup Database (Supabase)

### 1. Buat Project Supabase

1. Kunjungi [supabase.com](https://supabase.com)
2. Sign up/Login dengan GitHub
3. Klik "New Project"
4. Pilih organization dan isi:
   - **Name**: `mybinder-db`
   - **Database Password**: Generate strong password
   - **Region**: <PERSON><PERSON>h yang terdekat
5. Klik "Create new project"

### 2. Setup Database Schema

1. Tunggu project selesai dibuat (2-3 menit)
2. Buka tab "SQL Editor"
3. Jalankan schema dari file `prisma/schema.prisma`:

```sql
-- Supabase akan otomatis membuat tables berdasarkan Prisma schema
-- Kita akan menggunakan Prisma migrate
```

### 3. Dapatkan Connection String

1. Buka "Settings" > "Database"
2. Scroll ke "Connection string"
3. Copy "URI" connection string
4. Format: `postgresql://postgres:[password]@[host]:5432/postgres`

### 4. Setup Environment Variables

Buat file `.env.production`:

```env
# Database (Supabase)
DATABASE_URL="postgresql://postgres:[YOUR_PASSWORD]@[YOUR_HOST]:5432/postgres"

# JWT Secret (generate random 32+ character string)
JWT_SECRET="your-super-secure-jwt-secret-for-production-32chars+"

# Next.js
NEXTAUTH_SECRET="your-nextauth-secret-32chars+"
NEXTAUTH_URL="https://your-app-name.vercel.app"
```

## 🚀 Deploy Frontend (Vercel)

### 1. Connect Repository

1. Kunjungi [vercel.com](https://vercel.com)
2. Sign up/Login dengan GitHub
3. Klik "New Project"
4. Import repository MyBinder
5. Configure project:
   - **Framework Preset**: Next.js
   - **Root Directory**: `./`
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`

### 2. Environment Variables

Di Vercel dashboard:

1. Buka "Settings" > "Environment Variables"
2. Tambahkan variables:

```
DATABASE_URL = postgresql://postgres:[password]@[host]:5432/postgres
JWT_SECRET = your-super-secure-jwt-secret-for-production-32chars+
NEXTAUTH_SECRET = your-nextauth-secret-32chars+
NEXTAUTH_URL = https://your-app-name.vercel.app
```

### 3. Deploy

1. Klik "Deploy"
2. Tunggu build selesai (3-5 menit)
3. Aplikasi akan tersedia di `https://your-app-name.vercel.app`

## 🔄 Database Migration & Seeding

### 1. Setup Prisma untuk Production

```bash
# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Push schema ke Supabase
npx prisma db push

# Seed database dengan data demo
npm run db:seed
```

### 2. Verifikasi Database

1. Buka Supabase dashboard
2. Klik "Table Editor"
3. Pastikan tables sudah terbuat:
   - `User`
   - `Group`
   - `GroupMember`
   - `Message`
   - `Note`
   - `NoteBlock`

## ✅ Verifikasi Deployment

### 1. Test Aplikasi

1. Buka URL Vercel app
2. Test login dengan akun demo:
   - Email: `<EMAIL>`
   - Password: `demo123`

### 2. Test Fitur

- ✅ Login/Register
- ✅ Dashboard
- ✅ Group chat
- ✅ Note-taking
- ✅ Real-time updates

## 🔧 Troubleshooting

### Database Connection Error

```bash
# Check connection string format
DATABASE_URL="postgresql://postgres:[password]@[host]:5432/postgres"

# Verify Supabase project is active
# Check password contains special characters (escape them)
```

### Build Errors

```bash
# Check environment variables in Vercel
# Verify all dependencies in package.json
# Check TypeScript errors
```

### 404 Errors

```bash
# Verify Next.js routing
# Check file structure in src/app/
# Ensure all pages are properly exported
```

## 📊 Monitoring

### Vercel Analytics

1. Buka Vercel dashboard
2. Klik "Analytics" tab
3. Monitor:
   - Page views
   - Performance metrics
   - Error rates

### Supabase Monitoring

1. Buka Supabase dashboard
2. Klik "Reports" tab
3. Monitor:
   - Database usage
   - API requests
   - Performance

## 🔄 Updates & Maintenance

### Deploy Updates

```bash
# Push changes to GitHub
git add .
git commit -m "Update feature"
git push origin main

# Vercel akan otomatis deploy
```

### Database Updates

```bash
# Untuk schema changes
npx prisma db push

# Untuk data updates
npm run db:seed
```

## 💰 Cost Monitoring

### Vercel (Gratis)
- ✅ 100GB bandwidth/month
- ✅ 1000 serverless function invocations/day
- ✅ Custom domain support

### Supabase (Gratis)
- ✅ 500MB database storage
- ✅ 2GB bandwidth/month
- ✅ 50,000 monthly active users

## 🆘 Support

Jika mengalami masalah:

1. Check Vercel build logs
2. Check Supabase logs
3. Verify environment variables
4. Test locally first

---

**Deployment berhasil! 🎉**
