#!/bin/bash

# MyBinder Deployment Script
# This script helps deploy MyBinder to production

set -e

echo "🚀 MyBinder Deployment Script"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_error "git is not installed"
        exit 1
    fi
    
    print_success "All dependencies are installed"
}

# Install project dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    npm install
    print_success "Dependencies installed"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    npm test
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
    else
        print_error "Tests failed. Please fix before deploying."
        exit 1
    fi
}

# Build the project
build_project() {
    print_status "Building project for production..."
    npm run build
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Check environment variables
check_env_vars() {
    print_status "Checking environment variables..."
    
    if [ -z "$DATABASE_URL" ]; then
        print_warning "DATABASE_URL not set. Make sure to set it in Vercel dashboard."
    fi
    
    if [ -z "$JWT_SECRET" ]; then
        print_warning "JWT_SECRET not set. Make sure to set it in Vercel dashboard."
    fi
    
    if [ -z "$NEXTAUTH_SECRET" ]; then
        print_warning "NEXTAUTH_SECRET not set. Make sure to set it in Vercel dashboard."
    fi
    
    print_success "Environment variables check completed"
}

# Generate Prisma client
generate_prisma() {
    print_status "Generating Prisma client..."
    npx prisma generate
    print_success "Prisma client generated"
}

# Deploy to Vercel (if Vercel CLI is installed)
deploy_vercel() {
    if command -v vercel &> /dev/null; then
        print_status "Deploying to Vercel..."
        vercel --prod
        print_success "Deployed to Vercel"
    else
        print_warning "Vercel CLI not installed. Please deploy manually:"
        echo "1. Push code to GitHub"
        echo "2. Connect repository to Vercel"
        echo "3. Set environment variables"
        echo "4. Deploy"
    fi
}

# Setup database (if DATABASE_URL is provided)
setup_database() {
    if [ ! -z "$DATABASE_URL" ]; then
        print_status "Setting up production database..."
        npx prisma db push
        print_status "Seeding database with demo data..."
        npm run db:seed
        print_success "Database setup completed"
    else
        print_warning "DATABASE_URL not provided. Skipping database setup."
        echo "Please set up your Supabase database manually:"
        echo "1. Create Supabase project"
        echo "2. Get connection string"
        echo "3. Run: npx prisma db push"
        echo "4. Run: npm run db:seed"
    fi
}

# Main deployment function
main() {
    echo ""
    print_status "Starting deployment process..."
    echo ""
    
    check_dependencies
    install_dependencies
    generate_prisma
    check_env_vars
    run_tests
    build_project
    
    echo ""
    print_status "Pre-deployment checks completed successfully!"
    echo ""
    
    # Ask user if they want to continue with deployment
    read -p "Do you want to continue with deployment? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
        deploy_vercel
        
        echo ""
        print_success "🎉 Deployment completed!"
        echo ""
        echo "Next steps:"
        echo "1. Verify your app is running at the Vercel URL"
        echo "2. Test login with demo accounts:"
        echo "   - <EMAIL> / demo123"
        echo "   - <EMAIL> / demo123"
        echo "3. Check all features are working correctly"
        echo ""
    else
        print_status "Deployment cancelled by user"
    fi
}

# Run main function
main "$@"
