module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>a,"requireAuth",()=>n]);var t=e.i(79832),r=e.i(98043);async function a(e){try{let a=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!a)return null;let n=(0,t.verifyToken)(a);if(!n)return null;return await r.prisma.user.findUnique({where:{id:n.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function n(e){return async t=>{let r=await a(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},51725,(e,t,r)=>{},27675,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>T,"routeModule",()=>A,"serverHooks",()=>j,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>N],27675);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var R=e.i(220);e.s(["DELETE",()=>k,"PUT",()=>y],47477);var f=e.i(89171),v=e.i(98043),w=e.i(24960),g=e.i(69719);let E=g.z.object({content:g.z.string().max(5e3,"Content too long").optional(),type:g.z.enum(["TEXT","HEADING","BULLET_LIST","NUMBERED_LIST","CODE","QUOTE"]).optional()});async function y(e,{params:t}){try{let r=await (0,w.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{blockId:a}=await t,n=await e.json(),o=E.parse(n);if(!await v.prisma.noteBlock.findFirst({where:{id:a,note:{group:{members:{some:{userId:r.id}}}}}}))return f.NextResponse.json({error:"Block not found or access denied"},{status:404});let s=await v.prisma.noteBlock.update({where:{id:a},data:o,include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}}});return f.NextResponse.json({message:"Block updated successfully",block:s})}catch(e){if(e instanceof g.z.ZodError)return f.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Update block error:",e),f.NextResponse.json({error:"Failed to update block"},{status:500})}}async function k(e,{params:t}){try{let r=await (0,w.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{blockId:a}=await t,n=await v.prisma.noteBlock.findFirst({where:{id:a,note:{group:{members:{some:{userId:r.id}}}}},include:{note:!0}});if(!n)return f.NextResponse.json({error:"Block not found or access denied"},{status:404});return await v.prisma.noteBlock.delete({where:{id:a}}),await v.prisma.noteBlock.updateMany({where:{noteId:n.noteId,order:{gt:n.order}},data:{order:{decrement:1}}}),f.NextResponse.json({message:"Block deleted successfully"})}catch(e){return console.error("Delete block error:",e),f.NextResponse.json({error:"Failed to delete block"},{status:500})}}var b=e.i(47477);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/blocks/[blockId]/route",pathname:"/api/blocks/[blockId]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/blocks/[blockId]/route.ts",nextConfigOutput:"",userland:b}),{workAsyncStorage:C,workUnitAsyncStorage:N,serverHooks:j}=A;function T(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:N})}async function q(e,t,a){var f;let v="/api/blocks/[blockId]/route";v=v.replace(/\/index$/,"")||"/";let w=await A.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:g,params:E,nextConfig:y,isDraftMode:k,prerenderManifest:b,routerServerContext:C,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,resolvedPathname:T}=w,q=(0,s.normalizeAppPath)(v),I=!!(b.dynamicRoutes[q]||b.routes[T]);if(I&&!k){let e=!!b.routes[T],t=b.dynamicRoutes[q];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let U=null;!I||A.isDev||k||(U="/index"===(U=T)?"/":U);let O=!0===A.isDev||!I,P=I&&!O,_=e.method||"GET",S=(0,o.getTracer)(),H=S.getActiveScopeSpan(),B={params:E,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=y.experimental)?void 0:f.cacheLife,isRevalidate:P,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>A.onRequestError(e,t,a,C)},sharedContext:{buildId:g}},D=new i.NodeNextRequest(e),M=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let s=async r=>A.handle(F,B).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=S.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${_} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),i=async o=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=B.renderOpts.fetchMetrics;let l=B.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=B.renderOpts.collectedTags;if(!I)return await (0,c.sendResponse)(D,M,i,B.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==B.renderOpts.collectedRevalidate&&!(B.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&B.renderOpts.collectedRevalidate,a=void 0===B.renderOpts.collectedExpire||B.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:B.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:P,isOnDemandRevalidate:N})},C),t}},m=await A.handleResponse({req:e,nextConfig:y,cacheKey:U,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,responseGenerator:d,waitUntil:a.waitUntil});if(!I)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),k&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&I||f.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,c.sendResponse)(D,M,new Response(m.value.body,{headers:f,status:m.value.status||200})),null};H?await i(H):await S.withPropagatedContext(e.headers,()=>S.trace(d.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},i))}catch(t){if(H||t instanceof m.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:q,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:P,isOnDemandRevalidate:N})}),I)throw t;return await (0,c.sendResponse)(D,M,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__6fb6c356._.js.map