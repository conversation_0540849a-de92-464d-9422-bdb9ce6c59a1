module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>a,"requireAuth",()=>n]);var t=e.i(79832),r=e.i(98043);async function a(e){try{let a=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!a)return null;let n=(0,t.verifyToken)(a);if(!n)return null;return await r.prisma.user.findUnique({where:{id:n.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function n(e){return async t=>{let r=await a(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},14478,(e,t,r)=>{},87309,e=>{"use strict";e.s(["handler",()=>k,"patchFetch",()=>T,"routeModule",()=>b,"serverHooks",()=>q,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>j],87309);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),u=e.i(87718),l=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var g=e.i(220);e.s(["GET",()=>E,"POST",()=>A],94062);var R=e.i(89171),v=e.i(98043),f=e.i(24960),w=e.i(69719);let y=w.z.object({title:w.z.string().min(1,"Note title is required").max(200,"Title too long"),description:w.z.string().max(500,"Description too long").optional()});async function E(e,{params:t}){try{let r=await (0,f.getAuthenticatedUser)(e);if(!r)return R.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:a}=await t;if(!await v.prisma.groupMember.findFirst({where:{groupId:a,userId:r.id}}))return R.NextResponse.json({error:"Access denied. You are not a member of this group."},{status:403});let n=await v.prisma.note.findMany({where:{groupId:a},include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}},_count:{select:{blocks:!0}}},orderBy:{updatedAt:"desc"}});return R.NextResponse.json({notes:n})}catch(e){return console.error("Get notes error:",e),R.NextResponse.json({error:"Failed to fetch notes"},{status:500})}}async function A(e,{params:t}){try{let r=await (0,f.getAuthenticatedUser)(e);if(!r)return R.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:a}=await t,n=await e.json(),{title:o,description:s}=y.parse(n);if(!await v.prisma.groupMember.findFirst({where:{groupId:a,userId:r.id}}))return R.NextResponse.json({error:"Access denied. You are not a member of this group."},{status:403});let i=await v.prisma.note.create({data:{title:o,description:s,authorId:r.id,groupId:a,blocks:{create:{type:"TEXT",content:"",order:0,authorId:r.id}}},include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}},blocks:{include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}},orderBy:{order:"asc"}}}});return R.NextResponse.json({message:"Note created successfully",note:i})}catch(e){if(e instanceof w.z.ZodError)return R.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Create note error:",e),R.NextResponse.json({error:"Failed to create note"},{status:500})}}var C=e.i(94062);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/groups/[groupId]/notes/route",pathname:"/api/groups/[groupId]/notes",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/groups/[groupId]/notes/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:N,workUnitAsyncStorage:j,serverHooks:q}=b;function T(){return(0,a.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:j})}async function k(e,t,a){var R;let v="/api/groups/[groupId]/notes/route";v=v.replace(/\/index$/,"")||"/";let f=await b.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:A,prerenderManifest:C,routerServerContext:N,isOnDemandRevalidate:j,revalidateOnlyGenerated:q,resolvedPathname:T}=f,k=(0,s.normalizeAppPath)(v),I=!!(C.dynamicRoutes[k]||C.routes[T]);if(I&&!A){let e=!!C.routes[T],t=C.dynamicRoutes[k];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!I||b.isDev||A||(O="/index"===(O=T)?"/":O);let P=!0===b.isDev||!I,_=I&&!P,S=e.method||"GET",U=(0,o.getTracer)(),H=U.getActiveScopeSpan(),M={params:y,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=E.experimental)?void 0:R.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>b.onRequestError(e,t,a,N)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=u.NextRequestAdapter.fromNodeNextRequest(D,(0,u.signalFromNodeResponse)(t));try{let s=async r=>b.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${S} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${S} ${e.url}`)}),i=async o=>{var i,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&j&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=M.renderOpts.fetchMetrics;let u=M.renderOpts.pendingWaitUntil;u&&a.waitUntil&&(a.waitUntil(u),u=void 0);let l=M.renderOpts.collectedTags;if(!I)return await (0,p.sendResponse)(D,F,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:j})},N),t}},m=await b.handleResponse({req:e,nextConfig:E,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:q,responseGenerator:l,waitUntil:a.waitUntil});if(!I)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(u=m.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&I||R.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(D,F,new Response(m.value.body,{headers:R,status:m.value.status||200})),null};H?await i(H):await U.withPropagatedContext(e.headers,()=>U.trace(l.BaseServerSpan.handleRequest,{spanName:`${S} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":S,"http.target":e.url}},i))}catch(t){if(H||t instanceof m.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:j})}),I)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__4a9ee112._.js.map