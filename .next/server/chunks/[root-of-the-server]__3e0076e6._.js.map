{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: any): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, JWT_SECRET)\n  } catch (error) {\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,aAAa,QAAgB;IACjD,OAAO,8IAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,8IAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAY;IACxC,OAAO,kJAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,kJAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/middleware.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { verifyToken } from './auth'\nimport { prisma } from './prisma'\n\nexport async function getAuthenticatedUser(request: NextRequest) {\n  try {\n    // Get token from cookie or Authorization header\n    const token = request.cookies.get('auth-token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!token) {\n      return null\n    }\n\n    // Verify token\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        name: true,\n        avatar: true,\n        createdAt: true,\n        updatedAt: true,\n      },\n    })\n\n    return user\n  } catch (error) {\n    console.error('Auth middleware error:', error)\n    return null\n  }\n}\n\nexport function requireAuth(handler: (request: NextRequest, user: any) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getAuthenticatedUser(request)\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAEO,eAAe,qBAAqB,OAAoB;IAC7D,IAAI;QACF,gDAAgD;QAChD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAEvE,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,eAAe;QACf,MAAM,UAAU,IAAA,mIAAW,EAAC;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,gBAAgB;QAChB,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,OAA+D;IACzF,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,qBAAqB;QAExC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/app/api/notes/%5BnoteId%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getAuthenticatedUser } from '@/lib/middleware'\nimport { z } from 'zod'\n\nconst updateNoteSchema = z.object({\n  title: z.string().min(1, 'Note title is required').max(200, 'Title too long').optional(),\n  description: z.string().max(500, 'Description too long').optional(),\n})\n\n// GET /api/notes/[noteId] - Get specific note with blocks\nexport async function GET(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = await params\n\n    // Get note with blocks and check access\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        group: {\n          members: {\n            some: { userId: user.id }\n          }\n        }\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        group: {\n          select: {\n            id: true,\n            name: true,\n          }\n        },\n        blocks: {\n          include: {\n            author: {\n              select: {\n                id: true,\n                username: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          },\n          orderBy: {\n            order: 'asc'\n          }\n        }\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    return NextResponse.json({ note })\n  } catch (error) {\n    console.error('Get note error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch note' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT /api/notes/[noteId] - Update note\nexport async function PUT(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = await params\n    const body = await request.json()\n    const updateData = updateNoteSchema.parse(body)\n\n    // Check if user has access to the note\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        group: {\n          members: {\n            some: { userId: user.id }\n          }\n        }\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Update note\n    const updatedNote = await prisma.note.update({\n      where: { id: noteId },\n      data: updateData,\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        blocks: {\n          include: {\n            author: {\n              select: {\n                id: true,\n                username: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          },\n          orderBy: {\n            order: 'asc'\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Note updated successfully',\n      note: updatedNote\n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Update note error:', error)\n    return NextResponse.json(\n      { error: 'Failed to update note' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE /api/notes/[noteId] - Delete note\nexport async function DELETE(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = await params\n\n    // Check if user is the author or has admin/owner role in the group\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        OR: [\n          { authorId: user.id },\n          {\n            group: {\n              OR: [\n                { ownerId: user.id },\n                {\n                  members: {\n                    some: {\n                      userId: user.id,\n                      role: { in: ['OWNER', 'ADMIN'] }\n                    }\n                  }\n                }\n              ]\n            }\n          }\n        ]\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or insufficient permissions' },\n        { status: 404 }\n      )\n    }\n\n    await prisma.note.delete({\n      where: { id: noteId }\n    })\n\n    return NextResponse.json({\n      message: 'Note deleted successfully'\n    })\n  } catch (error) {\n    console.error('Delete note error:', error)\n    return NextResponse.json(\n      { error: 'Failed to delete note' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,mBAAmB,oLAAC,CAAC,MAAM,CAAC;IAChC,OAAO,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACtF,aAAa,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;AACnE;AAGO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAA2C;IACjG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;QAEzB,wCAAwC;QACxC,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;gBACJ,OAAO;oBACL,SAAS;wBACP,MAAM;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBAC1B;gBACF;YACF;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,QAAQ;wBACN,IAAI;wBACJ,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,SAAS;wBACP,QAAQ;4BACN,QAAQ;gCACN,IAAI;gCACJ,UAAU;gCACV,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;oBACA,SAAS;wBACP,OAAO;oBACT;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAA2C;IACjG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;QACzB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,aAAa,iBAAiB,KAAK,CAAC;QAE1C,uCAAuC;QACvC,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;gBACJ,OAAO;oBACL,SAAS;wBACP,MAAM;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBAC1B;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,cAAc,MAAM,gIAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;YACN,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;wBACP,QAAQ;4BACN,QAAQ;gCACN,IAAI;gCACJ,UAAU;gCACV,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;oBACA,SAAS;wBACP,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB,EAAE,EAAE,MAAM,EAA2C;IACpG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;QAEzB,mEAAmE;QACnE,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;gBACJ,IAAI;oBACF;wBAAE,UAAU,KAAK,EAAE;oBAAC;oBACpB;wBACE,OAAO;4BACL,IAAI;gCACF;oCAAE,SAAS,KAAK,EAAE;gCAAC;gCACnB;oCACE,SAAS;wCACP,MAAM;4CACJ,QAAQ,KAAK,EAAE;4CACf,MAAM;gDAAE,IAAI;oDAAC;oDAAS;iDAAQ;4CAAC;wCACjC;oCACF;gCACF;6BACD;wBACH;oBACF;iBACD;YACH;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6C,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gIAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}