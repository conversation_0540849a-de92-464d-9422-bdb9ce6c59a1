module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>a,"requireAuth",()=>n]);var t=e.i(79832),r=e.i(98043);async function a(e){try{let a=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!a)return null;let n=(0,t.verifyToken)(a);if(!n)return null;return await r.prisma.user.findUnique({where:{id:n.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function n(e){return async t=>{let r=await a(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},35932,(e,t,r)=>{},40725,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>I,"routeModule",()=>A,"serverHooks",()=>j,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>N],40725);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),u=e.i(87718),l=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),m=e.i(26937),x=e.i(10372),h=e.i(93695);e.i(52474);var R=e.i(220);e.s(["POST",()=>y],32515);var f=e.i(89171),g=e.i(98043),v=e.i(24960),w=e.i(69719);let E=w.z.object({email:w.z.string().email("Invalid email format"),role:w.z.enum(["MEMBER","ADMIN"]).optional().default("MEMBER")});async function y(e,{params:t}){try{let r=await (0,v.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:a}=t,n=await e.json(),{email:s,role:o}=E.parse(n);if(!await g.prisma.group.findFirst({where:{id:a,OR:[{ownerId:r.id},{members:{some:{userId:r.id,role:{in:["OWNER","ADMIN"]}}}}]}}))return f.NextResponse.json({error:"Group not found or insufficient permissions"},{status:404});let i=await g.prisma.user.findUnique({where:{email:s},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0}});if(!i)return f.NextResponse.json({error:"User not found"},{status:404});if(await g.prisma.groupMember.findUnique({where:{userId_groupId:{userId:i.id,groupId:a}}}))return f.NextResponse.json({error:"User is already a member of this group"},{status:400});let u=await g.prisma.groupMember.create({data:{userId:i.id,groupId:a,role:o},include:{user:{select:{id:!0,email:!0,username:!0,name:!0,avatar:!0}}}});return f.NextResponse.json({message:"Member added successfully",member:u})}catch(e){if(e instanceof w.z.ZodError)return f.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Add member error:",e),f.NextResponse.json({error:"Failed to add member"},{status:500})}}var b=e.i(32515);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/groups/[groupId]/members/route",pathname:"/api/groups/[groupId]/members",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/groups/[groupId]/members/route.ts",nextConfigOutput:"",userland:b}),{workAsyncStorage:C,workUnitAsyncStorage:N,serverHooks:j}=A;function I(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:N})}async function q(e,t,a){var f;let g="/api/groups/[groupId]/members/route";g=g.replace(/\/index$/,"")||"/";let v=await A.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:E,nextConfig:y,isDraftMode:b,prerenderManifest:C,routerServerContext:N,isOnDemandRevalidate:j,revalidateOnlyGenerated:I,resolvedPathname:q}=v,O=(0,o.normalizeAppPath)(g),T=!!(C.dynamicRoutes[O]||C.routes[q]);if(T&&!b){let e=!!C.routes[q],t=C.dynamicRoutes[O];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let k=null;!T||A.isDev||b||(k="/index"===(k=q)?"/":k);let M=!0===A.isDev||!T,P=T&&!M,U=e.method||"GET",_=(0,s.getTracer)(),S=_.getActiveScopeSpan(),H={params:E,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:M,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=y.experimental)?void 0:f.cacheLife,isRevalidate:P,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>A.onRequestError(e,t,a,N)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=u.NextRequestAdapter.fromNodeNextRequest(D,(0,u.signalFromNodeResponse)(t));try{let o=async r=>A.handle($,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=_.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${U} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),i=async s=>{var i,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&j&&I&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=H.renderOpts.fetchMetrics;let u=H.renderOpts.pendingWaitUntil;u&&a.waitUntil&&(a.waitUntil(u),u=void 0);let l=H.renderOpts.collectedTags;if(!T)return await (0,p.sendResponse)(D,F,i,H.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[x.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,a=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:P,isOnDemandRevalidate:j})},N),t}},h=await A.handleResponse({req:e,nextConfig:y,cacheKey:k,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:I,responseGenerator:l,waitUntil:a.waitUntil});if(!T)return null;if((null==h||null==(i=h.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(u=h.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),b&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&T||f.delete(x.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(D,F,new Response(h.value.body,{headers:f,status:h.value.status||200})),null};S?await i(S):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},i))}catch(t){if(S||t instanceof h.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:O,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:P,isOnDemandRevalidate:j})}),T)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__df873e03._.js.map