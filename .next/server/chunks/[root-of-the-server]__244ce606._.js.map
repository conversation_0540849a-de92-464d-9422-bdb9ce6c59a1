{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: any): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, JWT_SECRET)\n  } catch (error) {\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,aAAa,QAAgB;IACjD,OAAO,8IAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,8IAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAY;IACxC,OAAO,kJAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,kJAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/middleware.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { verifyToken } from './auth'\nimport { prisma } from './prisma'\n\nexport async function getAuthenticatedUser(request: NextRequest) {\n  try {\n    // Get token from cookie or Authorization header\n    const token = request.cookies.get('auth-token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!token) {\n      return null\n    }\n\n    // Verify token\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        name: true,\n        avatar: true,\n        createdAt: true,\n        updatedAt: true,\n      },\n    })\n\n    return user\n  } catch (error) {\n    console.error('Auth middleware error:', error)\n    return null\n  }\n}\n\nexport function requireAuth(handler: (request: NextRequest, user: any) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getAuthenticatedUser(request)\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAEO,eAAe,qBAAqB,OAAoB;IAC7D,IAAI;QACF,gDAAgD;QAChD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAEvE,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,eAAe;QACf,MAAM,UAAU,IAAA,mIAAW,EAAC;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,gBAAgB;QAChB,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,OAA+D;IACzF,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,qBAAqB;QAExC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/app/api/notes/%5BnoteId%5D/blocks/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getAuthenticatedUser } from '@/lib/middleware'\nimport { z } from 'zod'\n\nconst createBlockSchema = z.object({\n  type: z.enum(['TEXT', 'HEADING_1', 'HEADING_2', 'HEADING_3', 'HEADING', 'BULLET_LIST', 'NUMBERED_LIST', 'CODE', 'QUOTE']),\n  content: z.string().max(5000, 'Content too long'),\n  order: z.number().int().min(0),\n})\n\nconst updateBlockSchema = z.object({\n  content: z.string().max(5000, 'Content too long').optional(),\n  type: z.enum(['TEXT', 'HEADING_1', 'HEADING_2', 'HEADING_3', 'HEADING', 'BULLET_LIST', 'NUMBERED_LIST', 'CODE', 'QUOTE']).optional(),\n  order: z.number().int().min(0).optional(),\n})\n\n// POST /api/notes/[noteId]/blocks - Create a new block\nexport async function POST(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = await params\n    const body = await request.json()\n    const { type, content, order } = createBlockSchema.parse(body)\n\n    // Check if user has access to the note\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        group: {\n          members: {\n            some: { userId: user.id }\n          }\n        }\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Adjust order of existing blocks if necessary\n    await prisma.noteBlock.updateMany({\n      where: {\n        noteId: noteId,\n        order: { gte: order }\n      },\n      data: {\n        order: { increment: 1 }\n      }\n    })\n\n    // Create new block\n    const block = await prisma.noteBlock.create({\n      data: {\n        type,\n        content,\n        order,\n        noteId,\n        authorId: user.id\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Block created successfully',\n      block\n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Create block error:', error)\n    return NextResponse.json(\n      { error: 'Failed to create block' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT /api/notes/[noteId]/blocks - Update multiple blocks (for reordering)\nexport async function PUT(request: NextRequest, { params }: { params: { noteId: string } }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = params\n    const body = await request.json()\n    \n    const updateBlocksSchema = z.object({\n      blocks: z.array(z.object({\n        id: z.string(),\n        order: z.number().int().min(0)\n      }))\n    })\n\n    const { blocks } = updateBlocksSchema.parse(body)\n\n    // Check if user has access to the note\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        group: {\n          members: {\n            some: { userId: user.id }\n          }\n        }\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Update blocks in a transaction\n    await prisma.$transaction(\n      blocks.map(block => \n        prisma.noteBlock.update({\n          where: { id: block.id },\n          data: { order: block.order }\n        })\n      )\n    )\n\n    // Get updated blocks\n    const updatedBlocks = await prisma.noteBlock.findMany({\n      where: { noteId },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        }\n      },\n      orderBy: { order: 'asc' }\n    })\n\n    return NextResponse.json({\n      message: 'Blocks updated successfully',\n      blocks: updatedBlocks\n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Update blocks error:', error)\n    return NextResponse.json(\n      { error: 'Failed to update blocks' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,oBAAoB,oLAAC,CAAC,MAAM,CAAC;IACjC,MAAM,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAa;QAAa;QAAa;QAAW;QAAe;QAAiB;QAAQ;KAAQ;IACxH,SAAS,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM;IAC9B,OAAO,oLAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9B;AAEA,MAAM,oBAAoB,oLAAC,CAAC,MAAM,CAAC;IACjC,SAAS,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,oBAAoB,QAAQ;IAC1D,MAAM,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAa;QAAa;QAAa;QAAW;QAAe;QAAiB;QAAQ;KAAQ,EAAE,QAAQ;IAClI,OAAO,oLAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,QAAQ;AACzC;AAGO,eAAe,KAAK,OAAoB,EAAE,EAAE,MAAM,EAA2C;IAClG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;QACzB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,kBAAkB,KAAK,CAAC;QAEzD,uCAAuC;QACvC,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;gBACJ,OAAO;oBACL,SAAS;wBACP,MAAM;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBAC1B;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,gIAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAChC,OAAO;gBACL,QAAQ;gBACR,OAAO;oBAAE,KAAK;gBAAM;YACtB;YACA,MAAM;gBACJ,OAAO;oBAAE,WAAW;gBAAE;YACxB;QACF;QAEA,mBAAmB;QACnB,MAAM,QAAQ,MAAM,gIAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA,UAAU,KAAK,EAAE;YACnB;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAAkC;IACxF,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,qBAAqB,oLAAC,CAAC,MAAM,CAAC;YAClC,QAAQ,oLAAC,CAAC,KAAK,CAAC,oLAAC,CAAC,MAAM,CAAC;gBACvB,IAAI,oLAAC,CAAC,MAAM;gBACZ,OAAO,oLAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;YAC9B;QACF;QAEA,MAAM,EAAE,MAAM,EAAE,GAAG,mBAAmB,KAAK,CAAC;QAE5C,uCAAuC;QACvC,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;gBACJ,OAAO;oBACL,SAAS;wBACP,MAAM;4BAAE,QAAQ,KAAK,EAAE;wBAAC;oBAC1B;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,gIAAM,CAAC,YAAY,CACvB,OAAO,GAAG,CAAC,CAAA,QACT,gIAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBACtB,OAAO;oBAAE,IAAI,MAAM,EAAE;gBAAC;gBACtB,MAAM;oBAAE,OAAO,MAAM,KAAK;gBAAC;YAC7B;QAIJ,qBAAqB;QACrB,MAAM,gBAAgB,MAAM,gIAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpD,OAAO;gBAAE;YAAO;YAChB,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBAAE,OAAO;YAAM;QAC1B;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}