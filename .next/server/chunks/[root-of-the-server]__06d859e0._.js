module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>n,"requireAuth",()=>a]);var t=e.i(79832),r=e.i(98043);async function n(e){try{let n=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!n)return null;let a=(0,t.verifyToken)(n);if(!a)return null;return await r.prisma.user.findUnique({where:{id:a.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function a(e){return async t=>{let r=await n(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},85590,(e,t,r)=>{},12902,e=>{"use strict";e.s(["handler",()=>I,"patchFetch",()=>T,"routeModule",()=>N,"serverHooks",()=>j,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>k],12902);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var R=e.i(220);e.s(["POST",()=>y,"PUT",()=>A],59611);var f=e.i(89171),v=e.i(98043),E=e.i(24960),g=e.i(69719);let w=g.z.object({type:g.z.enum(["TEXT","HEADING_1","HEADING_2","HEADING_3","HEADING","BULLET_LIST","NUMBERED_LIST","CODE","QUOTE"]),content:g.z.string().max(5e3,"Content too long"),order:g.z.number().int().min(0)});async function y(e,{params:t}){try{let r=await (0,E.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{noteId:n}=await t,a=await e.json(),{type:o,content:s,order:i}=w.parse(a);if(!await v.prisma.note.findFirst({where:{id:n,group:{members:{some:{userId:r.id}}}}}))return f.NextResponse.json({error:"Note not found or access denied"},{status:404});await v.prisma.noteBlock.updateMany({where:{noteId:n,order:{gte:i}},data:{order:{increment:1}}});let l=await v.prisma.noteBlock.create({data:{type:o,content:s,order:i,noteId:n,authorId:r.id},include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}}});return f.NextResponse.json({message:"Block created successfully",block:l})}catch(e){if(e instanceof g.z.ZodError)return f.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Create block error:",e),f.NextResponse.json({error:"Failed to create block"},{status:500})}}async function A(e,{params:t}){try{let r=await (0,E.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{noteId:n}=t,a=await e.json(),{blocks:o}=g.z.object({blocks:g.z.array(g.z.object({id:g.z.string(),order:g.z.number().int().min(0)}))}).parse(a);if(!await v.prisma.note.findFirst({where:{id:n,group:{members:{some:{userId:r.id}}}}}))return f.NextResponse.json({error:"Note not found or access denied"},{status:404});await v.prisma.$transaction(o.map(e=>v.prisma.noteBlock.update({where:{id:e.id},data:{order:e.order}})));let s=await v.prisma.noteBlock.findMany({where:{noteId:n},include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}},orderBy:{order:"asc"}});return f.NextResponse.json({message:"Blocks updated successfully",blocks:s})}catch(e){if(e instanceof g.z.ZodError)return f.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Update blocks error:",e),f.NextResponse.json({error:"Failed to update blocks"},{status:500})}}g.z.object({content:g.z.string().max(5e3,"Content too long").optional(),type:g.z.enum(["TEXT","HEADING_1","HEADING_2","HEADING_3","HEADING","BULLET_LIST","NUMBERED_LIST","CODE","QUOTE"]).optional(),order:g.z.number().int().min(0).optional()});var b=e.i(59611);let N=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/notes/[noteId]/blocks/route",pathname:"/api/notes/[noteId]/blocks",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/notes/[noteId]/blocks/route.ts",nextConfigOutput:"",userland:b}),{workAsyncStorage:C,workUnitAsyncStorage:k,serverHooks:j}=N;function T(){return(0,n.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:k})}async function I(e,t,n){var f;let v="/api/notes/[noteId]/blocks/route";v=v.replace(/\/index$/,"")||"/";let E=await N.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!E)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:g,params:w,nextConfig:y,isDraftMode:A,prerenderManifest:b,routerServerContext:C,isOnDemandRevalidate:k,revalidateOnlyGenerated:j,resolvedPathname:T}=E,I=(0,s.normalizeAppPath)(v),_=!!(b.dynamicRoutes[I]||b.routes[T]);if(_&&!A){let e=!!b.routes[T],t=b.dynamicRoutes[I];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let q=null;!_||N.isDev||A||(q="/index"===(q=T)?"/":q);let U=!0===N.isDev||!_,O=_&&!U,H=e.method||"GET",D=(0,o.getTracer)(),P=D.getActiveScopeSpan(),S={params:w,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:U,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=y.experimental)?void 0:f.cacheLife,isRevalidate:O,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>N.onRequestError(e,t,n,C)},sharedContext:{buildId:g}},M=new i.NodeNextRequest(e),z=new i.NodeNextResponse(t),B=l.NextRequestAdapter.fromNodeNextRequest(M,(0,l.signalFromNodeResponse)(t));try{let s=async r=>N.handle(B,S).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=D.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${H} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${H} ${e.url}`)}),i=async o=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&k&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=S.renderOpts.fetchMetrics;let l=S.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let d=S.renderOpts.collectedTags;if(!_)return await (0,c.sendResponse)(M,z,i,S.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==S.renderOpts.collectedRevalidate&&!(S.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&S.renderOpts.collectedRevalidate,n=void 0===S.renderOpts.collectedExpire||S.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:S.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await N.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:k})},C),t}},m=await N.handleResponse({req:e,nextConfig:y,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:k,revalidateOnlyGenerated:j,responseGenerator:d,waitUntil:n.waitUntil});if(!_)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",k?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&_||f.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,c.sendResponse)(M,z,new Response(m.value.body,{headers:f,status:m.value.status||200})),null};P?await i(P):await D.withPropagatedContext(e.headers,()=>D.trace(d.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},i))}catch(t){if(P||t instanceof m.NoFallbackError||await N.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:k})}),_)throw t;return await (0,c.sendResponse)(M,z,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__06d859e0._.js.map