{"version": 3, "sources": ["turbopack:///[project]/src/lib/middleware.ts", "turbopack:///[project]/src/app/api/notes/[noteId]/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { verifyToken } from './auth'\nimport { prisma } from './prisma'\n\nexport async function getAuthenticatedUser(request: NextRequest) {\n  try {\n    // Get token from cookie or Authorization header\n    const token = request.cookies.get('auth-token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!token) {\n      return null\n    }\n\n    // Verify token\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        name: true,\n        avatar: true,\n        createdAt: true,\n        updatedAt: true,\n      },\n    })\n\n    return user\n  } catch (error) {\n    console.error('Auth middleware error:', error)\n    return null\n  }\n}\n\nexport function requireAuth(handler: (request: NextRequest, user: any) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getAuthenticatedUser(request)\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getAuthenticatedUser } from '@/lib/middleware'\nimport { z } from 'zod'\n\nconst updateNoteSchema = z.object({\n  title: z.string().min(1, 'Note title is required').max(200, 'Title too long').optional(),\n  description: z.string().max(500, 'Description too long').optional(),\n})\n\n// GET /api/notes/[noteId] - Get specific note with blocks\nexport async function GET(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = await params\n\n    // Get note with blocks and check access\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        group: {\n          members: {\n            some: { userId: user.id }\n          }\n        }\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        group: {\n          select: {\n            id: true,\n            name: true,\n          }\n        },\n        blocks: {\n          include: {\n            author: {\n              select: {\n                id: true,\n                username: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          },\n          orderBy: {\n            order: 'asc'\n          }\n        }\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    return NextResponse.json({ note })\n  } catch (error) {\n    console.error('Get note error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch note' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT /api/notes/[noteId] - Update note\nexport async function PUT(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = await params\n    const body = await request.json()\n    const updateData = updateNoteSchema.parse(body)\n\n    // Check if user has access to the note\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        group: {\n          members: {\n            some: { userId: user.id }\n          }\n        }\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Update note\n    const updatedNote = await prisma.note.update({\n      where: { id: noteId },\n      data: updateData,\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        blocks: {\n          include: {\n            author: {\n              select: {\n                id: true,\n                username: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          },\n          orderBy: {\n            order: 'asc'\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Note updated successfully',\n      note: updatedNote\n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Update note error:', error)\n    return NextResponse.json(\n      { error: 'Failed to update note' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE /api/notes/[noteId] - Delete note\nexport async function DELETE(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { noteId } = await params\n\n    // Check if user is the author or has admin/owner role in the group\n    const note = await prisma.note.findFirst({\n      where: {\n        id: noteId,\n        OR: [\n          { authorId: user.id },\n          {\n            group: {\n              OR: [\n                { ownerId: user.id },\n                {\n                  members: {\n                    some: {\n                      userId: user.id,\n                      role: { in: ['OWNER', 'ADMIN'] }\n                    }\n                  }\n                }\n              ]\n            }\n          }\n        ]\n      }\n    })\n\n    if (!note) {\n      return NextResponse.json(\n        { error: 'Note not found or insufficient permissions' },\n        { status: 404 }\n      )\n    }\n\n    await prisma.note.delete({\n      where: { id: noteId }\n    })\n\n    return NextResponse.json({\n      message: 'Note deleted successfully'\n    })\n  } catch (error) {\n    console.error('Delete note error:', error)\n    return NextResponse.json(\n      { error: 'Failed to delete note' },\n      { status: 500 }\n    )\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/notes/[noteId]/route\",\n        pathname: \"/api/notes/[noteId]\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/notes/[noteId]/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/notes/[noteId]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "g8CACA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,eAAe,EAAqB,CAAoB,EAC7D,GAAI,CAEF,IAAM,EAAQ,EAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,OACnC,EAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,UAAW,IAEvE,GAAI,CAAC,EACH,KADU,EACH,KAIT,IAAM,EAAU,CAAA,EAAA,EAAA,WAAW,AAAX,EAAY,GAC5B,GAAI,CAAC,EACH,OADY,AACL,KAiBT,OAba,AAaN,MAbY,EAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CACxC,MAAO,CAAE,GAAI,EAAQ,MAAM,AAAC,EAC5B,OAAQ,CACN,IAAI,EACJ,OAAO,EACP,UAAU,EACV,MAAM,EACN,QAAQ,EACR,WAAW,EACX,WAAW,CACb,CACF,EAGF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,yBAA0B,GACjC,IACT,CACF,CAEO,SAAS,EAAY,CAA+D,EACzF,OAAO,MAAO,IACZ,IAAM,EAAO,MAAM,EAAqB,UAEnC,AAAL,EAUO,EAVH,AAUW,EAAS,AAVb,GACF,IAAI,SACT,KAAK,SAAS,CAAC,CAAE,MAAO,yBAA0B,GAClD,CACE,OAAQ,IACR,QAAS,CAAE,eAAgB,kBAAmB,CAChD,EAKN,CACF,0LEzDA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,yDDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAmB,EAAA,CAAC,CAAC,MAAM,CAAC,CAChC,MAAO,EAAA,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,EAAG,0BAA0B,GAAG,CAAC,IAAK,kBAAkB,QAAQ,GACtF,YAAa,EAAA,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAK,wBAAwB,QAAQ,EACnE,GAGO,eAAe,EAAI,CAAoB,CAAE,QAAE,CAAM,CAA2C,EACjG,GAAI,CACF,IAAM,EAAO,MAAM,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,GACxC,GAAI,CAAC,EACH,IADS,GACF,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,yBAA0B,EAAG,CAAE,OAAQ,GAAI,GAG/E,GAAM,CAAE,QAAM,CAAE,CAAG,MAAM,EAGnB,EAAO,MAAM,EAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CACvC,MAAO,CACL,GAAI,EACJ,MAAO,CACL,QAAS,CACP,KAAM,CAAE,OAAQ,EAAK,EAAG,AAAD,CACzB,CACF,CACF,EACA,QAAS,CACP,OAAQ,CACN,OAAQ,CACN,IAAI,EACJ,UAAU,EACV,MAAM,EACN,QAAQ,CACV,CACF,EACA,MAAO,CACL,OAAQ,CACN,IAAI,EACJ,MAAM,CACR,CACF,EACA,OAAQ,CACN,QAAS,CACP,OAAQ,CACN,OAAQ,CACN,GAAI,GACJ,UAAU,EACV,MAAM,EACN,QAAQ,CACV,CACF,CACF,EACA,QAAS,CACP,MAAO,KACT,CACF,CACF,CACF,GAEA,GAAI,CAAC,EACH,IADS,GACF,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,iCAAkC,EAC3C,CAAE,OAAQ,GAAI,GAIlB,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,MAAE,CAAK,EAClC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kBAAmB,GAC1B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,sBAAuB,EAChC,CAAE,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe,EAAI,CAAoB,CAAE,QAAE,CAAM,CAA2C,EACjG,GAAI,CACF,IAAM,EAAO,MAAM,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,GACxC,GAAI,CAAC,EACH,IADS,GACF,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,yBAA0B,EAAG,CAAE,OAAQ,GAAI,GAG/E,GAAM,QAAE,CAAM,CAAE,CAAG,MAAM,EACnB,EAAO,MAAM,EAAQ,IAAI,GACzB,EAAa,EAAiB,KAAK,CAAC,GAc1C,GAAI,CAXS,AAWR,MAXc,AAWR,EAXQ,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CACvC,MAAO,CACL,GAAI,EACJ,MAAO,CACL,QAAS,CACP,KAAM,CAAE,OAAQ,EAAK,EAAE,AAAC,CAC1B,CACF,CACF,CACF,GAGE,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,iCAAkC,EAC3C,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAc,MAAM,EAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAC3C,MAAO,CAAE,GAAI,CAAO,EACpB,KAAM,EACN,QAAS,CACP,OAAQ,CACN,OAAQ,CACN,IAAI,EACJ,UAAU,EACV,MAAM,EACN,OAAQ,EACV,CACF,EACA,OAAQ,CACN,QAAS,CACP,OAAQ,CACN,OAAQ,CACN,IAAI,EACJ,UAAU,EACV,KAAM,GACN,OAAQ,EACV,CACF,CACF,EACA,QAAS,CACP,MAAO,KACT,CACF,CACF,CACF,GAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,QAAS,4BACT,KAAM,CACR,EACF,CAAE,MAAO,EAAO,CACd,GAAI,aAAiB,EAAA,CAAC,CAAC,QAAQ,CAC7B,CAD+B,MACxB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,mBAAoB,QAAS,EAAM,MAAM,AAAC,EACnD,CAAE,OAAQ,GAAI,GAKlB,OADA,QAAQ,KAAK,CAAC,qBAAsB,GAC7B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,uBAAwB,EACjC,CAAE,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe,EAAO,CAAoB,CAAE,QAAE,CAAM,CAA2C,EACpG,GAAI,CACF,IAAM,EAAO,MAAM,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,GACxC,GAAI,CAAC,EACH,IADS,GACF,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,yBAA0B,EAAG,CAAE,OAAQ,GAAI,GAG/E,GAAM,QAAE,CAAM,CAAE,CAAG,MAAM,EA2BzB,GAAI,CAxBS,AAwBR,MAAM,AAxBQ,EAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CACvC,MAAO,CACL,GAAI,EACJ,GAAI,CACF,CAAE,SAAU,EAAK,EAAE,AAAC,EACpB,CACE,MAAO,CACL,GAAI,CACF,CAAE,QAAS,EAAK,EAAE,AAAC,EACnB,CACE,QAAS,CACP,KAAM,CACJ,OAAQ,EAAK,EAAE,CACf,KAAM,CAAE,GAAI,CAAC,QAAS,QAAS,AAAD,CAChC,CACF,CACF,EACD,AACH,CACF,EACD,AACH,CACF,GAGE,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,4CAA6C,EACtD,CAAE,OAAQ,GAAI,GAQlB,OAJA,MAAM,EAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CACvB,MAAO,CAAE,GAAI,CAAO,CACtB,GAEO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,QAAS,2BACX,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qBAAsB,GAC7B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,uBAAwB,EACjC,CAAE,OAAQ,GAAI,EAElB,CACF,CC1MA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,4BACN,SAAU,sBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,gDAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,4BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,CAAE,kBAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,GAC+B,KAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,CACZ,2BACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,CAC7H,eACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,EAAmB,QAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAI,AAAJ,EAEnC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,CAAG,OAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CACf,AAWG,MAXI,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,EACnB,uBACA,4CACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAmD,AAA1C,GAAJ,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAK,AAAJ,MAAU,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAAQ,AADqC,GAAG,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAQ,AAAT,GAAY,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [2]}