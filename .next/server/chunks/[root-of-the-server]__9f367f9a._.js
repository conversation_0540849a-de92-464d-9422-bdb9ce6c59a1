module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},84400,(e,t,r)=>{},93025,e=>{"use strict";e.s(["handler",()=>j,"patchFetch",()=>b,"routeModule",()=>y,"serverHooks",()=>N,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>A],93025);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),u=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),v=e.i(93695);e.i(52474);var R=e.i(220);e.s(["GET",()=>E],95399);var m=e.i(89171),f=e.i(98043),g=e.i(79832);async function E(e){try{let t=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!t)return m.NextResponse.json({error:"No token provided"},{status:401});let r=(0,g.verifyToken)(t);if(!r)return m.NextResponse.json({error:"Invalid token"},{status:401});let a=await f.prisma.user.findUnique({where:{id:r.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}});if(!a)return m.NextResponse.json({error:"User not found"},{status:404});return m.NextResponse.json({user:a})}catch(e){return console.error("Auth verification error:",e),m.NextResponse.json({error:"Internal server error"},{status:500})}}var w=e.i(95399);let y=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/auth/me/route.ts",nextConfigOutput:"",userland:w}),{workAsyncStorage:C,workUnitAsyncStorage:A,serverHooks:N}=y;function b(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:A})}async function j(e,t,a){var m;let f="/api/auth/me/route";f=f.replace(/\/index$/,"")||"/";let g=await y.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:E,params:w,nextConfig:C,isDraftMode:A,prerenderManifest:N,routerServerContext:b,isOnDemandRevalidate:j,revalidateOnlyGenerated:k,resolvedPathname:q}=g,T=(0,o.normalizeAppPath)(f),P=!!(N.dynamicRoutes[T]||N.routes[q]);if(P&&!A){let e=!!N.routes[q],t=N.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new v.NoFallbackError}let O=null;!P||y.isDev||A||(O="/index"===(O=q)?"/":O);let _=!0===y.isDev||!P,S=P&&!_,U=e.method||"GET",H=(0,s.getTracer)(),I=H.getActiveScopeSpan(),M={params:w,prerenderManifest:N,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(m=C.experimental)?void 0:m.cacheLife,isRevalidate:S,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>y.onRequestError(e,t,a,b)},sharedContext:{buildId:E}},D=new i.NodeNextRequest(e),$=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>y.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=H.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${U} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),i=async s=>{var i,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&j&&k&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=M.renderOpts.collectedTags;if(!P)return await (0,p.sendResponse)(D,$,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);u&&(t[h.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await y.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:j})},b),t}},v=await y.handleResponse({req:e,nextConfig:C,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:N,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:k,responseGenerator:u,waitUntil:a.waitUntil});if(!P)return null;if((null==v||null==(i=v.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==v||null==(l=v.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":v.isMiss?"MISS":v.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,c.fromNodeOutgoingHttpHeaders)(v.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||m.delete(h.NEXT_CACHE_TAGS_HEADER),!v.cacheControl||t.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,x.getCacheControlHeader)(v.cacheControl)),await (0,p.sendResponse)(D,$,new Response(v.value.body,{headers:m,status:v.value.status||200})),null};I?await i(I):await H.withPropagatedContext(e.headers,()=>H.trace(u.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},i))}catch(t){if(I||t instanceof v.NoFallbackError||await y.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:j})}),P)throw t;return await (0,p.sendResponse)(D,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9f367f9a._.js.map