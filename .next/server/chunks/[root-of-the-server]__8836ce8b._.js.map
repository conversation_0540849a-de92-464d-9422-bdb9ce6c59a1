{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: any): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, JWT_SECRET)\n  } catch (error) {\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,aAAa,QAAgB;IACjD,OAAO,8IAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,8IAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAY;IACxC,OAAO,kJAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,kJAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/middleware.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { verifyToken } from './auth'\nimport { prisma } from './prisma'\n\nexport async function getAuthenticatedUser(request: NextRequest) {\n  try {\n    // Get token from cookie or Authorization header\n    const token = request.cookies.get('auth-token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!token) {\n      return null\n    }\n\n    // Verify token\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        name: true,\n        avatar: true,\n        createdAt: true,\n        updatedAt: true,\n      },\n    })\n\n    return user\n  } catch (error) {\n    console.error('Auth middleware error:', error)\n    return null\n  }\n}\n\nexport function requireAuth(handler: (request: NextRequest, user: any) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getAuthenticatedUser(request)\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAEO,eAAe,qBAAqB,OAAoB;IAC7D,IAAI;QACF,gDAAgD;QAChD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAEvE,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,eAAe;QACf,MAAM,UAAU,IAAA,mIAAW,EAAC;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,gBAAgB;QAChB,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,OAA+D;IACzF,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,qBAAqB;QAExC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/app/api/blocks/%5BblockId%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getAuthenticatedUser } from '@/lib/middleware'\nimport { z } from 'zod'\n\nconst updateBlockSchema = z.object({\n  content: z.string().max(5000, 'Content too long').optional(),\n  type: z.enum(['TEXT', 'HEADING', 'BULLET_LIST', 'NUMBERED_LIST', 'CODE', 'QUOTE']).optional(),\n})\n\n// PUT /api/blocks/[blockId] - Update a specific block\nexport async function PUT(request: NextRequest, { params }: { params: Promise<{ blockId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { blockId } = await params\n    const body = await request.json()\n    const updateData = updateBlockSchema.parse(body)\n\n    // Check if user has access to the block\n    const block = await prisma.noteBlock.findFirst({\n      where: {\n        id: blockId,\n        note: {\n          group: {\n            members: {\n              some: { userId: user.id }\n            }\n          }\n        }\n      }\n    })\n\n    if (!block) {\n      return NextResponse.json(\n        { error: 'Block not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Update block\n    const updatedBlock = await prisma.noteBlock.update({\n      where: { id: blockId },\n      data: updateData,\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Block updated successfully',\n      block: updatedBlock\n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Update block error:', error)\n    return NextResponse.json(\n      { error: 'Failed to update block' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE /api/blocks/[blockId] - Delete a specific block\nexport async function DELETE(request: NextRequest, { params }: { params: Promise<{ blockId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { blockId } = await params\n\n    // Check if user has access to the block\n    const block = await prisma.noteBlock.findFirst({\n      where: {\n        id: blockId,\n        note: {\n          group: {\n            members: {\n              some: { userId: user.id }\n            }\n          }\n        }\n      },\n      include: {\n        note: true\n      }\n    })\n\n    if (!block) {\n      return NextResponse.json(\n        { error: 'Block not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Delete block\n    await prisma.noteBlock.delete({\n      where: { id: blockId }\n    })\n\n    // Reorder remaining blocks\n    await prisma.noteBlock.updateMany({\n      where: {\n        noteId: block.noteId,\n        order: { gt: block.order }\n      },\n      data: {\n        order: { decrement: 1 }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Block deleted successfully'\n    })\n  } catch (error) {\n    console.error('Delete block error:', error)\n    return NextResponse.json(\n      { error: 'Failed to delete block' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,oBAAoB,oLAAC,CAAC,MAAM,CAAC;IACjC,SAAS,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,oBAAoB,QAAQ;IAC1D,MAAM,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAW;QAAe;QAAiB;QAAQ;KAAQ,EAAE,QAAQ;AAC7F;AAGO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAA4C;IAClG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAC1B,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,aAAa,kBAAkB,KAAK,CAAC;QAE3C,wCAAwC;QACxC,MAAM,QAAQ,MAAM,gIAAM,CAAC,SAAS,CAAC,SAAS,CAAC;YAC7C,OAAO;gBACL,IAAI;gBACJ,MAAM;oBACJ,OAAO;wBACL,SAAS;4BACP,MAAM;gCAAE,QAAQ,KAAK,EAAE;4BAAC;wBAC1B;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC,OAAO;YACV,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,eAAe,MAAM,gIAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACjD,OAAO;gBAAE,IAAI;YAAQ;YACrB,MAAM;YACN,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB,EAAE,EAAE,MAAM,EAA4C;IACrG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAE1B,wCAAwC;QACxC,MAAM,QAAQ,MAAM,gIAAM,CAAC,SAAS,CAAC,SAAS,CAAC;YAC7C,OAAO;gBACL,IAAI;gBACJ,MAAM;oBACJ,OAAO;wBACL,SAAS;4BACP,MAAM;gCAAE,QAAQ,KAAK,EAAE;4BAAC;wBAC1B;oBACF;gBACF;YACF;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,IAAI,CAAC,OAAO;YACV,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,gIAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE,IAAI;YAAQ;QACvB;QAEA,2BAA2B;QAC3B,MAAM,gIAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAChC,OAAO;gBACL,QAAQ,MAAM,MAAM;gBACpB,OAAO;oBAAE,IAAI,MAAM,KAAK;gBAAC;YAC3B;YACA,MAAM;gBACJ,OAAO;oBAAE,WAAW;gBAAE;YACxB;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}