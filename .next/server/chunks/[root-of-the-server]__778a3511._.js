module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>n,"requireAuth",()=>a]);var t=e.i(79832),r=e.i(98043);async function n(e){try{let n=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!n)return null;let a=(0,t.verifyToken)(n);if(!a)return null;return await r.prisma.user.findUnique({where:{id:a.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function a(e){return async t=>{let r=await n(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},46327,(e,t,r)=>{},75569,e=>{"use strict";e.s(["handler",()=>O,"patchFetch",()=>I,"routeModule",()=>C,"serverHooks",()=>T,"workAsyncStorage",()=>b,"workUnitAsyncStorage",()=>q],75569);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),u=e.i(87718),l=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),m=e.i(10372),h=e.i(93695);e.i(52474);var g=e.i(220);e.s(["DELETE",()=>N,"GET",()=>E,"PUT",()=>A],24669);var R=e.i(89171),f=e.i(98043),v=e.i(24960),w=e.i(69719);let y=w.z.object({name:w.z.string().min(1,"Group name is required").max(100,"Group name too long").optional(),description:w.z.string().max(500,"Description too long").optional(),isPrivate:w.z.boolean().optional()});async function E(e,{params:t}){try{let r=await (0,v.getAuthenticatedUser)(e);if(!r)return R.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:n}=t,a=await f.prisma.group.findFirst({where:{id:n,OR:[{ownerId:r.id},{members:{some:{userId:r.id}}}]},include:{owner:{select:{id:!0,username:!0,name:!0,avatar:!0}},members:{include:{user:{select:{id:!0,username:!0,name:!0,avatar:!0}}},orderBy:{joinedAt:"asc"}},_count:{select:{members:!0,messages:!0,notes:!0}}}});if(!a)return R.NextResponse.json({error:"Group not found or access denied"},{status:404});return R.NextResponse.json({group:a})}catch(e){return console.error("Get group error:",e),R.NextResponse.json({error:"Failed to fetch group"},{status:500})}}async function A(e,{params:t}){try{let r=await (0,v.getAuthenticatedUser)(e);if(!r)return R.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:n}=t,a=await e.json(),s=y.parse(a);if(!await f.prisma.group.findFirst({where:{id:n,OR:[{ownerId:r.id},{members:{some:{userId:r.id,role:{in:["OWNER","ADMIN"]}}}}]}}))return R.NextResponse.json({error:"Group not found or insufficient permissions"},{status:404});let o=await f.prisma.group.update({where:{id:n},data:s,include:{owner:{select:{id:!0,username:!0,name:!0,avatar:!0}},members:{include:{user:{select:{id:!0,username:!0,name:!0,avatar:!0}}}},_count:{select:{members:!0,messages:!0,notes:!0}}}});return R.NextResponse.json({message:"Group updated successfully",group:o})}catch(e){if(e instanceof w.z.ZodError)return R.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Update group error:",e),R.NextResponse.json({error:"Failed to update group"},{status:500})}}async function N(e,{params:t}){try{let r=await (0,v.getAuthenticatedUser)(e);if(!r)return R.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:n}=t;if(!await f.prisma.group.findFirst({where:{id:n,ownerId:r.id}}))return R.NextResponse.json({error:"Group not found or insufficient permissions"},{status:404});return await f.prisma.group.delete({where:{id:n}}),R.NextResponse.json({message:"Group deleted successfully"})}catch(e){return console.error("Delete group error:",e),R.NextResponse.json({error:"Failed to delete group"},{status:500})}}var j=e.i(24669);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/groups/[groupId]/route",pathname:"/api/groups/[groupId]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/groups/[groupId]/route.ts",nextConfigOutput:"",userland:j}),{workAsyncStorage:b,workUnitAsyncStorage:q,serverHooks:T}=C;function I(){return(0,n.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:q})}async function O(e,t,n){var R;let f="/api/groups/[groupId]/route";f=f.replace(/\/index$/,"")||"/";let v=await C.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:A,prerenderManifest:N,routerServerContext:j,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,resolvedPathname:T}=v,I=(0,o.normalizeAppPath)(f),O=!!(N.dynamicRoutes[I]||N.routes[T]);if(O&&!A){let e=!!N.routes[T],t=N.dynamicRoutes[I];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let k=null;!O||C.isDev||A||(k="/index"===(k=T)?"/":k);let P=!0===C.isDev||!O,U=O&&!P,_=e.method||"GET",S=(0,s.getTracer)(),H=S.getActiveScopeSpan(),D={params:y,prerenderManifest:N,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=E.experimental)?void 0:R.cacheLife,isRevalidate:U,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>C.onRequestError(e,t,n,j)},sharedContext:{buildId:w}},M=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),G=u.NextRequestAdapter.fromNodeNextRequest(M,(0,u.signalFromNodeResponse)(t));try{let o=async r=>C.handle(G,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=S.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${_} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),i=async s=>{var i,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&b&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=D.renderOpts.fetchMetrics;let u=D.renderOpts.pendingWaitUntil;u&&n.waitUntil&&(n.waitUntil(u),u=void 0);let l=D.renderOpts.collectedTags;if(!O)return await (0,p.sendResponse)(M,F,i,D.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[m.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,n=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})},j),t}},h=await C.handleResponse({req:e,nextConfig:E,cacheKey:k,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:N,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,responseGenerator:l,waitUntil:n.waitUntil});if(!O)return null;if((null==h||null==(i=h.value)?void 0:i.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(u=h.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&O||R.delete(m.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(M,F,new Response(h.value.body,{headers:R,status:h.value.status||200})),null};H?await i(H):await S.withPropagatedContext(e.headers,()=>S.trace(l.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},i))}catch(t){if(H||t instanceof h.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})}),O)throw t;return await (0,p.sendResponse)(M,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__778a3511._.js.map