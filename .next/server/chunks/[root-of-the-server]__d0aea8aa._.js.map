{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: any): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, JWT_SECRET)\n  } catch (error) {\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,aAAa,QAAgB;IACjD,OAAO,8IAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,8IAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAY;IACxC,OAAO,kJAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,kJAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/middleware.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { verifyToken } from './auth'\nimport { prisma } from './prisma'\n\nexport async function getAuthenticatedUser(request: NextRequest) {\n  try {\n    // Get token from cookie or Authorization header\n    const token = request.cookies.get('auth-token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!token) {\n      return null\n    }\n\n    // Verify token\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        name: true,\n        avatar: true,\n        createdAt: true,\n        updatedAt: true,\n      },\n    })\n\n    return user\n  } catch (error) {\n    console.error('Auth middleware error:', error)\n    return null\n  }\n}\n\nexport function requireAuth(handler: (request: NextRequest, user: any) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getAuthenticatedUser(request)\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAEO,eAAe,qBAAqB,OAAoB;IAC7D,IAAI;QACF,gDAAgD;QAChD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAEvE,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,eAAe;QACf,MAAM,UAAU,IAAA,mIAAW,EAAC;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,gBAAgB;QAChB,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,OAA+D;IACzF,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,qBAAqB;QAExC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/app/api/groups/%5BgroupId%5D/messages/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getAuthenticatedUser } from '@/lib/middleware'\nimport { z } from 'zod'\n\nconst createMessageSchema = z.object({\n  content: z.string().min(1, 'Message content is required').max(2000, 'Message too long'),\n})\n\n// GET /api/groups/[groupId]/messages - Get messages for a group\nexport async function GET(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { groupId } = await params\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '50')\n    const offset = (page - 1) * limit\n\n    // Check if user is member of the group\n    const groupMember = await prisma.groupMember.findFirst({\n      where: {\n        groupId: groupId,\n        userId: user.id\n      }\n    })\n\n    if (!groupMember) {\n      return NextResponse.json(\n        { error: 'Access denied. You are not a member of this group.' },\n        { status: 403 }\n      )\n    }\n\n    // Get messages with pagination\n    const messages = await prisma.message.findMany({\n      where: {\n        groupId: groupId\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      },\n      take: limit,\n      skip: offset\n    })\n\n    // Get total count for pagination\n    const totalMessages = await prisma.message.count({\n      where: {\n        groupId: groupId\n      }\n    })\n\n    const totalPages = Math.ceil(totalMessages / limit)\n    const hasMore = page < totalPages\n\n    return NextResponse.json({\n      messages: messages.reverse(), // Reverse to show oldest first\n      pagination: {\n        page,\n        limit,\n        totalMessages,\n        totalPages,\n        hasMore\n      }\n    })\n  } catch (error) {\n    console.error('Get messages error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch messages' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST /api/groups/[groupId]/messages - Send a message to a group\nexport async function POST(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { groupId } = await params\n    const body = await request.json()\n    const { content } = createMessageSchema.parse(body)\n\n    // Check if user is member of the group\n    const groupMember = await prisma.groupMember.findFirst({\n      where: {\n        groupId: groupId,\n        userId: user.id\n      }\n    })\n\n    if (!groupMember) {\n      return NextResponse.json(\n        { error: 'Access denied. You are not a member of this group.' },\n        { status: 403 }\n      )\n    }\n\n    // Create message\n    const message = await prisma.message.create({\n      data: {\n        content,\n        authorId: user.id,\n        groupId: groupId\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Message sent successfully',\n      data: message\n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Send message error:', error)\n    return NextResponse.json(\n      { error: 'Failed to send message' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,sBAAsB,oLAAC,CAAC,MAAM,CAAC;IACnC,SAAS,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,MAAM;AACtE;AAGO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAA4C;IAClG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAC1B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,uCAAuC;QACvC,MAAM,cAAc,MAAM,gIAAM,CAAC,WAAW,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,SAAS;gBACT,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqD,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,WAAW,MAAM,gIAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,SAAS;YACX;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;YACA,MAAM;YACN,MAAM;QACR;QAEA,iCAAiC;QACjC,MAAM,gBAAgB,MAAM,gIAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC/C,OAAO;gBACL,SAAS;YACX;QACF;QAEA,MAAM,aAAa,KAAK,IAAI,CAAC,gBAAgB;QAC7C,MAAM,UAAU,OAAO;QAEvB,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,UAAU,SAAS,OAAO;YAC1B,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB,EAAE,EAAE,MAAM,EAA4C;IACnG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAC1B,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,GAAG,oBAAoB,KAAK,CAAC;QAE9C,uCAAuC;QACvC,MAAM,cAAc,MAAM,gIAAM,CAAC,WAAW,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,SAAS;gBACT,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqD,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,UAAU,MAAM,gIAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA,UAAU,KAAK,EAAE;gBACjB,SAAS;YACX;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}