module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},9606,(e,t,r)=>{},30450,e=>{"use strict";e.s(["handler",()=>P,"patchFetch",()=>q,"routeModule",()=>A,"serverHooks",()=>N,"workAsyncStorage",()=>b,"workUnitAsyncStorage",()=>j],30450);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var v=e.i(220);e.s(["POST",()=>y],11615);var R=e.i(89171),g=e.i(98043),f=e.i(79832),w=e.i(69719);let E=w.z.object({email:w.z.string().email("Invalid email format"),password:w.z.string().min(1,"Password is required")});async function y(e){try{let t=await e.json(),{email:r,password:a}=E.parse(t),n=await g.prisma.user.findUnique({where:{email:r}});if(!n||!await (0,f.verifyPassword)(a,n.password))return R.NextResponse.json({error:"Invalid email or password"},{status:401});let s=(0,f.generateToken)({userId:n.id,email:n.email,username:n.username}),o={id:n.id,email:n.email,username:n.username,name:n.name,avatar:n.avatar,createdAt:n.createdAt},i=R.NextResponse.json({message:"Login successful",user:o,token:s});return i.cookies.set("auth-token",s,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800}),i}catch(e){if(e instanceof w.z.ZodError)return R.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Login error:",e),R.NextResponse.json({error:"Internal server error"},{status:500})}}var C=e.i(11615);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/auth/login/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:b,workUnitAsyncStorage:j,serverHooks:N}=A;function q(){return(0,a.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:j})}async function P(e,t,a){var R;let g="/api/auth/login/route";g=g.replace(/\/index$/,"")||"/";let f=await A.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:E,nextConfig:y,isDraftMode:C,prerenderManifest:b,routerServerContext:j,isOnDemandRevalidate:N,revalidateOnlyGenerated:q,resolvedPathname:P}=f,k=(0,o.normalizeAppPath)(g),T=!!(b.dynamicRoutes[k]||b.routes[P]);if(T&&!C){let e=!!b.routes[P],t=b.dynamicRoutes[k];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!T||A.isDev||C||(O="/index"===(O=P)?"/":O);let S=!0===A.isDev||!T,_=T&&!S,I=e.method||"GET",H=(0,s.getTracer)(),U=H.getActiveScopeSpan(),M={params:E,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=y.experimental)?void 0:R.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>A.onRequestError(e,t,a,j)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),$=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>A.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=H.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${I} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),i=async s=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!T)return await (0,p.sendResponse)(D,$,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:v.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})},j),t}},m=await A.handleResponse({req:e,nextConfig:y,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:q,responseGenerator:d,waitUntil:a.waitUntil});if(!T)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==v.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&T||R.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(D,$,new Response(m.value.body,{headers:R,status:m.value.status||200})),null};U?await i(U):await H.withPropagatedContext(e.headers,()=>H.trace(d.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},i))}catch(t){if(U||t instanceof m.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})}),T)throw t;return await (0,p.sendResponse)(D,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__11132f81._.js.map