module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>a,"requireAuth",()=>n]);var t=e.i(79832),r=e.i(98043);async function a(e){try{let a=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!a)return null;let n=(0,t.verifyToken)(a);if(!n)return null;return await r.prisma.user.findUnique({where:{id:n.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function n(e){return async t=>{let r=await a(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},22009,(e,t,r)=>{},53105,e=>{"use strict";e.s(["handler",()=>O,"patchFetch",()=>T,"routeModule",()=>b,"serverHooks",()=>q,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>j],53105);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),u=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),m=e.i(10372),h=e.i(93695);e.i(52474);var v=e.i(220);e.s(["GET",()=>E,"POST",()=>A],40705);var g=e.i(89171),R=e.i(98043),f=e.i(24960),w=e.i(69719);let y=w.z.object({name:w.z.string().min(1,"Group name is required").max(100,"Group name too long"),description:w.z.string().max(500,"Description too long").optional(),isPrivate:w.z.boolean().optional().default(!1)}),E=(0,f.requireAuth)(async(e,t)=>{try{let e=await R.prisma.group.findMany({where:{OR:[{ownerId:t.id},{members:{some:{userId:t.id}}}]},include:{owner:{select:{id:!0,username:!0,name:!0,avatar:!0}},members:{include:{user:{select:{id:!0,username:!0,name:!0,avatar:!0}}}},_count:{select:{members:!0,messages:!0,notes:!0}}},orderBy:{updatedAt:"desc"}});return g.NextResponse.json({groups:e})}catch(e){return console.error("Get groups error:",e),g.NextResponse.json({error:"Failed to fetch groups"},{status:500})}}),A=(0,f.requireAuth)(async(e,t)=>{try{let r=await e.json(),{name:a,description:n,isPrivate:s}=y.parse(r),o=await R.prisma.group.create({data:{name:a,description:n,isPrivate:s,ownerId:t.id,members:{create:{userId:t.id,role:"OWNER"}}},include:{owner:{select:{id:!0,username:!0,name:!0,avatar:!0}},members:{include:{user:{select:{id:!0,username:!0,name:!0,avatar:!0}}}},_count:{select:{members:!0,messages:!0,notes:!0}}}});return g.NextResponse.json({message:"Group created successfully",group:o})}catch(e){if(e instanceof w.z.ZodError)return g.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Create group error:",e),g.NextResponse.json({error:"Failed to create group"},{status:500})}});var C=e.i(40705);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/groups/route",pathname:"/api/groups",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/groups/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:N,workUnitAsyncStorage:j,serverHooks:q}=b;function T(){return(0,a.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:j})}async function O(e,t,a){var g;let R="/api/groups/route";R=R.replace(/\/index$/,"")||"/";let f=await b.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:A,prerenderManifest:C,routerServerContext:N,isOnDemandRevalidate:j,revalidateOnlyGenerated:q,resolvedPathname:T}=f,O=(0,o.normalizeAppPath)(R),k=!!(C.dynamicRoutes[O]||C.routes[T]);if(k&&!A){let e=!!C.routes[T],t=C.dynamicRoutes[O];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let P=null;!k||b.isDev||A||(P="/index"===(P=T)?"/":P);let _=!0===b.isDev||!k,S=k&&!_,I=e.method||"GET",U=(0,s.getTracer)(),H=U.getActiveScopeSpan(),M={params:y,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=E.experimental)?void 0:g.cacheLife,isRevalidate:S,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>b.onRequestError(e,t,a,N)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>b.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${I} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),i=async s=>{var i,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&j&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=M.renderOpts.collectedTags;if(!k)return await (0,p.sendResponse)(D,F,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);u&&(t[m.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:v.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:j})},N),t}},h=await b.handleResponse({req:e,nextConfig:E,cacheKey:P,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:q,responseGenerator:u,waitUntil:a.waitUntil});if(!k)return null;if((null==h||null==(i=h.value)?void 0:i.kind)!==v.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&k||g.delete(m.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(D,F,new Response(h.value.body,{headers:g,status:h.value.status||200})),null};H?await i(H):await U.withPropagatedContext(e.headers,()=>U.trace(u.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},i))}catch(t){if(H||t instanceof h.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:O,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:j})}),k)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__cef4bb1d._.js.map