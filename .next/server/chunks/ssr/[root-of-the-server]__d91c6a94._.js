module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},98253,a=>{"use strict";a.s(["default",()=>h],98253);var b=a.i(87924),c=a.i(72131),d=a.i(50944),e=a.i(11642);function f({onSuccess:a,onSwitchToRegister:d}){let[f,g]=(0,c.useState)(""),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(""),[l,m]=(0,c.useState)(!1),{login:n}=(0,e.useAuth)(),o=async b=>{b.preventDefault(),k(""),m(!0);try{await n(f,h),a?.()}catch(a){k(a instanceof Error?a.message:"Login failed")}finally{m(!1)}};return(0,b.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,b.jsxs)("div",{className:"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4",children:[(0,b.jsx)("h2",{className:"text-2xl font-bold text-center text-gray-800 mb-6",children:"Sign In to MyBinder"}),j&&(0,b.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:j}),(0,b.jsxs)("form",{onSubmit:o,children:[(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),(0,b.jsx)("input",{id:"email",type:"email",value:f,onChange:a=>g(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your email",required:!0})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password"}),(0,b.jsx)("input",{id:"password",type:"password",value:h,onChange:a=>i(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your password",required:!0})]}),(0,b.jsx)("div",{className:"flex items-center justify-between",children:(0,b.jsx)("button",{type:"submit",disabled:l,className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed w-full",children:l?"Signing In...":"Sign In"})})]}),d&&(0,b.jsx)("div",{className:"text-center mt-4",children:(0,b.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",(0,b.jsx)("button",{onClick:d,className:"text-blue-500 hover:text-blue-700 font-medium",children:"Sign up here"})]})}),(0,b.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded",children:[(0,b.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Demo Accounts:"}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:"<EMAIL> / demo123"}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:"<EMAIL> / demo123"})]})]})})}function g({onSuccess:a,onSwitchToLogin:d}){let[f,g]=(0,c.useState)(""),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(""),[l,m]=(0,c.useState)(""),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)(!1),{register:r}=(0,e.useAuth)(),s=async b=>{b.preventDefault(),o(""),q(!0);try{await r(f,h,j,l||void 0),a?.()}catch(a){o(a instanceof Error?a.message:"Registration failed")}finally{q(!1)}};return(0,b.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,b.jsxs)("div",{className:"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4",children:[(0,b.jsx)("h2",{className:"text-2xl font-bold text-center text-gray-800 mb-6",children:"Join MyBinder"}),n&&(0,b.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:n}),(0,b.jsxs)("form",{onSubmit:s,children:[(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email *"}),(0,b.jsx)("input",{id:"email",type:"email",value:f,onChange:a=>g(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your email",required:!0})]}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"username",children:"Username *"}),(0,b.jsx)("input",{id:"username",type:"text",value:h,onChange:a=>i(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Choose a username",required:!0})]}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Full Name"}),(0,b.jsx)("input",{id:"name",type:"text",value:l,onChange:a=>m(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your full name (optional)"})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password *"}),(0,b.jsx)("input",{id:"password",type:"password",value:j,onChange:a=>k(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Create a password (min. 6 characters)",required:!0,minLength:6})]}),(0,b.jsx)("div",{className:"flex items-center justify-between",children:(0,b.jsx)("button",{type:"submit",disabled:p,className:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed w-full",children:p?"Creating Account...":"Create Account"})})]}),d&&(0,b.jsx)("div",{className:"text-center mt-4",children:(0,b.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",(0,b.jsx)("button",{onClick:d,className:"text-blue-500 hover:text-blue-700 font-medium",children:"Sign in here"})]})})]})})}function h(){let[a,h]=(0,c.useState)(!0),{user:i,loading:j}=(0,e.useAuth)(),k=(0,d.useRouter)();if((0,c.useEffect)(()=>{!j&&i&&k.push("/dashboard")},[i,j,k]),j)return(0,b.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,b.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});if(i)return null;let l=()=>{k.push("/dashboard")};return(0,b.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,b.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"MyBinder"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Group Chat with Integrated Note-Taking"})]})}),(0,b.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:a?(0,b.jsx)(f,{onSuccess:l,onSwitchToRegister:()=>h(!1)}):(0,b.jsx)(g,{onSuccess:l,onSwitchToLogin:()=>h(!0)})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d91c6a94._.js.map