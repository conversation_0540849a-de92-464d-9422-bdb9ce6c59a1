module.exports=[89578,a=>{a.v({className:"geist_a71539c9-module__T19VSG__className",variable:"geist_a71539c9-module__T19VSG__variable"})},35214,a=>{a.v({className:"geist_mono_8d43a2aa-module__8Li5zG__className",variable:"geist_mono_8d43a2aa-module__8Li5zG__variable"})},25331,a=>{"use strict";a.s(["AuthProvider",()=>c,"useAuth",()=>d]);var b=a.i(11857);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/contexts/AuthContext.tsx <module evaluation>","AuthProvider"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/contexts/AuthContext.tsx <module evaluation>","useAuth")},13002,a=>{"use strict";a.s(["AuthProvider",()=>c,"useAuth",()=>d]);var b=a.i(11857);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/contexts/AuthContext.tsx","AuthProvider"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/contexts/AuthContext.tsx","useAuth")},6416,a=>{"use strict";a.i(25331);var b=a.i(13002);a.n(b)},27572,a=>{"use strict";a.s(["default",()=>i,"metadata",()=>h],27572);var b=a.i(7997),c=a.i(89578);let d={className:c.default.className,style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(35214);let f={className:e.default.className,style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"}};null!=e.default.variable&&(f.variable=e.default.variable);var g=a.i(6416);let h={title:"MyBinder - Group Chat with Note-Taking",description:"A collaborative platform for group communication and note-taking"};function i({children:a}){return(0,b.jsx)("html",{lang:"en",children:(0,b.jsx)("body",{className:`${d.variable} ${f.variable} antialiased`,children:(0,b.jsx)(g.AuthProvider,{children:a})})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__82389b4c._.js.map