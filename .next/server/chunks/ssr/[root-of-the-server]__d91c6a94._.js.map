{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/src/app/auth/page.tsx", "turbopack:///[project]/src/components/auth/LoginForm.tsx", "turbopack:///[project]/src/components/auth/RegisterForm.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginForm from '@/components/auth/LoginForm'\nimport RegisterForm from '@/components/auth/RegisterForm'\n\nexport default function AuthPage() {\n  const [isLogin, setIsLogin] = useState(true)\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && user) {\n      router.push('/dashboard')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (user) {\n    return null // Will redirect to dashboard\n  }\n\n  const handleAuthSuccess = () => {\n    router.push('/dashboard')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">MyBinder</h1>\n          <p className=\"text-gray-600\">Group Chat with Integrated Note-Taking</p>\n        </div>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        {isLogin ? (\n          <LoginForm\n            onSuccess={handleAuthSuccess}\n            onSwitchToRegister={() => setIsLogin(false)}\n          />\n        ) : (\n          <RegisterForm\n            onSuccess={handleAuthSuccess}\n            onSwitchToLogin={() => setIsLogin(true)}\n          />\n        )}\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface LoginFormProps {\n  onSuccess?: () => void\n  onSwitchToRegister?: () => void\n}\n\nexport default function LoginForm({ onSuccess, onSwitchToRegister }: LoginFormProps) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState('')\n  const [loading, setLoading] = useState(false)\n  const { login } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await login(email, password)\n      onSuccess?.()\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Login failed')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4\">\n        <h2 className=\"text-2xl font-bold text-center text-gray-800 mb-6\">\n          Sign In to MyBinder\n        </h2>\n        \n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"email\">\n              Email\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter your email\"\n              required\n            />\n          </div>\n\n          <div className=\"mb-6\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"password\">\n              Password\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter your password\"\n              required\n            />\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed w-full\"\n            >\n              {loading ? 'Signing In...' : 'Sign In'}\n            </button>\n          </div>\n        </form>\n\n        {onSwitchToRegister && (\n          <div className=\"text-center mt-4\">\n            <p className=\"text-gray-600\">\n              Don&apos;t have an account?{' '}\n              <button\n                onClick={onSwitchToRegister}\n                className=\"text-blue-500 hover:text-blue-700 font-medium\"\n              >\n                Sign up here\n              </button>\n            </p>\n          </div>\n        )}\n\n        <div className=\"mt-6 p-4 bg-gray-50 rounded\">\n          <p className=\"text-sm text-gray-600 mb-2\">Demo Accounts:</p>\n          <p className=\"text-xs text-gray-500\"><EMAIL> / demo123</p>\n          <p className=\"text-xs text-gray-500\"><EMAIL> / demo123</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface RegisterFormProps {\n  onSuccess?: () => void\n  onSwitchToLogin?: () => void\n}\n\nexport default function RegisterForm({ onSuccess, onSwitchToLogin }: RegisterFormProps) {\n  const [email, setEmail] = useState('')\n  const [username, setUsername] = useState('')\n  const [password, setPassword] = useState('')\n  const [name, setName] = useState('')\n  const [error, setError] = useState('')\n  const [loading, setLoading] = useState(false)\n  const { register } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await register(email, username, password, name || undefined)\n      onSuccess?.()\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Registration failed')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4\">\n        <h2 className=\"text-2xl font-bold text-center text-gray-800 mb-6\">\n          Join MyBinder\n        </h2>\n        \n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"email\">\n              Email *\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter your email\"\n              required\n            />\n          </div>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"username\">\n              Username *\n            </label>\n            <input\n              id=\"username\"\n              type=\"text\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Choose a username\"\n              required\n            />\n          </div>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"name\">\n              Full Name\n            </label>\n            <input\n              id=\"name\"\n              type=\"text\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter your full name (optional)\"\n            />\n          </div>\n\n          <div className=\"mb-6\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"password\">\n              Password *\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Create a password (min. 6 characters)\"\n              required\n              minLength={6}\n            />\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed w-full\"\n            >\n              {loading ? 'Creating Account...' : 'Create Account'}\n            </button>\n          </div>\n        </form>\n\n        {onSwitchToLogin && (\n          <div className=\"text-center mt-4\">\n            <p className=\"text-gray-600\">\n              Already have an account?{' '}\n              <button\n                onClick={onSwitchToLogin}\n                className=\"text-blue-500 hover:text-blue-700 font-medium\"\n              >\n                Sign in here\n              </button>\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext", "HooksClientContext", "ServerInsertedHtml"], "mappings": "+iBAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACC,gBAAgB,+BCFvCJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACE,kBAAkB,+BCFzCL,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACG,kBAAkB,uECAzC,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OCMe,SAAS,EAAU,CAAE,WAAS,oBAAE,CAAkB,CAAkB,EACjF,GAAM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACnC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,OAAE,CAAK,CAAE,CAAG,CAAA,EAAA,EAAA,OAAA,AAAO,IAEnB,EAAe,MAAO,IAC1B,EAAE,cAAc,GAChB,EAAS,IACT,GAAW,GAEX,GAAI,CACF,MAAM,EAAM,EAAO,GACnB,KACF,CAAE,MAAO,EAAK,CACZ,EAAS,aAAe,MAAQ,EAAI,OAAO,CAAG,eAChD,QAAU,CACR,GAAW,EACb,CACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DAAoD,wBAIjE,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gFACZ,IAIL,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,YACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,iBAAQ,UAG9E,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,UAAU,6HACV,YAAY,mBACZ,QAAQ,CAAA,CAAA,OAIZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,oBAAW,aAGjF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,WACH,KAAK,WACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAY,EAAE,MAAM,CAAC,KAAK,EAC3C,UAAU,6HACV,YAAY,sBACZ,QAAQ,CAAA,CAAA,OAIZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,EACV,UAAU,+KAET,EAAU,gBAAkB,iBAKlC,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,0BAAgB,yBACC,IAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,yDACX,sBAOP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,mBAC1C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,iCACrC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,wCAK/C,CCnGe,SAAS,EAAa,WAAE,CAAS,CAAE,iBAAe,CAAqB,EACpF,GAAM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACnC,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACnC,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC3B,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,UAAE,CAAQ,CAAE,CAAG,CAAA,EAAA,EAAA,OAAA,AAAO,IAEtB,EAAe,MAAO,IAC1B,EAAE,cAAc,GAChB,EAAS,IACT,GAAW,GAEX,GAAI,CACF,MAAM,EAAS,EAAO,EAAU,EAAU,QAAQ,GAClD,KACF,CAAE,MAAO,EAAK,CACZ,EAAS,aAAe,MAAQ,EAAI,OAAO,CAAG,sBAChD,QAAU,CACR,GAAW,EACb,CACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DAAoD,kBAIjE,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gFACZ,IAIL,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,YACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,iBAAQ,YAG9E,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,UAAU,6HACV,YAAY,mBACZ,QAAQ,CAAA,CAAA,OAIZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,oBAAW,eAGjF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,WACH,KAAK,OACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAY,EAAE,MAAM,CAAC,KAAK,EAC3C,UAAU,6HACV,YAAY,oBACZ,QAAQ,CAAA,CAAA,OAIZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,gBAAO,cAG7E,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,OACH,KAAK,OACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAQ,EAAE,MAAM,CAAC,KAAK,EACvC,UAAU,6HACV,YAAY,uCAIhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,oBAAW,eAGjF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,WACH,KAAK,WACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAY,EAAE,MAAM,CAAC,KAAK,EAC3C,UAAU,6HACV,YAAY,wCACZ,QAAQ,CAAA,CAAA,EACR,UAAW,OAIf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,EACV,UAAU,iLAET,EAAU,sBAAwB,wBAKxC,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,0BAAgB,2BACF,IACzB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,yDACX,0BASf,CF/He,SAAS,IACtB,GAAM,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,MAAE,CAAI,SAAE,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,OAAA,AAAO,IAC3B,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAQxB,GANA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACJ,CAAC,GAAW,GACd,EAAO,CADa,GACT,CAAC,aAEhB,EAAG,CAAC,EAAM,EAAS,EAAO,EAEtB,EACF,MACE,CAFS,AAET,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2EACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,oBAM1C,GAAI,EACF,IADQ,GACD,KAAK,AAGd,IAAM,EAAoB,KACxB,EAAO,IAAI,CAAC,WAJ6B,EAK3C,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,iDAAwC,aACtD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,gDAIjC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACZ,EACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,UAAW,EACX,mBAAoB,IAAM,GAAW,KAGvC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,UAAW,EACX,gBAAiB,IAAM,GAAW,SAM9C", "ignoreList": [0, 1, 2]}