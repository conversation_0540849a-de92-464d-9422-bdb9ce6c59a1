{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/shared/lib/i18n/normalize-locale-path.ts", "turbopack:///[project]/node_modules/next/src/lib/constants.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts", "turbopack:///[project]/node_modules/next/src/server/web/utils.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react.ts", "turbopack:///[project]/node_modules/next/dist/esm/client/components/hooks-server-context.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/static-generation-bailout.js", "turbopack:///[project]/node_modules/next/dist/esm/server/lib/patch-fetch.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/lazy-dynamic/bailout-to-csr.js", "turbopack:///[project]/node_modules/next/dist/esm/server/lib/clone-response.js", "turbopack:///[project]/node_modules/next/dist/esm/server/app-render/dynamic-rendering.js", "turbopack:///[project]/node_modules/next/dist/esm/server/dynamic-rendering-utils.js", "turbopack:///[project]/node_modules/next/dist/esm/lib/framework/boundary-constants.js", "turbopack:///[project]/node_modules/next/dist/esm/server/lib/dedupe-fetch.js"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n", "import type { ServerRuntime } from '../types'\n\nexport const TEXT_PLAIN_CONTENT_TYPE_HEADER = 'text/plain'\nexport const HTML_CONTENT_TYPE_HEADER = 'text/html; charset=utf-8'\nexport const JSON_CONTENT_TYPE_HEADER = 'application/json; charset=utf-8'\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS =\n  'private-next-rsc-track-dynamic-import'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n", "/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../lib/constants'\n\n/**\n * Converts a Node.js IncomingHttpHeaders object to a Headers object. Any\n * headers with multiple values will be joined with a comma and space. Any\n * headers that have an undefined value will be ignored and others will be\n * coerced to strings.\n *\n * @param nodeHeaders the headers object to convert\n * @returns the converted headers object\n */\nexport function fromNodeOutgoingHttpHeaders(\n  nodeHeaders: OutgoingHttpHeaders\n): Headers {\n  const headers = new Headers()\n  for (let [key, value] of Object.entries(nodeHeaders)) {\n    const values = Array.isArray(value) ? value : [value]\n    for (let v of values) {\n      if (typeof v === 'undefined') continue\n      if (typeof v === 'number') {\n        v = v.toString()\n      }\n\n      headers.append(key, v)\n    }\n  }\n  return headers\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n  \n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nexport function splitCookiesString(cookiesString: string) {\n  var cookiesStrings = []\n  var pos = 0\n  var start\n  var ch\n  var lastComma\n  var nextStart\n  var cookiesSeparatorFound\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1\n    }\n    return pos < cookiesString.length\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos)\n\n    return ch !== '=' && ch !== ';' && ch !== ','\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos\n    cookiesSeparatorFound = false\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos)\n      if (ch === ',') {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos\n        pos += 1\n\n        skipWhitespace()\n        nextStart = pos\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === '=') {\n          // we found cookies separator\n          cookiesSeparatorFound = true\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart\n          cookiesStrings.push(cookiesString.substring(start, lastComma))\n          start = pos\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1\n        }\n      } else {\n        pos += 1\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length))\n    }\n  }\n\n  return cookiesStrings\n}\n\n/**\n * Converts a Headers object to a Node.js OutgoingHttpHeaders object. This is\n * required to support the set-cookie header, which may have multiple values.\n *\n * @param headers the headers object to convert\n * @returns the converted headers object\n */\nexport function toNodeOutgoingHttpHeaders(\n  headers: Headers\n): OutgoingHttpHeaders {\n  const nodeHeaders: OutgoingHttpHeaders = {}\n  const cookies: string[] = []\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      if (key.toLowerCase() === 'set-cookie') {\n        // We may have gotten a comma joined string of cookies, or multiple\n        // set-cookie headers. We need to merge them into one header array\n        // to represent all the cookies.\n        cookies.push(...splitCookiesString(value))\n        nodeHeaders[key] = cookies.length === 1 ? cookies[0] : cookies\n      } else {\n        nodeHeaders[key] = value\n      }\n    }\n  }\n  return nodeHeaders\n}\n\n/**\n * Validate the correctness of a user-provided URL.\n */\nexport function validateURL(url: string | URL): string {\n  try {\n    return String(new URL(String(url)))\n  } catch (error: any) {\n    throw new Error(\n      `URL is malformed \"${String(\n        url\n      )}\". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,\n      { cause: error }\n    )\n  }\n}\n\n/**\n * Normalizes `nxtP` and `nxtI` query param values to remove the prefix.\n * This function does not mutate the input key.\n */\nexport function normalizeNextQueryParam(key: string): null | string {\n  const prefixes = [NEXT_QUERY_PARAM_PREFIX, NEXT_INTERCEPTION_MARKER_PREFIX]\n  for (const prefix of prefixes) {\n    if (key !== prefix && key.startsWith(prefix)) {\n      return key.substring(prefix.length)\n    }\n  }\n  return null\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.React\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE';\nexport class DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description), this.description = description, this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nexport function isDynamicServerError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err) || typeof err.digest !== 'string') {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\n\n//# sourceMappingURL=hooks-server-context.js.map", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT';\nexport class StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args), this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nexport function isStaticGenBailoutError(error) {\n    if (typeof error !== 'object' || error === null || !('code' in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\n\n//# sourceMappingURL=static-generation-bailout.js.map", "import { AppRenderSpan, NextNodeServerSpan } from './trace/constants';\nimport { getTracer, SpanKind } from './trace/tracer';\nimport { CACHE_ONE_YEAR, INFINITE_CACHE, NEXT_CACHE_TAG_MAX_ITEMS, NEXT_CACHE_TAG_MAX_LENGTH } from '../../lib/constants';\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering';\nimport { makeHangingPromise } from '../dynamic-rendering-utils';\nimport { createDedupeFetch } from './dedupe-fetch';\nimport { getCacheSignal } from '../app-render/work-unit-async-storage.external';\nimport { CachedRouteKind, IncrementalCacheKind } from '../response-cache';\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler';\nimport { cloneResponse } from './clone-response';\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge';\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch');\nfunction isFetchPatched() {\n    return globalThis[NEXT_PATCH_SYMBOL] === true;\n}\nexport function validateRevalidate(revalidateVal, route) {\n    try {\n        let normalizedRevalidate = undefined;\n        if (revalidateVal === false) {\n            normalizedRevalidate = INFINITE_CACHE;\n        } else if (typeof revalidateVal === 'number' && !isNaN(revalidateVal) && revalidateVal > -1) {\n            normalizedRevalidate = revalidateVal;\n        } else if (typeof revalidateVal !== 'undefined') {\n            throw Object.defineProperty(new Error(`Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`), \"__NEXT_ERROR_CODE\", {\n                value: \"E179\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        return normalizedRevalidate;\n    } catch (err) {\n        // handle client component error from attempting to check revalidate value\n        if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n            throw err;\n        }\n        return undefined;\n    }\n}\nexport function validateTags(tags, description) {\n    const validTags = [];\n    const invalidTags = [];\n    for(let i = 0; i < tags.length; i++){\n        const tag = tags[i];\n        if (typeof tag !== 'string') {\n            invalidTags.push({\n                tag,\n                reason: 'invalid type, must be a string'\n            });\n        } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n            invalidTags.push({\n                tag,\n                reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`\n            });\n        } else {\n            validTags.push(tag);\n        }\n        if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n            console.warn(`Warning: exceeded max tag count for ${description}, dropped tags:`, tags.slice(i).join(', '));\n            break;\n        }\n    }\n    if (invalidTags.length > 0) {\n        console.warn(`Warning: invalid tags passed to ${description}: `);\n        for (const { tag, reason } of invalidTags){\n            console.log(`tag: \"${tag}\" ${reason}`);\n        }\n    }\n    return validTags;\n}\nfunction trackFetchMetric(workStore, ctx) {\n    if (!workStore.shouldTrackFetchMetrics) {\n        return;\n    }\n    workStore.fetchMetrics ??= [];\n    workStore.fetchMetrics.push({\n        ...ctx,\n        end: performance.timeOrigin + performance.now(),\n        idx: workStore.nextFetchId || 0\n    });\n}\nasync function createCachedPrerenderResponse(res, cacheKey, incrementalCacheContext, incrementalCache, revalidate, handleUnlock) {\n    // We are prerendering at build time or revalidate time with cacheComponents so we\n    // need to buffer the response so we can guarantee it can be read in a\n    // microtask.\n    const bodyBuffer = await res.arrayBuffer();\n    const fetchedData = {\n        headers: Object.fromEntries(res.headers.entries()),\n        body: Buffer.from(bodyBuffer).toString('base64'),\n        status: res.status,\n        url: res.url\n    };\n    // We can skip setting the serverComponentsHmrCache because we aren't in dev\n    // mode.\n    if (incrementalCacheContext) {\n        await incrementalCache.set(cacheKey, {\n            kind: CachedRouteKind.FETCH,\n            data: fetchedData,\n            revalidate\n        }, incrementalCacheContext);\n    }\n    await handleUnlock();\n    // We return a new Response to the caller.\n    return new Response(bodyBuffer, {\n        headers: res.headers,\n        status: res.status,\n        statusText: res.statusText\n    });\n}\nasync function createCachedDynamicResponse(workStore, res, cacheKey, incrementalCacheContext, incrementalCache, serverComponentsHmrCache, revalidate, input, handleUnlock) {\n    // We're cloning the response using this utility because there exists a bug in\n    // the undici library around response cloning. See the following pull request\n    // for more details: https://github.com/vercel/next.js/pull/73274\n    const [cloned1, cloned2] = cloneResponse(res);\n    // We are dynamically rendering including dev mode. We want to return the\n    // response to the caller as soon as possible because it might stream over a\n    // very long time.\n    const cacheSetPromise = cloned1.arrayBuffer().then(async (arrayBuffer)=>{\n        const bodyBuffer = Buffer.from(arrayBuffer);\n        const fetchedData = {\n            headers: Object.fromEntries(cloned1.headers.entries()),\n            body: bodyBuffer.toString('base64'),\n            status: cloned1.status,\n            url: cloned1.url\n        };\n        serverComponentsHmrCache == null ? void 0 : serverComponentsHmrCache.set(cacheKey, fetchedData);\n        if (incrementalCacheContext) {\n            await incrementalCache.set(cacheKey, {\n                kind: CachedRouteKind.FETCH,\n                data: fetchedData,\n                revalidate\n            }, incrementalCacheContext);\n        }\n    }).catch((error)=>console.warn(`Failed to set fetch cache`, input, error)).finally(handleUnlock);\n    const pendingRevalidateKey = `cache-set-${cacheKey}`;\n    workStore.pendingRevalidates ??= {};\n    if (pendingRevalidateKey in workStore.pendingRevalidates) {\n        // there is already a pending revalidate entry that we need to await to\n        // avoid race conditions\n        await workStore.pendingRevalidates[pendingRevalidateKey];\n    }\n    workStore.pendingRevalidates[pendingRevalidateKey] = cacheSetPromise.finally(()=>{\n        var _workStore_pendingRevalidates;\n        // If the pending revalidate is not present in the store, then we have\n        // nothing to delete.\n        if (!((_workStore_pendingRevalidates = workStore.pendingRevalidates) == null ? void 0 : _workStore_pendingRevalidates[pendingRevalidateKey])) {\n            return;\n        }\n        delete workStore.pendingRevalidates[pendingRevalidateKey];\n    });\n    return cloned2;\n}\nexport function createPatchedFetcher(originFetch, { workAsyncStorage, workUnitAsyncStorage }) {\n    // Create the patched fetch function.\n    const patched = async function fetch(input, init) {\n        var _init_method, _init_next;\n        let url;\n        try {\n            url = new URL(input instanceof Request ? input.url : input);\n            url.username = '';\n            url.password = '';\n        } catch  {\n            // Error caused by malformed URL should be handled by native fetch\n            url = undefined;\n        }\n        const fetchUrl = (url == null ? void 0 : url.href) ?? '';\n        const method = (init == null ? void 0 : (_init_method = init.method) == null ? void 0 : _init_method.toUpperCase()) || 'GET';\n        // Do create a new span trace for internal fetches in the\n        // non-verbose mode.\n        const isInternal = (init == null ? void 0 : (_init_next = init.next) == null ? void 0 : _init_next.internal) === true;\n        const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1';\n        // We don't track fetch metrics for internal fetches\n        // so it's not critical that we have a start time, as it won't be recorded.\n        // This is to workaround a flaky issue where performance APIs might\n        // not be available and will require follow-up investigation.\n        const fetchStart = isInternal ? undefined : performance.timeOrigin + performance.now();\n        const workStore = workAsyncStorage.getStore();\n        const workUnitStore = workUnitAsyncStorage.getStore();\n        // During static generation we track cache reads so we can reason about when they fill\n        let cacheSignal = workUnitStore ? getCacheSignal(workUnitStore) : null;\n        if (cacheSignal) {\n            cacheSignal.beginRead();\n        }\n        const result = getTracer().trace(isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch, {\n            hideSpan,\n            kind: SpanKind.CLIENT,\n            spanName: [\n                'fetch',\n                method,\n                fetchUrl\n            ].filter(Boolean).join(' '),\n            attributes: {\n                'http.url': fetchUrl,\n                'http.method': method,\n                'net.peer.name': url == null ? void 0 : url.hostname,\n                'net.peer.port': (url == null ? void 0 : url.port) || undefined\n            }\n        }, async ()=>{\n            var _getRequestMeta;\n            // If this is an internal fetch, we should not do any special treatment.\n            if (isInternal) {\n                return originFetch(input, init);\n            }\n            // If the workStore is not available, we can't do any\n            // special treatment of fetch, therefore fallback to the original\n            // fetch implementation.\n            if (!workStore) {\n                return originFetch(input, init);\n            }\n            // We should also fallback to the original fetch implementation if we\n            // are in draft mode, it does not constitute a static generation.\n            if (workStore.isDraftMode) {\n                return originFetch(input, init);\n            }\n            const isRequestInput = input && typeof input === 'object' && typeof input.method === 'string';\n            const getRequestMeta = (field)=>{\n                // If request input is present but init is not, retrieve from input first.\n                const value = init == null ? void 0 : init[field];\n                return value || (isRequestInput ? input[field] : null);\n            };\n            let finalRevalidate = undefined;\n            const getNextField = (field)=>{\n                var _init_next, _init_next1, _input_next;\n                return typeof (init == null ? void 0 : (_init_next = init.next) == null ? void 0 : _init_next[field]) !== 'undefined' ? init == null ? void 0 : (_init_next1 = init.next) == null ? void 0 : _init_next1[field] : isRequestInput ? (_input_next = input.next) == null ? void 0 : _input_next[field] : undefined;\n            };\n            // RequestInit doesn't keep extra fields e.g. next so it's\n            // only available if init is used separate\n            const originalFetchRevalidate = getNextField('revalidate');\n            let currentFetchRevalidate = originalFetchRevalidate;\n            const tags = validateTags(getNextField('tags') || [], `fetch ${input.toString()}`);\n            let revalidateStore;\n            if (workUnitStore) {\n                switch(workUnitStore.type){\n                    case 'prerender':\n                    case 'prerender-runtime':\n                    // TODO: Stop accumulating tags in client prerender. (fallthrough)\n                    case 'prerender-client':\n                    case 'prerender-ppr':\n                    case 'prerender-legacy':\n                    case 'cache':\n                    case 'private-cache':\n                        revalidateStore = workUnitStore;\n                        break;\n                    case 'request':\n                    case 'unstable-cache':\n                        break;\n                    default:\n                        workUnitStore;\n                }\n            }\n            if (revalidateStore) {\n                if (Array.isArray(tags)) {\n                    // Collect tags onto parent caches or parent prerenders.\n                    const collectedTags = revalidateStore.tags ?? (revalidateStore.tags = []);\n                    for (const tag of tags){\n                        if (!collectedTags.includes(tag)) {\n                            collectedTags.push(tag);\n                        }\n                    }\n                }\n            }\n            const implicitTags = workUnitStore == null ? void 0 : workUnitStore.implicitTags;\n            let pageFetchCacheMode = workStore.fetchCache;\n            if (workUnitStore) {\n                switch(workUnitStore.type){\n                    case 'unstable-cache':\n                        // Inside unstable-cache we treat it the same as force-no-store on\n                        // the page.\n                        pageFetchCacheMode = 'force-no-store';\n                        break;\n                    case 'prerender':\n                    case 'prerender-client':\n                    case 'prerender-runtime':\n                    case 'prerender-ppr':\n                    case 'prerender-legacy':\n                    case 'request':\n                    case 'cache':\n                    case 'private-cache':\n                        break;\n                    default:\n                        workUnitStore;\n                }\n            }\n            const isUsingNoStore = !!workStore.isUnstableNoStore;\n            let currentFetchCacheConfig = getRequestMeta('cache');\n            let cacheReason = '';\n            let cacheWarning;\n            if (typeof currentFetchCacheConfig === 'string' && typeof currentFetchRevalidate !== 'undefined') {\n                // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n                const isConflictingRevalidate = // revalidate: 0 and cache: force-cache\n                currentFetchCacheConfig === 'force-cache' && currentFetchRevalidate === 0 || // revalidate: >0 or revalidate: false and cache: no-store\n                currentFetchCacheConfig === 'no-store' && (currentFetchRevalidate > 0 || currentFetchRevalidate === false);\n                if (isConflictingRevalidate) {\n                    cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`;\n                    currentFetchCacheConfig = undefined;\n                    currentFetchRevalidate = undefined;\n                }\n            }\n            const hasExplicitFetchCacheOptOut = // fetch config itself signals not to cache\n            currentFetchCacheConfig === 'no-cache' || currentFetchCacheConfig === 'no-store' || // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n            // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n            pageFetchCacheMode === 'force-no-store' || pageFetchCacheMode === 'only-no-store';\n            // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n            // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n            // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n            // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n            // other configurations.\n            const noFetchConfigAndForceDynamic = !pageFetchCacheMode && !currentFetchCacheConfig && !currentFetchRevalidate && workStore.forceDynamic;\n            if (// force-cache was specified without a revalidate value. We set the revalidate value to false\n            // which will signal the cache to not revalidate\n            currentFetchCacheConfig === 'force-cache' && typeof currentFetchRevalidate === 'undefined') {\n                currentFetchRevalidate = false;\n            } else if (hasExplicitFetchCacheOptOut || noFetchConfigAndForceDynamic) {\n                currentFetchRevalidate = 0;\n            }\n            if (currentFetchCacheConfig === 'no-cache' || currentFetchCacheConfig === 'no-store') {\n                cacheReason = `cache: ${currentFetchCacheConfig}`;\n            }\n            finalRevalidate = validateRevalidate(currentFetchRevalidate, workStore.route);\n            const _headers = getRequestMeta('headers');\n            const initHeaders = typeof (_headers == null ? void 0 : _headers.get) === 'function' ? _headers : new Headers(_headers || {});\n            const hasUnCacheableHeader = initHeaders.get('authorization') || initHeaders.get('cookie');\n            const isUnCacheableMethod = ![\n                'get',\n                'head'\n            ].includes(((_getRequestMeta = getRequestMeta('method')) == null ? void 0 : _getRequestMeta.toLowerCase()) || 'get');\n            /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */ const hasNoExplicitCacheConfig = // eslint-disable-next-line eqeqeq\n            pageFetchCacheMode == undefined && // eslint-disable-next-line eqeqeq\n            (currentFetchCacheConfig == undefined || // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') && // eslint-disable-next-line eqeqeq\n            currentFetchRevalidate == undefined;\n            let autoNoCache = Boolean((hasUnCacheableHeader || isUnCacheableMethod) && (revalidateStore == null ? void 0 : revalidateStore.revalidate) === 0);\n            let isImplicitBuildTimeCache = false;\n            if (!autoNoCache && hasNoExplicitCacheConfig) {\n                // We don't enable automatic no-cache behavior during build-time\n                // prerendering so that we can still leverage the fetch cache between\n                // export workers.\n                if (workStore.isBuildTimePrerendering) {\n                    isImplicitBuildTimeCache = true;\n                } else {\n                    autoNoCache = true;\n                }\n            }\n            // If we have no cache config, and we're in Dynamic I/O prerendering,\n            // it'll be a dynamic call. We don't have to issue that dynamic call.\n            if (hasNoExplicitCacheConfig && workUnitStore !== undefined) {\n                switch(workUnitStore.type){\n                    case 'prerender':\n                    case 'prerender-runtime':\n                    // While we don't want to do caching in the client scope we know the\n                    // fetch will be dynamic for cacheComponents so we may as well avoid the\n                    // call here. (fallthrough)\n                    case 'prerender-client':\n                        if (cacheSignal) {\n                            cacheSignal.endRead();\n                            cacheSignal = null;\n                        }\n                        return makeHangingPromise(workUnitStore.renderSignal, workStore.route, 'fetch()');\n                    case 'prerender-ppr':\n                    case 'prerender-legacy':\n                    case 'request':\n                    case 'cache':\n                    case 'private-cache':\n                    case 'unstable-cache':\n                        break;\n                    default:\n                        workUnitStore;\n                }\n            }\n            switch(pageFetchCacheMode){\n                case 'force-no-store':\n                    {\n                        cacheReason = 'fetchCache = force-no-store';\n                        break;\n                    }\n                case 'only-no-store':\n                    {\n                        if (currentFetchCacheConfig === 'force-cache' || typeof finalRevalidate !== 'undefined' && finalRevalidate > 0) {\n                            throw Object.defineProperty(new Error(`cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E448\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheReason = 'fetchCache = only-no-store';\n                        break;\n                    }\n                case 'only-cache':\n                    {\n                        if (currentFetchCacheConfig === 'no-store') {\n                            throw Object.defineProperty(new Error(`cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E521\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        break;\n                    }\n                case 'force-cache':\n                    {\n                        if (typeof currentFetchRevalidate === 'undefined' || currentFetchRevalidate === 0) {\n                            cacheReason = 'fetchCache = force-cache';\n                            finalRevalidate = INFINITE_CACHE;\n                        }\n                        break;\n                    }\n                case 'default-cache':\n                case 'default-no-store':\n                case 'auto':\n                case undefined:\n                    break;\n                default:\n                    pageFetchCacheMode;\n            }\n            if (typeof finalRevalidate === 'undefined') {\n                if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n                    finalRevalidate = INFINITE_CACHE;\n                    cacheReason = 'fetchCache = default-cache';\n                } else if (pageFetchCacheMode === 'default-no-store') {\n                    finalRevalidate = 0;\n                    cacheReason = 'fetchCache = default-no-store';\n                } else if (isUsingNoStore) {\n                    finalRevalidate = 0;\n                    cacheReason = 'noStore call';\n                } else if (autoNoCache) {\n                    finalRevalidate = 0;\n                    cacheReason = 'auto no cache';\n                } else {\n                    // TODO: should we consider this case an invariant?\n                    cacheReason = 'auto cache';\n                    finalRevalidate = revalidateStore ? revalidateStore.revalidate : INFINITE_CACHE;\n                }\n            } else if (!cacheReason) {\n                cacheReason = `revalidate: ${finalRevalidate}`;\n            }\n            if (// when force static is configured we don't bail from\n            // `revalidate: 0` values\n            !(workStore.forceStatic && finalRevalidate === 0) && // we don't consider autoNoCache to switch to dynamic for ISR\n            !autoNoCache && // If the revalidate value isn't currently set or the value is less\n            // than the current revalidate value, we should update the revalidate\n            // value.\n            revalidateStore && finalRevalidate < revalidateStore.revalidate) {\n                // If we were setting the revalidate value to 0, we should try to\n                // postpone instead first.\n                if (finalRevalidate === 0) {\n                    if (workUnitStore) {\n                        switch(workUnitStore.type){\n                            case 'prerender':\n                            case 'prerender-client':\n                            case 'prerender-runtime':\n                                if (cacheSignal) {\n                                    cacheSignal.endRead();\n                                    cacheSignal = null;\n                                }\n                                return makeHangingPromise(workUnitStore.renderSignal, workStore.route, 'fetch()');\n                            case 'prerender-ppr':\n                            case 'prerender-legacy':\n                            case 'request':\n                            case 'cache':\n                            case 'private-cache':\n                            case 'unstable-cache':\n                                break;\n                            default:\n                                workUnitStore;\n                        }\n                    }\n                    markCurrentScopeAsDynamic(workStore, workUnitStore, `revalidate: 0 fetch ${input} ${workStore.route}`);\n                }\n                // We only want to set the revalidate store's revalidate time if it\n                // was explicitly set for the fetch call, i.e.\n                // originalFetchRevalidate.\n                if (revalidateStore && originalFetchRevalidate === finalRevalidate) {\n                    revalidateStore.revalidate = finalRevalidate;\n                }\n            }\n            const isCacheableRevalidate = typeof finalRevalidate === 'number' && finalRevalidate > 0;\n            let cacheKey;\n            const { incrementalCache } = workStore;\n            let isHmrRefresh = false;\n            let serverComponentsHmrCache;\n            if (workUnitStore) {\n                switch(workUnitStore.type){\n                    case 'request':\n                    case 'cache':\n                    case 'private-cache':\n                        isHmrRefresh = workUnitStore.isHmrRefresh ?? false;\n                        serverComponentsHmrCache = workUnitStore.serverComponentsHmrCache;\n                        break;\n                    case 'prerender':\n                    case 'prerender-client':\n                    case 'prerender-runtime':\n                    case 'prerender-ppr':\n                    case 'prerender-legacy':\n                    case 'unstable-cache':\n                        break;\n                    default:\n                        workUnitStore;\n                }\n            }\n            if (incrementalCache && (isCacheableRevalidate || serverComponentsHmrCache)) {\n                try {\n                    cacheKey = await incrementalCache.generateCacheKey(fetchUrl, isRequestInput ? input : init);\n                } catch (err) {\n                    console.error(`Failed to generate cache key for`, input);\n                }\n            }\n            const fetchIdx = workStore.nextFetchId ?? 1;\n            workStore.nextFetchId = fetchIdx + 1;\n            let handleUnlock = ()=>{};\n            const doOriginalFetch = async (isStale, cacheReasonOverride)=>{\n                const requestInputFields = [\n                    'cache',\n                    'credentials',\n                    'headers',\n                    'integrity',\n                    'keepalive',\n                    'method',\n                    'mode',\n                    'redirect',\n                    'referrer',\n                    'referrerPolicy',\n                    'window',\n                    'duplex',\n                    // don't pass through signal when revalidating\n                    ...isStale ? [] : [\n                        'signal'\n                    ]\n                ];\n                if (isRequestInput) {\n                    const reqInput = input;\n                    const reqOptions = {\n                        body: reqInput._ogBody || reqInput.body\n                    };\n                    for (const field of requestInputFields){\n                        // @ts-expect-error custom fields\n                        reqOptions[field] = reqInput[field];\n                    }\n                    input = new Request(reqInput.url, reqOptions);\n                } else if (init) {\n                    const { _ogBody, body, signal, ...otherInput } = init;\n                    init = {\n                        ...otherInput,\n                        body: _ogBody || body,\n                        signal: isStale ? undefined : signal\n                    };\n                }\n                // add metadata to init without editing the original\n                const clonedInit = {\n                    ...init,\n                    next: {\n                        ...init == null ? void 0 : init.next,\n                        fetchType: 'origin',\n                        fetchIdx\n                    }\n                };\n                return originFetch(input, clonedInit).then(async (res)=>{\n                    if (!isStale && fetchStart) {\n                        trackFetchMetric(workStore, {\n                            start: fetchStart,\n                            url: fetchUrl,\n                            cacheReason: cacheReasonOverride || cacheReason,\n                            cacheStatus: finalRevalidate === 0 || cacheReasonOverride ? 'skip' : 'miss',\n                            cacheWarning,\n                            status: res.status,\n                            method: clonedInit.method || 'GET'\n                        });\n                    }\n                    if (res.status === 200 && incrementalCache && cacheKey && (isCacheableRevalidate || serverComponentsHmrCache)) {\n                        const normalizedRevalidate = finalRevalidate >= INFINITE_CACHE ? CACHE_ONE_YEAR : finalRevalidate;\n                        const incrementalCacheConfig = isCacheableRevalidate ? {\n                            fetchCache: true,\n                            fetchUrl,\n                            fetchIdx,\n                            tags,\n                            isImplicitBuildTimeCache\n                        } : undefined;\n                        switch(workUnitStore == null ? void 0 : workUnitStore.type){\n                            case 'prerender':\n                            case 'prerender-client':\n                            case 'prerender-runtime':\n                                return createCachedPrerenderResponse(res, cacheKey, incrementalCacheConfig, incrementalCache, normalizedRevalidate, handleUnlock);\n                            case 'prerender-ppr':\n                            case 'prerender-legacy':\n                            case 'request':\n                            case 'cache':\n                            case 'private-cache':\n                            case 'unstable-cache':\n                            case undefined:\n                                return createCachedDynamicResponse(workStore, res, cacheKey, incrementalCacheConfig, incrementalCache, serverComponentsHmrCache, normalizedRevalidate, input, handleUnlock);\n                            default:\n                                workUnitStore;\n                        }\n                    }\n                    // we had response that we determined shouldn't be cached so we return it\n                    // and don't cache it. This also needs to unlock the cache lock we acquired.\n                    await handleUnlock();\n                    return res;\n                }).catch((error)=>{\n                    handleUnlock();\n                    throw error;\n                });\n            };\n            let cacheReasonOverride;\n            let isForegroundRevalidate = false;\n            let isHmrRefreshCache = false;\n            if (cacheKey && incrementalCache) {\n                let cachedFetchData;\n                if (isHmrRefresh && serverComponentsHmrCache) {\n                    cachedFetchData = serverComponentsHmrCache.get(cacheKey);\n                    isHmrRefreshCache = true;\n                }\n                if (isCacheableRevalidate && !cachedFetchData) {\n                    handleUnlock = await incrementalCache.lock(cacheKey);\n                    const entry = workStore.isOnDemandRevalidate ? null : await incrementalCache.get(cacheKey, {\n                        kind: IncrementalCacheKind.FETCH,\n                        revalidate: finalRevalidate,\n                        fetchUrl,\n                        fetchIdx,\n                        tags,\n                        softTags: implicitTags == null ? void 0 : implicitTags.tags\n                    });\n                    if (hasNoExplicitCacheConfig && workUnitStore) {\n                        switch(workUnitStore.type){\n                            case 'prerender':\n                            case 'prerender-client':\n                            case 'prerender-runtime':\n                                // We sometimes use the cache to dedupe fetches that do not\n                                // specify a cache configuration. In these cases we want to\n                                // make sure we still exclude them from prerenders if\n                                // cacheComponents is on so we introduce an artificial task boundary\n                                // here.\n                                await waitAtLeastOneReactRenderTask();\n                                break;\n                            case 'prerender-ppr':\n                            case 'prerender-legacy':\n                            case 'request':\n                            case 'cache':\n                            case 'private-cache':\n                            case 'unstable-cache':\n                                break;\n                            default:\n                                workUnitStore;\n                        }\n                    }\n                    if (entry) {\n                        await handleUnlock();\n                    } else {\n                        // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n                        cacheReasonOverride = 'cache-control: no-cache (hard refresh)';\n                    }\n                    if ((entry == null ? void 0 : entry.value) && entry.value.kind === CachedRouteKind.FETCH) {\n                        // when stale and is revalidating we wait for fresh data\n                        // so the revalidated entry has the updated data\n                        if (workStore.isRevalidate && entry.isStale) {\n                            isForegroundRevalidate = true;\n                        } else {\n                            if (entry.isStale) {\n                                workStore.pendingRevalidates ??= {};\n                                if (!workStore.pendingRevalidates[cacheKey]) {\n                                    const pendingRevalidate = doOriginalFetch(true).then(async (response)=>({\n                                            body: await response.arrayBuffer(),\n                                            headers: response.headers,\n                                            status: response.status,\n                                            statusText: response.statusText\n                                        })).finally(()=>{\n                                        workStore.pendingRevalidates ??= {};\n                                        delete workStore.pendingRevalidates[cacheKey || ''];\n                                    });\n                                    // Attach the empty catch here so we don't get a \"unhandled\n                                    // promise rejection\" warning.\n                                    pendingRevalidate.catch(console.error);\n                                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate;\n                                }\n                            }\n                            cachedFetchData = entry.value.data;\n                        }\n                    }\n                }\n                if (cachedFetchData) {\n                    if (fetchStart) {\n                        trackFetchMetric(workStore, {\n                            start: fetchStart,\n                            url: fetchUrl,\n                            cacheReason,\n                            cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                            cacheWarning,\n                            status: cachedFetchData.status || 200,\n                            method: (init == null ? void 0 : init.method) || 'GET'\n                        });\n                    }\n                    const response = new Response(Buffer.from(cachedFetchData.body, 'base64'), {\n                        headers: cachedFetchData.headers,\n                        status: cachedFetchData.status\n                    });\n                    Object.defineProperty(response, 'url', {\n                        value: cachedFetchData.url\n                    });\n                    return response;\n                }\n            }\n            if (workStore.isStaticGeneration && init && typeof init === 'object') {\n                const { cache } = init;\n                // Delete `cache` property as Cloudflare Workers will throw an error\n                if (isEdgeRuntime) delete init.cache;\n                if (cache === 'no-store') {\n                    // If enabled, we should bail out of static generation.\n                    if (workUnitStore) {\n                        switch(workUnitStore.type){\n                            case 'prerender':\n                            case 'prerender-client':\n                            case 'prerender-runtime':\n                                if (cacheSignal) {\n                                    cacheSignal.endRead();\n                                    cacheSignal = null;\n                                }\n                                return makeHangingPromise(workUnitStore.renderSignal, workStore.route, 'fetch()');\n                            case 'prerender-ppr':\n                            case 'prerender-legacy':\n                            case 'request':\n                            case 'cache':\n                            case 'private-cache':\n                            case 'unstable-cache':\n                                break;\n                            default:\n                                workUnitStore;\n                        }\n                    }\n                    markCurrentScopeAsDynamic(workStore, workUnitStore, `no-store fetch ${input} ${workStore.route}`);\n                }\n                const hasNextConfig = 'next' in init;\n                const { next = {} } = init;\n                if (typeof next.revalidate === 'number' && revalidateStore && next.revalidate < revalidateStore.revalidate) {\n                    if (next.revalidate === 0) {\n                        // If enabled, we should bail out of static generation.\n                        if (workUnitStore) {\n                            switch(workUnitStore.type){\n                                case 'prerender':\n                                case 'prerender-client':\n                                case 'prerender-runtime':\n                                    return makeHangingPromise(workUnitStore.renderSignal, workStore.route, 'fetch()');\n                                case 'request':\n                                case 'cache':\n                                case 'private-cache':\n                                case 'unstable-cache':\n                                case 'prerender-legacy':\n                                case 'prerender-ppr':\n                                    break;\n                                default:\n                                    workUnitStore;\n                            }\n                        }\n                        markCurrentScopeAsDynamic(workStore, workUnitStore, `revalidate: 0 fetch ${input} ${workStore.route}`);\n                    }\n                    if (!workStore.forceStatic || next.revalidate !== 0) {\n                        revalidateStore.revalidate = next.revalidate;\n                    }\n                }\n                if (hasNextConfig) delete init.next;\n            }\n            // if we are revalidating the whole page via time or on-demand and\n            // the fetch cache entry is stale we should still de-dupe the\n            // origin hit if it's a cache-able entry\n            if (cacheKey && isForegroundRevalidate) {\n                const pendingRevalidateKey = cacheKey;\n                workStore.pendingRevalidates ??= {};\n                let pendingRevalidate = workStore.pendingRevalidates[pendingRevalidateKey];\n                if (pendingRevalidate) {\n                    const revalidatedResult = await pendingRevalidate;\n                    return new Response(revalidatedResult.body, {\n                        headers: revalidatedResult.headers,\n                        status: revalidatedResult.status,\n                        statusText: revalidatedResult.statusText\n                    });\n                }\n                // We used to just resolve the Response and clone it however for\n                // static generation with cacheComponents we need the response to be able to\n                // be resolved in a microtask and cloning the response will never have\n                // a body that can resolve in a microtask in node (as observed through\n                // experimentation) So instead we await the body and then when it is\n                // available we construct manually cloned Response objects with the\n                // body as an ArrayBuffer. This will be resolvable in a microtask\n                // making it compatible with cacheComponents.\n                const pendingResponse = doOriginalFetch(true, cacheReasonOverride)// We're cloning the response using this utility because there\n                // exists a bug in the undici library around response cloning.\n                // See the following pull request for more details:\n                // https://github.com/vercel/next.js/pull/73274\n                .then(cloneResponse);\n                pendingRevalidate = pendingResponse.then(async (responses)=>{\n                    const response = responses[0];\n                    return {\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText\n                    };\n                }).finally(()=>{\n                    var _workStore_pendingRevalidates;\n                    // If the pending revalidate is not present in the store, then\n                    // we have nothing to delete.\n                    if (!((_workStore_pendingRevalidates = workStore.pendingRevalidates) == null ? void 0 : _workStore_pendingRevalidates[pendingRevalidateKey])) {\n                        return;\n                    }\n                    delete workStore.pendingRevalidates[pendingRevalidateKey];\n                });\n                // Attach the empty catch here so we don't get a \"unhandled promise\n                // rejection\" warning\n                pendingRevalidate.catch(()=>{});\n                workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate;\n                return pendingResponse.then((responses)=>responses[1]);\n            } else {\n                return doOriginalFetch(false, cacheReasonOverride);\n            }\n        });\n        if (cacheSignal) {\n            try {\n                return await result;\n            } finally{\n                if (cacheSignal) {\n                    cacheSignal.endRead();\n                }\n            }\n        }\n        return result;\n    };\n    // Attach the necessary properties to the patched fetch function.\n    // We don't use this to determine if the fetch function has been patched,\n    // but for external consumers to determine if the fetch function has been\n    // patched.\n    patched.__nextPatched = true;\n    patched.__nextGetStaticStore = ()=>workAsyncStorage;\n    patched._nextOriginalFetch = originFetch;\n    globalThis[NEXT_PATCH_SYMBOL] = true;\n    // Assign the function name also as a name property, so that it's preserved\n    // even when mangling is enabled.\n    Object.defineProperty(patched, 'name', {\n        value: 'fetch',\n        writable: false\n    });\n    return patched;\n}\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options) {\n    // If we've already patched fetch, we should not patch it again.\n    if (isFetchPatched()) return;\n    // Grab the original fetch function. We'll attach this so we can use it in\n    // the patched fetch function.\n    const original = createDedupeFetch(globalThis.fetch);\n    // Set the global fetch to the patched fetch.\n    globalThis.fetch = createPatchedFetcher(original, options);\n}\n\n//# sourceMappingURL=patch-fetch.js.map", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING';\n/** An error that should be thrown when we want to bail out to client-side rendering. */ export class BailoutToCSRError extends Error {\n    constructor(reason){\n        super(\"Bail out to client-side rendering: \" + reason), this.reason = reason, this.digest = BAILOUT_TO_CSR;\n    }\n}\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */ export function isBailoutToCSRError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err)) {\n        return false;\n    }\n    return err.digest === BAILOUT_TO_CSR;\n}\n\n//# sourceMappingURL=bailout-to-csr.js.map", "const noop = ()=>{};\nlet registry;\nif (globalThis.FinalizationRegistry) {\n    registry = new FinalizationRegistry((weakRef)=>{\n        const stream = weakRef.deref();\n        if (stream && !stream.locked) {\n            stream.cancel('Response object has been garbage collected').then(noop);\n        }\n    });\n}\n/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */ export function cloneResponse(original) {\n    // If the response has no body, then we can just return the original response\n    // twice because it's immutable.\n    if (!original.body) {\n        return [\n            original,\n            original\n        ];\n    }\n    const [body1, body2] = original.body.tee();\n    const cloned1 = new Response(body1, {\n        status: original.status,\n        statusText: original.statusText,\n        headers: original.headers\n    });\n    Object.defineProperty(cloned1, 'url', {\n        value: original.url,\n        // How the original response.url behaves\n        configurable: true,\n        enumerable: true,\n        writable: false\n    });\n    // The Fetch Standard allows users to skip consuming the response body by\n    // relying on garbage collection to release connection resources.\n    // https://github.com/nodejs/undici?tab=readme-ov-file#garbage-collection\n    //\n    // To cancel the stream you then need to cancel both resulting branches.\n    // Teeing a stream will generally lock it for the duration, preventing other\n    // readers from locking it.\n    // https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/tee\n    // cloned2 is stored in a react cache and cloned for subsequent requests.\n    // It is the original request, and is is garbage collected by a\n    // FinalizationRegistry in Undici, but since we're tee-ing the stream\n    // ourselves, we need to cancel clone1's stream (the response returned from\n    // our dedupe fetch) when clone1 is reclaimed, otherwise we leak memory.\n    if (registry && cloned1.body) {\n        registry.register(cloned1, new WeakRef(cloned1.body));\n    }\n    const cloned2 = new Response(body2, {\n        status: original.status,\n        statusText: original.statusText,\n        headers: original.headers\n    });\n    Object.defineProperty(cloned2, 'url', {\n        value: original.url,\n        // How the original response.url behaves\n        configurable: true,\n        enumerable: true,\n        writable: false\n    });\n    return [\n        cloned1,\n        cloned2\n    ];\n}\n\n//# sourceMappingURL=clone-response.js.map", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ // Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react';\nimport { DynamicServerError } from '../../client/components/hooks-server-context';\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout';\nimport { getRuntimeStagePromise, workUnitAsyncStorage } from './work-unit-async-storage.external';\nimport { workAsyncStorage } from '../app-render/work-async-storage.external';\nimport { makeHangingPromise } from '../dynamic-rendering-utils';\nimport { METADATA_BOUNDARY_NAME, VIEWPORT_BOUNDARY_NAME, OUTLET_BOUNDARY_NAME, ROOT_LAYOUT_BOUNDARY_NAME } from '../../lib/framework/boundary-constants';\nimport { scheduleOnNextTick } from '../../lib/scheduler';\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr';\nimport { InvariantError } from '../../shared/lib/invariant-error';\nconst hasPostpone = typeof React.unstable_postpone === 'function';\nexport function createDynamicTrackingState(isDebugDynamicAccesses) {\n    return {\n        isDebugDynamicAccesses,\n        dynamicAccesses: [],\n        syncDynamicErrorWithStack: null\n    };\n}\nexport function createDynamicValidationState() {\n    return {\n        hasSuspenseAboveBody: false,\n        hasDynamicMetadata: false,\n        hasDynamicViewport: false,\n        hasAllowedDynamic: false,\n        dynamicErrors: []\n    };\n}\nexport function getFirstDynamicReason(trackingState) {\n    var _trackingState_dynamicAccesses_;\n    return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */ export function markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'cache':\n            case 'unstable-cache':\n                // Inside cache scopes, marking a scope as dynamic has no effect,\n                // because the outer cache scope creates a cache boundary. This is\n                // subtly different from reading a dynamic data source, which is\n                // forbidden inside a cache scope.\n                return;\n            case 'private-cache':\n                // A private cache scope is already dynamic by definition.\n                return;\n            case 'prerender-legacy':\n            case 'prerender-ppr':\n            case 'request':\n                break;\n            default:\n                workUnitStore;\n        }\n    }\n    // If we're forcing dynamic rendering or we're forcing static rendering, we\n    // don't need to do anything here because the entire page is already dynamic\n    // or it's static and it should not throw or postpone here.\n    if (store.forceDynamic || store.forceStatic) return;\n    if (store.dynamicShouldError) {\n        throw Object.defineProperty(new StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n            value: \"E553\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender-ppr':\n                return postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n            case 'prerender-legacy':\n                workUnitStore.revalidate = 0;\n                // We aren't prerendering, but we are generating a static page. We need\n                // to bail out of static generation.\n                const err = Object.defineProperty(new DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E550\",\n                    enumerable: false,\n                    configurable: true\n                });\n                store.dynamicUsageDescription = expression;\n                store.dynamicUsageStack = err.stack;\n                throw err;\n            case 'request':\n                if (process.env.NODE_ENV !== 'production') {\n                    workUnitStore.usedDynamic = true;\n                }\n                break;\n            default:\n                workUnitStore;\n        }\n    }\n}\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */ export function throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n    // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n    const err = Object.defineProperty(new DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n        value: \"E558\",\n        enumerable: false,\n        configurable: true\n    });\n    prerenderStore.revalidate = 0;\n    store.dynamicUsageDescription = expression;\n    store.dynamicUsageStack = err.stack;\n    throw err;\n}\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */ export function trackDynamicDataInDynamicRender(workUnitStore) {\n    switch(workUnitStore.type){\n        case 'cache':\n        case 'unstable-cache':\n            // Inside cache scopes, marking a scope as dynamic has no effect,\n            // because the outer cache scope creates a cache boundary. This is\n            // subtly different from reading a dynamic data source, which is\n            // forbidden inside a cache scope.\n            return;\n        case 'private-cache':\n            // A private cache scope is already dynamic by definition.\n            return;\n        case 'prerender':\n        case 'prerender-runtime':\n        case 'prerender-legacy':\n        case 'prerender-ppr':\n        case 'prerender-client':\n            break;\n        case 'request':\n            if (process.env.NODE_ENV !== 'production') {\n                workUnitStore.usedDynamic = true;\n            }\n            break;\n        default:\n            workUnitStore;\n    }\n}\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n    const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`;\n    const error = createPrerenderInterruptedError(reason);\n    prerenderStore.controller.abort(error);\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nexport function abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        }\n    }\n}\nexport function trackSynchronousPlatformIOAccessInDev(requestStore) {\n    // We don't actually have a controller to abort but we do the semantic equivalent by\n    // advancing the request store out of prerender mode\n    requestStore.prerenderPhase = false;\n}\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */ export function abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n    const prerenderSignal = prerenderStore.controller.signal;\n    if (prerenderSignal.aborted === false) {\n        // TODO it would be better to move this aborted check into the callsite so we can avoid making\n        // the error object when it isn't relevant to the aborting of the prerender however\n        // since we need the throw semantics regardless of whether we abort it is easier to land\n        // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n        // to ideal implementation\n        abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n        // It is important that we set this tracking value after aborting. Aborts are executed\n        // synchronously except for the case where you abort during render itself. By setting this\n        // value late we can use it to determine if any of the aborted tasks are the task that\n        // called the sync IO expression in the first place.\n        const dynamicTracking = prerenderStore.dynamicTracking;\n        if (dynamicTracking) {\n            if (dynamicTracking.syncDynamicErrorWithStack === null) {\n                dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n            }\n        }\n    }\n    throw createPrerenderInterruptedError(`Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`);\n}\n/**\n * Use this function when dynamically prerendering with dynamicIO.\n * We don't want to error, because it's better to return something\n * (and we've already aborted the render at the point where the sync dynamic error occured),\n * but we should log an error server-side.\n * @internal\n */ export function warnOnSyncDynamicError(dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack) {\n        // the server did something sync dynamic, likely\n        // leading to an early termination of the prerender.\n        console.error(dynamicTracking.syncDynamicErrorWithStack);\n    }\n}\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nexport function Postpone({ reason, route }) {\n    const prerenderStore = workUnitAsyncStorage.getStore();\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n    postponeWithTracking(route, reason, dynamicTracking);\n}\nexport function postponeWithTracking(route, expression, dynamicTracking) {\n    assertPostpone();\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n    React.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n    return `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n}\nexport function isDynamicPostpone(err) {\n    if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n        return isDynamicPostponeReason(err.message);\n    }\n    return false;\n}\nfunction isDynamicPostponeReason(reason) {\n    return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n    throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n        value: \"E296\",\n        enumerable: false,\n        configurable: true\n    });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = NEXT_PRERENDER_INTERRUPTED;\n    return error;\n}\nexport function isPrerenderInterruptedError(error) {\n    return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nexport function accessedDynamicData(dynamicAccesses) {\n    return dynamicAccesses.length > 0;\n}\nexport function consumeDynamicAccess(serverDynamic, clientDynamic) {\n    // We mutate because we only call this once we are no longer writing\n    // to the dynamicTrackingState and it's more efficient than creating a new\n    // array.\n    serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n    return serverDynamic.dynamicAccesses;\n}\nexport function formatDynamicAPIAccesses(dynamicAccesses) {\n    return dynamicAccesses.filter((access)=>typeof access.stack === 'string' && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split('\\n')// Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes('node_modules/next/')) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(' (<anonymous>)')) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(' (node:')) {\n                return false;\n            }\n            return true;\n        }).join('\\n');\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E224\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */ export function createRenderInBrowserAbortSignal() {\n    const controller = new AbortController();\n    controller.abort(Object.defineProperty(new BailoutToCSRError('Render in Browser'), \"__NEXT_ERROR_CODE\", {\n        value: \"E721\",\n        enumerable: false,\n        configurable: true\n    }));\n    return controller.signal;\n}\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */ export function createHangingInputAbortSignal(workUnitStore) {\n    switch(workUnitStore.type){\n        case 'prerender':\n        case 'prerender-runtime':\n            const controller = new AbortController();\n            if (workUnitStore.cacheSignal) {\n                // If we have a cacheSignal it means we're in a prospective render. If\n                // the input we're waiting on is coming from another cache, we do want\n                // to wait for it so that we can resolve this cache entry too.\n                workUnitStore.cacheSignal.inputReady().then(()=>{\n                    controller.abort();\n                });\n            } else {\n                // Otherwise we're in the final render and we should already have all\n                // our caches filled.\n                // If the prerender uses stages, we have wait until the runtime stage,\n                // at which point all runtime inputs will be resolved.\n                // (otherwise, a runtime prerender might consider `cookies()` hanging\n                //  even though they'd resolve in the next task.)\n                //\n                // We might still be waiting on some microtasks so we\n                // wait one tick before giving up. When we give up, we still want to\n                // render the content of this cache as deeply as we can so that we can\n                // suspend as deeply as possible in the tree or not at all if we don't\n                // end up waiting for the input.\n                const runtimeStagePromise = getRuntimeStagePromise(workUnitStore);\n                if (runtimeStagePromise) {\n                    runtimeStagePromise.then(()=>scheduleOnNextTick(()=>controller.abort()));\n                } else {\n                    scheduleOnNextTick(()=>controller.abort());\n                }\n            }\n            return controller.signal;\n        case 'prerender-client':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n        case 'cache':\n        case 'private-cache':\n        case 'unstable-cache':\n            return undefined;\n        default:\n            workUnitStore;\n    }\n}\nexport function annotateDynamicAccess(expression, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nexport function useDynamicRouteParams(expression) {\n    const workStore = workAsyncStorage.getStore();\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workStore && workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender-client':\n            case 'prerender':\n                {\n                    const fallbackParams = workUnitStore.fallbackRouteParams;\n                    if (fallbackParams && fallbackParams.size > 0) {\n                        // We are in a prerender with cacheComponents semantics. We are going to\n                        // hang here and never resolve. This will cause the currently\n                        // rendering component to effectively be a dynamic hole.\n                        React.use(makeHangingPromise(workUnitStore.renderSignal, workStore.route, expression));\n                    }\n                    break;\n                }\n            case 'prerender-ppr':\n                {\n                    const fallbackParams = workUnitStore.fallbackRouteParams;\n                    if (fallbackParams && fallbackParams.size > 0) {\n                        return postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n                    }\n                    break;\n                }\n            case 'prerender-runtime':\n                throw Object.defineProperty(new InvariantError(`\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E771\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'cache':\n            case 'private-cache':\n                throw Object.defineProperty(new InvariantError(`\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E745\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'prerender-legacy':\n            case 'request':\n            case 'unstable-cache':\n                break;\n            default:\n                workUnitStore;\n        }\n    }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags = 'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6';\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(`\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`);\nconst hasMetadataRegex = new RegExp(`\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasViewportRegex = new RegExp(`\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`);\nexport function trackAllowedDynamicAccess(workStore, componentStack, dynamicValidation, clientDynamic) {\n    if (hasOutletRegex.test(componentStack)) {\n        // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n        return;\n    } else if (hasMetadataRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicMetadata = true;\n        return;\n    } else if (hasViewportRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicViewport = true;\n        return;\n    } else if (hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(componentStack)) {\n        // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n        // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n        // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n        dynamicValidation.hasAllowedDynamic = true;\n        dynamicValidation.hasSuspenseAboveBody = true;\n        return;\n    } else if (hasSuspenseRegex.test(componentStack)) {\n        // this error had a Suspense boundary above it so we don't need to report it as a source\n        // of disallowed\n        dynamicValidation.hasAllowedDynamic = true;\n        return;\n    } else if (clientDynamic.syncDynamicErrorWithStack) {\n        // This task was the task that called the sync error.\n        dynamicValidation.dynamicErrors.push(clientDynamic.syncDynamicErrorWithStack);\n        return;\n    } else {\n        const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;\n        const error = createErrorWithComponentOrOwnerStack(message, componentStack);\n        dynamicValidation.dynamicErrors.push(error);\n        return;\n    }\n}\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */ function createErrorWithComponentOrOwnerStack(message, componentStack) {\n    const ownerStack = process.env.NODE_ENV !== 'production' && React.captureOwnerStack ? React.captureOwnerStack() : null;\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.stack = error.name + ': ' + message + (ownerStack ?? componentStack);\n    return error;\n}\nexport var PreludeState = /*#__PURE__*/ function(PreludeState) {\n    PreludeState[PreludeState[\"Full\"] = 0] = \"Full\";\n    PreludeState[PreludeState[\"Empty\"] = 1] = \"Empty\";\n    PreludeState[PreludeState[\"Errored\"] = 2] = \"Errored\";\n    return PreludeState;\n}({});\nexport function logDisallowedDynamicError(workStore, error) {\n    console.error(error);\n    if (!workStore.dev) {\n        if (workStore.hasReadableErrorStacks) {\n            console.error(`To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`);\n        } else {\n            console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`);\n        }\n    }\n}\nexport function throwIfDisallowedDynamic(workStore, prelude, dynamicValidation, serverDynamic) {\n    if (prelude !== 0) {\n        if (dynamicValidation.hasSuspenseAboveBody) {\n            // This route has opted into allowing fully dynamic rendering\n            // by including a Suspense boundary above the body. In this case\n            // a lack of a shell is not considered disallowed so we simply return\n            return;\n        }\n        if (serverDynamic.syncDynamicErrorWithStack) {\n            // There is no shell and the server did something sync dynamic likely\n            // leading to an early termination of the prerender before the shell\n            // could be completed. We terminate the build/validating render.\n            logDisallowedDynamicError(workStore, serverDynamic.syncDynamicErrorWithStack);\n            throw new StaticGenBailoutError();\n        }\n        // We didn't have any sync bailouts but there may be user code which\n        // blocked the root. We would have captured these during the prerender\n        // and can log them here and then terminate the build/validating render\n        const dynamicErrors = dynamicValidation.dynamicErrors;\n        if (dynamicErrors.length > 0) {\n            for(let i = 0; i < dynamicErrors.length; i++){\n                logDisallowedDynamicError(workStore, dynamicErrors[i]);\n            }\n            throw new StaticGenBailoutError();\n        }\n        // If we got this far then the only other thing that could be blocking\n        // the root is dynamic Viewport. If this is dynamic then\n        // you need to opt into that by adding a Suspense boundary above the body\n        // to indicate your are ok with fully dynamic rendering.\n        if (dynamicValidation.hasDynamicViewport) {\n            console.error(`Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`);\n            throw new StaticGenBailoutError();\n        }\n        if (prelude === 1) {\n            // If we ever get this far then we messed up the tracking of invalid dynamic.\n            // We still adhere to the constraint that you must produce a shell but invite the\n            // user to report this as a bug in Next.js.\n            console.error(`Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`);\n            throw new StaticGenBailoutError();\n        }\n    } else {\n        if (dynamicValidation.hasAllowedDynamic === false && dynamicValidation.hasDynamicMetadata) {\n            console.error(`Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`);\n            throw new StaticGenBailoutError();\n        }\n    }\n}\nexport function delayUntilRuntimeStage(prerenderStore, result) {\n    if (prerenderStore.runtimeStagePromise) {\n        return prerenderStore.runtimeStagePromise.then(()=>result);\n    }\n    return result;\n}\n\n//# sourceMappingURL=dynamic-rendering.js.map", "export function isHangingPromiseRejectionError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err)) {\n        return false;\n    }\n    return err.digest === HANGING_PROMISE_REJECTION;\n}\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION';\nclass HangingPromiseRejectionError extends Error {\n    constructor(route, expression){\n        super(`During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route \"${route}\".`), this.route = route, this.expression = expression, this.digest = HANGING_PROMISE_REJECTION;\n    }\n}\nconst abortListenersBySignal = new WeakMap();\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for cacheComponents where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */ export function makeHangingPromise(signal, route, expression) {\n    if (signal.aborted) {\n        return Promise.reject(new HangingPromiseRejectionError(route, expression));\n    } else {\n        const hangingPromise = new Promise((_, reject)=>{\n            const boundRejection = reject.bind(null, new HangingPromiseRejectionError(route, expression));\n            let currentListeners = abortListenersBySignal.get(signal);\n            if (currentListeners) {\n                currentListeners.push(boundRejection);\n            } else {\n                const listeners = [\n                    boundRejection\n                ];\n                abortListenersBySignal.set(signal, listeners);\n                signal.addEventListener('abort', ()=>{\n                    for(let i = 0; i < listeners.length; i++){\n                        listeners[i]();\n                    }\n                }, {\n                    once: true\n                });\n            }\n        });\n        // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n        // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n        // your own promise out of it you'll need to ensure you handle the error when it rejects.\n        hangingPromise.catch(ignoreReject);\n        return hangingPromise;\n    }\n}\nfunction ignoreReject() {}\nexport function makeDevtoolsIOAwarePromise(underlying) {\n    // in React DevTools if we resolve in a setTimeout we will observe\n    // the promise resolution as something that can suspend a boundary or root.\n    return new Promise((resolve)=>{\n        // Must use setTimeout to be considered IO React DevTools. setImmediate will not work.\n        setTimeout(()=>{\n            resolve(underlying);\n        }, 0);\n    });\n}\n\n//# sourceMappingURL=dynamic-rendering-utils.js.map", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__';\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__';\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__';\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__';\n\n//# sourceMappingURL=boundary-constants.js.map", "/**\n * Based on https://github.com/facebook/react/blob/d4e78c42a94be027b4dc7ed2659a5fddfbf9bd4e/packages/react/src/ReactFetch.js\n */ import * as React from 'react';\nimport { cloneResponse } from './clone-response';\nimport { InvariantError } from '../../shared/lib/invariant-error';\nconst simpleCacheKey = '[\"GET\",[],null,\"follow\",null,null,null,null]' // generateCacheKey(new Request('https://blank'));\n;\nfunction generateCacheKey(request) {\n    // We pick the fields that goes into the key used to dedupe requests.\n    // We don't include the `cache` field, because we end up using whatever\n    // caching resulted from the first request.\n    // Notably we currently don't consider non-standard (or future) options.\n    // This might not be safe. TODO: warn for non-standard extensions differing.\n    // IF YOU CHANGE THIS UPDATE THE simpleCacheKey ABOVE.\n    return JSON.stringify([\n        request.method,\n        Array.from(request.headers.entries()),\n        request.mode,\n        request.redirect,\n        request.credentials,\n        request.referrer,\n        request.referrerPolicy,\n        request.integrity\n    ]);\n}\nexport function createDedupeFetch(originalFetch) {\n    const getCacheEntries = React.cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- url is the cache key\n    (url)=>[]);\n    return function dedupeFetch(resource, options) {\n        if (options && options.signal) {\n            // If we're passed a signal, then we assume that\n            // someone else controls the lifetime of this object and opts out of\n            // caching. It's effectively the opt-out mechanism.\n            // Ideally we should be able to check this on the Request but\n            // it always gets initialized with its own signal so we don't\n            // know if it's supposed to override - unless we also override the\n            // Request constructor.\n            return originalFetch(resource, options);\n        }\n        // Normalize the Request\n        let url;\n        let cacheKey;\n        if (typeof resource === 'string' && !options) {\n            // Fast path.\n            cacheKey = simpleCacheKey;\n            url = resource;\n        } else {\n            // Normalize the request.\n            // if resource is not a string or a URL (its an instance of Request)\n            // then do not instantiate a new Request but instead\n            // reuse the request as to not disturb the body in the event it's a ReadableStream.\n            const request = typeof resource === 'string' || resource instanceof URL ? new Request(resource, options) : resource;\n            if (request.method !== 'GET' && request.method !== 'HEAD' || request.keepalive) {\n                // We currently don't dedupe requests that might have side-effects. Those\n                // have to be explicitly cached. We assume that the request doesn't have a\n                // body if it's GET or HEAD.\n                // keepalive gets treated the same as if you passed a custom cache signal.\n                return originalFetch(resource, options);\n            }\n            cacheKey = generateCacheKey(request);\n            url = request.url;\n        }\n        const cacheEntries = getCacheEntries(url);\n        for(let i = 0, j = cacheEntries.length; i < j; i += 1){\n            const [key, promise] = cacheEntries[i];\n            if (key === cacheKey) {\n                return promise.then(()=>{\n                    const response = cacheEntries[i][2];\n                    if (!response) throw Object.defineProperty(new InvariantError('No cached response'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E579\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    // We're cloning the response using this utility because there exists\n                    // a bug in the undici library around response cloning. See the\n                    // following pull request for more details:\n                    // https://github.com/vercel/next.js/pull/73274\n                    const [cloned1, cloned2] = cloneResponse(response);\n                    cacheEntries[i][2] = cloned2;\n                    return cloned1;\n                });\n            }\n        }\n        // We pass the original arguments here in case normalizing the Request\n        // doesn't include all the options in this environment.\n        const promise = originalFetch(resource, options);\n        const entry = [\n            cacheKey,\n            promise,\n            null\n        ];\n        cacheEntries.push(entry);\n        return promise.then((response)=>{\n            // We're cloning the response using this utility because there exists\n            // a bug in the undici library around response cloning. See the\n            // following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            const [cloned1, cloned2] = cloneResponse(response);\n            entry[2] = cloned2;\n            return cloned1;\n        });\n    };\n}\n\n//# sourceMappingURL=dedupe-fetch.js.map"], "names": ["normalizeLocalePath", "cache", "WeakMap", "pathname", "locales", "lowercasedLocales", "get", "map", "locale", "toLowerCase", "set", "detectedLocale", "segments", "split", "segment", "index", "indexOf", "slice", "length", "ACTION_SUFFIX", "APP_DIR_ALIAS", "CACHE_ONE_YEAR", "DOT_NEXT_ALIAS", "ESLINT_DEFAULT_DIRS", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "HTML_CONTENT_TYPE_HEADER", "INFINITE_CACHE", "INSTRUMENTATION_HOOK_FILENAME", "JSON_CONTENT_TYPE_HEADER", "MATCHED_PATH_HEADER", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "NEXT_BODY_SUFFIX", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_DATA_SUFFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_META_SUFFIX", "NEXT_QUERY_PARAM_PREFIX", "NEXT_RESUME_HEADER", "NON_STANDARD_NODE_ENV", "PAGES_DIR_ALIAS", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "ROOT_DIR_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SERVER_RUNTIME", "SSG_FALLBACK_EXPORT_ERROR", "SSG_GET_INITIAL_PROPS_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "TEXT_PLAIN_CONTENT_TYPE_HEADER", "UNSTABLE_REVALIDATE_RENAME_ERROR", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta", "removeTrailingSlash", "route", "replace", "fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "splitCookiesString", "toNodeOutgoingHttpHeaders", "validateURL", "nodeHeaders", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "toString", "append", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "cookies", "url", "String", "URL", "error", "Error", "cause", "prefixes", "prefix", "startsWith", "module", "exports", "require", "vendored", "React"], "mappings": "qHAqBgBA,sBAAAA,qCAAAA,KAXhB,IAAMC,EAAQ,IAAIC,QAWX,SAASF,EACdG,CAAgB,CAChBC,CAA2B,MAYvBO,EATJ,GAAI,CAACP,EAAS,MAAO,UAAED,CAAS,EAGhC,IAAIE,EAAoBJ,EAAMK,GAAG,CAACF,GAC7BC,IACHA,EAAoBD,EAAQG,GAAG,CAAC,AAACC,GAAWA,EAAOC,EAD7B,SACwC,IAC9DR,EAAMS,GAAG,CAACN,EAASC,IAOrB,IAAMO,EAAWT,EAASU,KAAK,CAAC,IAAK,GAIrC,GAAI,CAACD,CAAQ,CAAC,EAAE,CAAE,MAAO,UAAET,CAAS,EAGpC,IAAMW,EAAUF,CAAQ,CAAC,EAAE,CAACH,WAAW,GAIjCM,EAAQV,EAAkBW,OAAO,CAACF,UACxC,AAAIC,EAAQ,EAAU,CAAP,AAASZ,UAAS,GAGjCQ,EAAiBP,CAAO,CAACW,EAAM,CAKxB,CAAEZ,SAFTA,EAAWA,EAASc,KAAK,CAACN,EAAeO,MAAM,CAAG,IAAM,IAErCP,gBAAe,EACpC,uKC3CaQ,aAAa,CAAA,kBAAbA,GAuCAC,aAAa,CAAA,kBAAbA,GAnBAC,cAAc,CAAA,kBAAdA,GAiBAC,cAAc,CAAA,kBAAdA,GAwCAC,mBAAmB,CAAA,kBAAnBA,IAfAC,qBAAqB,CAAA,kBAArBA,GASAC,2BAA2B,CAAA,kBAA3BA,GAPAC,sBAAsB,CAAA,kBAAtBA,GA9EAC,wBAAwB,CAAA,kBAAxBA,GAuCAC,cAAc,CAAA,kBAAdA,GAOAC,6BAA6B,CAAA,kBAA7BA,GA7CAC,wBAAwB,CAAA,kBAAxBA,GAIAC,mBAAmB,CAAA,kBAAnBA,GAqCAC,mBAAmB,CAAA,kBAAnBA,GACAC,0BAA0B,CAAA,kBAA1BA,GA1BAC,gBAAgB,CAAA,kBAAhBA,GAcAC,0BAA0B,CAAA,kBAA1BA,GAXAC,kCAAkC,CAAA,kBAAlCA,GACAC,sCAAsC,CAAA,kBAAtCA,GASAC,8BAA8B,CAAA,kBAA9BA,GAXAC,sBAAsB,CAAA,kBAAtBA,GASAC,wBAAwB,CAAA,kBAAxBA,GACAC,yBAAyB,CAAA,kBAAzBA,GAdAC,gBAAgB,CAAA,kBAAhBA,GAZAC,+BAA+B,CAAA,kBAA/BA,GAaAC,gBAAgB,CAAA,kBAAhBA,GAdAC,uBAAuB,CAAA,kBAAvBA,GAsBAC,kBAAkB,CAAA,kBAAlBA,GA+DAC,qBAAqB,CAAA,kBAArBA,GArCAC,eAAe,CAAA,kBAAfA,GA5CAC,2BAA2B,CAAA,kBAA3BA,GACAC,0CAA0C,CAAA,kBAA1CA,GAyDAC,8BAA8B,CAAA,kBAA9BA,GAZAC,cAAc,CAAA,kBAAdA,GASAC,+BAA+B,CAAA,kBAA/BA,GADAC,2BAA2B,CAAA,kBAA3BA,GAJAC,sBAAsB,CAAA,kBAAtBA,GADAC,yBAAyB,CAAA,kBAAzBA,GAEAC,uBAAuB,CAAA,kBAAvBA,GACAC,gCAAgC,CAAA,kBAAhCA,GAJAC,uBAAuB,CAAA,kBAAvBA,GA5CAC,mBAAmB,CAAA,kBAAnBA,GACAC,uBAAuB,CAAA,kBAAvBA,GACAC,kBAAkB,CAAA,kBAAlBA,GACAC,UAAU,CAAA,kBAAVA,GA6DAC,yBAAyB,CAAA,kBAAzBA,GANAC,oCAAoC,CAAA,kBAApCA,GAEAC,yBAAyB,CAAA,kBAAzBA,GAuBAC,cAAc,CAAA,kBAAdA,IAJAC,yBAAyB,CAAA,kBAAzBA,IAvBAC,8BAA8B,CAAA,kBAA9BA,GAMAC,0CAA0C,CAAA,kBAA1CA,GAzEAC,8BAA8B,CAAA,kBAA9BA,GAkFAC,gCAAgC,CAAA,kBAAhCA,GAiIJC,cAAc,CAAA,kBAAdA,IAAgBC,wBAAwB,CAAA,kBAAxBA,MAnNlB,IAAMH,EAAiC,aACjC5C,EAA2B,2BAC3BG,EAA2B,kCAC3Be,EAA0B,OAC1BF,EAAkC,OAElCZ,EAAsB,iBACtBkB,EAA8B,yBAC9BC,EACX,sCAEWU,EAAsB,gBACtBC,EAA0B,YAC1BC,EAAqB,eACrBC,EAAa,OACb5C,EAAgB,UAChBuB,EAAmB,QACnBE,EAAmB,QACnBV,EAAmB,QAEnBK,EAAyB,oBACzBH,EAAqC,0BACrCC,EACX,8BAEWS,EAAqB,cAIrBN,EAA2B,IAC3BC,EAA4B,IAC5BH,EAAiC,KACjCH,EAA6B,QAG7Bd,EAAiB,QAKjBO,EAAiB,WAGjBI,EAAsB,aACtBC,EAA6B,CAAC,SAAS,EAAED,EAAAA,CAAqB,CAG9DH,EAAgC,kBAIhCmB,EAAkB,qBAClB1B,EAAiB,mBACjB8B,EAAiB,wBACjBhC,EAAgB,uBAChBuC,EAA0B,iCAC1BH,EAA4B,mCAC5BD,EAAyB,oCACzBE,EAA0B,iCAC1BC,EACX,wCACWJ,EAA8B,qCAC9BD,EACX,yCAEWF,EAAiC,CAAC,6KAA6K,CAAC,CAEhNkB,EAAiC,CAAC,mGAAmG,CAAC,CAEtIJ,EAAuC,CAAC,uFAAuF,CAAC,CAEhIC,EAA4B,CAAC,sHAAsH,CAAC,CAEpJI,EAA6C,CAAC,uGAAuG,CAAC,CAEtJN,EAA4B,CAAC,uHAAuH,AAEpJxC,CAFqJ,CAGhK,6FACWE,EACX,iGAEW8C,EACX,uEACA,8BAEW/C,EAA8B,CAAC,wJAAwJ,CAAC,CAExLsB,EAAwB,CAAC,iNAAiN,CAAC,CAE3OqB,GAA4B,CAAC,sJAE7B7C,EAFqL,CAAC,AAEhK,CAAC,MAAO,QAAS,aAAc,MAAO,MAAM,CAElE4C,GAAgD,CAC3DQ,KAAM,OACNC,iBAAkB,oBAClBC,OAAQ,QACV,EAMMC,GAAuB,CAI3BC,OAAQ,SAKRC,sBAAuB,MAIvBC,oBAAqB,MAIrBC,cAAe,iBAIfC,QAAS,WAITC,QAAS,WAITC,WAAY,aAIZC,WAAY,aAIZC,UAAW,aAIXC,gBAAiB,oBAIjBC,gBAAiB,oBAIjBC,aAAc,iBAIdC,aAAc,gBAChB,EAKMlB,GAAiB,CACrB,GAAGK,EAAoB,CACvBc,MAAO,CACLC,aAAc,CACZf,GAAqBE,qBAAqB,CAC1CF,GAAqBI,aAAa,CACnC,CACDY,WAAY,CACVhB,GAAqBE,qBAAqB,CAC1CF,GAAqBI,aAAa,CAClCJ,GAAqBQ,UAAU,CAC/BR,GAAqBO,UAAU,CAChC,CACDU,cAAe,CAEbjB,GAAqBK,OAAO,CAC5BL,GAAqBM,OAAO,CAC7B,CACDY,WAAY,CACVlB,GAAqBG,mBAAmB,CACxCH,GAAqBU,eAAe,CACrC,CACDS,QAAS,CACPnB,GAAqBE,qBAAqB,CAC1CF,GAAqBI,aAAa,CAClCJ,GAAqBG,mBAAmB,CACxCH,GAAqBU,eAAe,CACpCV,GAAqBC,MAAM,CAC3BD,GAAqBQ,UAAU,CAC/BR,GAAqBO,UAAU,CAChC,CACDa,SAAU,CAERpB,GAAqBE,qBAAqB,CAC1CF,GAAqBG,mBAAmB,CACxCH,GAAqBU,eAAe,CACpCV,GAAqBI,aAAa,CACnC,AACH,CACF,EAEMR,GAA2B,CAC/ByB,aAAc,0BACdC,SAAU,oBACVC,cAAe,0BACfC,kBAAmB,8BACrB,gCC5MO,SAASC,EAAoBC,CAAa,EAC/C,OAAOA,EAAMC,OAAO,CAAC,MAAO,KAAO,GACrC,CAHC,OAAA,cAAA,CAAA,EAAA,aAAA,oCACeF,sBAAAA,qCAAAA,0KCQAG,2BAA2B,CAAA,kBAA3BA,GA8IAC,uBAAuB,CAAA,kBAAvBA,GAlHAC,kBAAkB,CAAA,kBAAlBA,GAyEAC,yBAAyB,CAAA,kBAAzBA,GAwBAC,WAAW,CAAA,kBAAXA,aAxIT,CAAA,CAAA,IAAA,GAWA,SAASJ,EACdK,CAAgC,EAEhC,IAAMC,EAAU,IAAIC,QACpB,IAAK,GAAI,CAACC,EAAKC,EAAM,GAAIC,OAAOC,OAAO,CAACN,GAEtC,IAAK,IAAIU,EAF2C,GACrCF,AACDD,MADOE,CACC,MADM,CAACL,GAASA,EAAQ,CAACA,EAAM,MAElC,IAANM,IACM,KADa,KACH,AAAvB,OAAOA,IACTA,EAAIA,EAAEC,QAAQ,EAAA,EAGhBV,EAAQW,MAAM,CAACT,EAAKO,IAGxB,OAAOT,CACT,CAYO,SAASJ,EAAmBgB,CAAqB,EACtD,IAEIG,EACAC,EACAC,EACAC,EACAC,EANAN,EAAiB,EAAE,CACnBC,EAAM,EAOV,SAASM,IACP,KAAON,EAAMF,EAAc1G,MAAM,EAAI,KAAKmH,IAAI,CAACT,EAAcU,MAAM,CAACR,KAClEA,CADyE,EAClE,EAET,OAAOA,EAAMF,EAAc1G,MAAM,AACnC,CAQA,KAAO4G,EAAMF,EAAc1G,MAAM,EAAE,CAIjC,IAHA6G,EAAQD,EACRK,GAAwB,EAEjBC,KAEL,GAAIJ,AAAO,OADXA,EADuB,AAClBJ,EAAcU,MAAM,CAACR,EAAAA,EACV,CAQd,IANAG,EAAYH,EACZA,GAAO,EAEPM,IACAF,EAAYJ,EAELA,EAAMF,EAAc1G,MAAM,EAjBvB,AAAP8G,EAiBkCO,KAnBzCP,EAAKJ,EAAcU,MAAM,CAACR,CAmBiC,CAnBjCA,GAEE,MAAPE,GAAcA,AAAO,SAkBpCF,GAAO,EAILA,EAAMF,EAAc1G,MAAM,EAAkC,KAAK,CAAnC0G,EAAcU,MAAM,CAACR,IAErDK,EAAwB,GAExBL,EAAMI,EACNL,EAAeW,IAAI,CAACZ,EAAca,SAAS,CAACV,EAAOE,IACnDF,EAAQD,GAIRA,EAAMG,EAAY,CAEtB,MACEH,CADK,EACE,GAIP,CAACK,GAAyBL,GAAOF,EAAc1G,MAAAA,AAAM,EAAE,CACzD2G,EAAeW,IAAI,CAACZ,EAAca,SAAS,CAACV,EAAOH,EAAc1G,MAAM,EAE3E,CAEA,OAAO2G,CACT,CASO,SAAShB,EACdG,CAAgB,EAEhB,IAAMD,EAAmC,CAAC,EACpC2B,EAAoB,EAAE,CAC5B,GAAI1B,EACF,IAAK,GADM,AACA,CAACE,EAAKC,EAAM,GAAIH,EAAQK,OAAO,GAAI,AAClB,cAAc,CAApCH,EAAIzG,WAAW,IAIjBiI,EAAQF,IAAI,IAAI5B,EAAmBO,IACnCJ,CAAW,CAACG,EAAI,CAAsB,IAAnBwB,EAAQxH,MAAM,CAASwH,CAAO,CAAC,EAAE,CAAGA,GAEvD3B,CAAW,CAACG,EAAI,CAAGC,EAIzB,OAAOJ,CACT,CAKO,SAASD,EAAY6B,CAAiB,EAC3C,GAAI,CACF,OAAOC,OAAO,IAAIC,IAAID,OAAOD,IAC/B,CAAE,MAAOG,EAAY,CACnB,MAAM,OAAA,cAKL,CALK,AAAIC,MACR,CAAC,kBAAkB,EAAEH,OACnBD,GACA,4FAA4F,CAAC,CAC/F,CAAEK,MAAOF,CAAM,GAJX,oBAAA,OAAA,iBAAA,gBAAA,EAKN,EACF,CACF,CAMO,SAASnC,EAAwBO,CAAW,EAEjD,IAAK,IAAMgC,IADM,CAACrG,EAAAA,GACGoG,SAAU,WADU,CAAEtG,EAAAA,+BAA+B,CAAC,CAEzE,GAAIuE,IAAQgC,GAAUhC,EAAIiC,UAAU,CAACD,GACnC,MAD4C,CACrChC,EAAIuB,SAAS,CAACS,EAAOhI,MAAM,EAGtC,OAAO,IACT,8BCrKAkI,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK,gEKD1B,kCFDJ,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,oUGmBA,IAAA,EAAA,EAAA,CAAA,CAAA,0ELrBA,IAAM,EAAqB,sBACpB,OAAM,UAA2B,MACpC,YAAY,CAAW,CAAC,CACpB,KAAK,CAAC,yBAA2B,GAAc,IAAI,CAAC,WAAW,CAAG,EAAa,IAAI,CAAC,MAAM,CAAG,CACjG,CACJ,CACO,SAAS,EAAqB,CAAG,QACpC,AAAmB,UAAf,OAAO,GAA4B,OAAR,CAAgB,CAAC,CAAC,WAAY,GAAG,AAA2B,UAAtB,AAAgC,OAAzB,EAAI,MAAM,EAG/E,EAAI,MAAM,GAAK,CAC1B,EAEA,yCCZO,OAAM,ADYmC,UCZL,MACvC,YAAY,GAAG,CAAI,CAAC,CAChB,KAAK,IAAI,GAAO,IAAI,CAAC,IAAI,CAHD,EAGI,uBAChC,CACJ,CImBA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,iFClBA,OAAM,UAAqC,MACvC,YAAY,CAAK,CAAE,CAAU,CAAC,CAC1B,KAAK,CAAC,CAAC,qBAAqB,EAAE,EAAW,qGAAqG,EAAE,EAAW,8KAA8K,EAAE,EAAM,EAAE,CAAC,EAAG,IAAI,CAAC,KAAK,CAAG,EAAO,IAAI,CAAC,UAAU,CAAG,EAAY,IAAI,CAAC,MAAM,CAH1X,EAG6X,yBAC3Z,CACJ,CACA,IAAM,EAAyB,IAAI,QAOxB,SAAS,EAAmB,CAAM,CAAE,CAAK,CAAE,CAAU,EAC5D,GAAI,EAAO,OAAO,CACd,CADgB,MACT,QAAQ,MAAM,CAAC,IAAI,EAA6B,EAAO,GAC3D,EACH,IAAM,EAAiB,IAAI,QAAQ,CAAC,EAAG,KACnC,IAAM,EAAiB,EAAO,IAAI,CAAC,KAAM,IAAI,EAA6B,EAAO,IAC7E,EAAmB,EAAuB,GAAG,CAAC,GAClD,GAAI,EACA,EAAiB,IAAI,CAAC,OACnB,CACH,CAHkB,GAGZ,EAAY,CACd,EACH,CACD,EAAuB,GAAG,CAAC,EAAQ,GACnC,EAAO,gBAAgB,CAAC,QAAS,KAC7B,IAAI,IAAI,EAAI,EAAG,EAAI,EAAU,MAAM,CAAE,IAAI,AACrC,CAAS,CAAC,EAAE,EAEpB,EAAG,CACC,KAAM,EACV,EACJ,CACJ,GAKA,OADA,EAAe,KAAK,CAAC,GACd,CACX,CACJ,CACA,SAAS,IAAgB,CAClB,SAAS,EAA2B,CAAU,EAGjD,OAAO,IAAI,QAAQ,AAAC,IAEhB,WAAW,KACP,EAAQ,EACZ,EAAG,EACP,EACJ,EAEA,mDAAmD,sFC7D5C,IAAM,EAAyB,6BACzB,EAAyB,6BACzB,EAAuB,2BACvB,EAA4B,gCFyBzC,CEvBA,GFuBA,EAAA,EAAA,CAAA,CAAA,qCEvB8C,qCJJ9C,IAAM,EAAiB,kCACyE,OAAM,UAA0B,MAC5H,YAAY,CAAM,CAAC,CACf,KAAK,CAAC,sCAAwC,GAAS,IAAI,CAAC,MAAM,CAAG,EAAQ,IAAI,CAAC,MAAM,CAAG,CAC/F,CACJ,CACsH,SAAS,EAAoB,CAAG,QAC/H,AAAnB,UAAI,OAAO,GAA4B,OAAR,CAAgB,CAAC,CAAC,WAAY,GAAG,AAGzD,EAAI,CAHwD,KAGlD,GAAK,CAC1B,CEkBA,CFhBA,GEgBA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAiD,YAAnC,OAAO,CFjBe,CEiBf,OAAK,CAAC,iBAAiB,CA2BvC,SAAS,EAA0B,CAAK,CAAE,CAAa,CAAE,CAAU,EAC1E,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,QACL,IAAK,iBAML,IAAK,gBADD,MAUR,CAKJ,IAAI,EAAM,YAAY,GAAI,EAAM,WAAW,EAAE,AAC7C,GAAI,EAAM,kBAAkB,CACxB,CAD0B,KACpB,OAAO,cAAc,CAAC,IAAI,EAAsB,CAAC,MAAM,EAAE,EAAM,KAAK,CAAC,8EAA8E,EAAE,EAAW,4HAA4H,CAAC,EAAG,oBAAqB,CACvT,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,gBACD,OAAO,EAAqB,EAAM,KAAK,CAAE,EAAY,EAAc,eAAe,CACtF,KAAK,mBACD,EAAc,UAAU,CAAG,EAG3B,IAAM,EAAM,OAAO,cAAc,CAAC,IAAI,EAAmB,CAAC,MAAM,EAAE,EAAM,KAAK,CAAC,iDAAiD,EAAE,EAAW,2EAA2E,CAAC,EAAG,oBAAqB,CAC5O,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAGA,OAFA,EAAM,uBAAuB,CAAG,EAChC,EAAM,iBAAiB,CAAG,EAAI,KAAK,CAC7B,CAQd,EAER,CAMW,SAAS,EAAiC,CAAU,CAAE,CAAK,CAAE,CAAc,EAElF,IAAM,EAAM,OAAO,cAAc,CAAC,IAAI,EAAmB,CAAC,MAAM,EAAE,EAAM,KAAK,CAAC,mDAAmD,EAAE,EAAW,6EAA6E,CAAC,EAAG,oBAAqB,CAChP,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAIA,OAHA,EAAe,UAAU,CAAG,EAC5B,EAAM,uBAAuB,CAAG,EAChC,EAAM,iBAAiB,CAAG,EAAI,KAAK,CAC7B,CACV,CAOW,SAAS,EAAgC,CAAa,EAC7D,OAAO,EAAc,IAAI,EACrB,IAAK,QACL,IAAK,iBAML,IAAK,gBADD,MAiBR,CACJ,CA8EO,IAAM,EAlDN,SAAS,AAAsC,CAAY,EAG9D,EAAa,cAAc,EAAG,CAClC,EA+CO,MAD+C,GACtC,EAAS,QAAE,CAAM,OAAE,CAAK,CAAE,EACtC,IAAM,EAAiB,EAAA,oBAAoB,CAAC,QAAQ,GAEpD,EAAqB,EAAO,EADJ,GAA0C,GAC9B,eADM,EAAe,IAAI,CAAuB,EAAe,eAAe,CAAG,KAEzH,CACO,SAAS,EAAqB,CAAK,CAAE,CAAU,CAAE,CAAe,EACnE,CA4EJ,SAAS,EACL,GAAI,CAAC,EACD,MAAM,KADQ,EACD,cAAc,CAAC,AAAI,MAAM,CAAC,gIAAgI,CAAC,EAAG,oBAAqB,CAC5L,MAAO,OACP,YAAY,EACZ,aAAc,EAClB,GAER,IAnFQ,GACA,EAAgB,YADC,GACc,CAAC,IAAI,CAAC,CAGjC,MAAO,EAAgB,sBAAsB,CAAG,AAAI,QAAQ,KAAK,MAAG,aACpE,CACJ,GAEJ,EAAA,OAAK,CAAC,iBAAiB,CAAC,EAAqB,EAAO,GACxD,CACA,SAAS,EAAqB,CAAK,CAAE,CAAU,EAC3C,MAAO,CAAC,MAAM,EAAE,EAAM,iEAAiE,EAAE,EAAW,kKAAE,CAAC,AAC3G,CAUA,EAX8G,CAW1G,AAAgE,AAX2C,KAQ/G,EAG2E,OAHlE,AAAwB,CAAM,EACnC,OAAO,EAAO,QAAQ,CAAC,4CATmK,CAAC,GAAG,CAAC,mBASlG,EAAO,QAAQ,CAAC,mDATmK,CAAC,YAUrR,EAC4B,EAAqB,MAAO,QACpD,MAAM,OAAO,cAAc,CAAK,AAAJ,MAAU,0FAA2F,oBAAqB,CAClJ,MAAO,OACP,WAAY,GACZ,cAAc,CAClB,GAYG,SAAS,EAA4B,CAAK,EAC7C,MAAwB,UAAjB,OAAO,GAAgC,OAAV,GAXL,+BAWuB,EAAM,MAAM,EAAmC,GAA9B,MAAwC,GAAS,YAAa,GAAS,aAAiB,KACnK,CAwGO,SAAS,EAAsB,CAAU,CAAE,CAAc,EAC5D,IAAM,EAAkB,EAAe,eAAe,CAClD,GACA,EAAgB,YADC,GACc,CAAC,IAAI,CAAC,CACjC,MAAO,EAAgB,sBAAsB,CAAG,AAAI,QAAQ,KAAK,MAAG,aACpE,CACJ,EAER,CAkLO,SAAS,EAAuB,CAAc,CAAE,CAAM,SACzD,AAAI,EAAe,mBAAmB,CAC3B,CAD6B,CACd,mBAAmB,CAAC,IAAI,CAAC,IAAI,GAEhD,CACX,CAxHkE,AAAI,CA0HtE,MA1H6E,CAAC,sCA0HjC,iBA1HwF,EAAE,oBAAoB,yCAAyC,+DAAE,iBAAyC,EACtN,AAAI,OAAO,AAD4L,CAC3L,UAAU,EAAE,CAD6L,CACtK,QAAQ,CAAC,EACpD,AAAJ,OAAW,CAAC,UAAU,EAAE,EAAuB,QAAQ,CAAC,EAC1D,AAAI,OAAO,CAAC,UAAU,EAAE,EAAqB,QAAQ,CAAC,EDtd7E,IAAM,EAAO,KAAK,EAqBP,SAAS,EAAc,CAAQ,EAGtC,GAAI,CAAC,EAAS,IAAI,CACd,CADgB,KACT,CACH,EACA,EACH,CAEL,GAAM,CAAC,EAAO,EAAM,CAAG,EAAS,IAAI,CAAC,GAAG,GAClC,EAAU,IAAI,SAAS,EAAO,CAChC,OAAQ,EAAS,MAAM,CACvB,WAAY,EAAS,UAAU,CAC/B,QAAS,EAAS,OAAO,AAC7B,GACA,OAAO,cAAc,CAAC,EAAS,MAAO,CAClC,MAAO,EAAS,GAAG,CAEnB,aAAc,GACd,YAAY,EACZ,UAAU,CACd,GAcI,GAAY,EAAQ,IAAI,EAAE,AAC1B,EAAS,QAAQ,CAAC,EAAS,IAAI,QAAQ,EAAQ,IAAI,GAEvD,IAAM,EAAU,IAAI,SAAS,EAAO,CAChC,OAAQ,EAAS,MAAM,CACvB,WAAY,EAAS,UAAU,CAC/B,QAAS,EAAS,OACtB,AAD6B,GAS7B,OAPA,OAAO,cAAc,CAAC,EAAS,MAAO,CAClC,MAAO,EAAS,GAAG,CAEnB,cAAc,EACd,YAAY,EACZ,UAAU,CACd,GACO,CACH,EACA,EACH,AACL,CAzEI,CA2EJ,UA3Ee,oBAAoB,EAAE,CACjC,EAAW,IAAI,GA0EuB,kBA1EF,AAAC,IACjC,IAAM,EAAS,EAAQ,KAAK,GACxB,GAAU,CAAC,EAAO,MAAM,EACxB,AAD0B,EACnB,MAAM,CAAC,8CAA8C,IAAI,CAAC,EAEzE,EAAA,EFDJ,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,KAIO,IAAM,EAAoB,OAAO,GAAG,CAAC,cA0D5C,SAAS,EAAiB,CAAS,CAAE,CAAG,EAC/B,EAAU,uBAAuB,EAAE,CAGxC,EAAU,YAAY,GAAK,EAAE,CAC7B,EAAU,YAAY,CAAC,IAAI,CAAC,CACxB,GAAG,CAAG,CACN,IAAK,YAAY,UAAU,CAAG,YAAY,GAAG,GAC7C,IAAK,EAAU,WAAW,EAAI,CAClC,GACJ,CACA,eAAe,EAA8B,CAAG,CAAE,CAAQ,CAAE,CAAuB,CAAE,CAAgB,CAAE,CAAU,CAAE,CAAY,EAI3H,IAAM,EAAa,MAAM,EAAI,WAAW,GAClC,EAAc,CAChB,QAAS,OAAO,WAAW,CAAC,EAAI,OAAO,CAAC,OAAO,IAC/C,KAAM,OAAO,IAAI,CAAC,GAAY,QAAQ,CAAC,UACvC,OAAQ,EAAI,MAAM,CAClB,IAAK,EAAI,GACb,AADgB,EAahB,OATI,GACA,MAAM,EAAiB,GAAG,CAAC,EAAU,CACjC,KAAM,EAFe,AAEf,eAAe,CAAC,KAAK,CAC3B,KAAM,aACN,CACJ,EAAG,GAEP,MAAM,IAEC,IAAI,SAAS,EAAY,CAC5B,QAAS,EAAI,OAAO,CACpB,OAAQ,EAAI,MAAM,CAClB,WAAY,EAAI,UACpB,AAD8B,EAElC,CACA,eAAe,EAA4B,CAAS,CAAE,CAAG,CAAE,CAAQ,CAAE,CAAuB,CAAE,CAAgB,CAAE,CAAwB,CAAE,CAAU,CAAE,CAAK,CAAE,CAAY,EAIrK,GAAM,CAAC,EAAS,EAAQ,CAAG,EAAc,GAInC,EAAkB,EAAQ,WAAW,GAAG,IAAI,CAAC,MAAO,IACtD,IAAM,EAAa,OAAO,IAAI,CAAC,GACzB,EAAc,CAChB,QAAS,OAAO,WAAW,CAAC,EAAQ,OAAO,CAAC,OAAO,IACnD,KAAM,EAAW,QAAQ,CAAC,UAC1B,OAAQ,EAAQ,MAAM,CACtB,IAAK,EAAQ,GAAG,AACpB,CAC4B,OAA5B,AAAmC,GAAS,EAAJ,AAA6B,GAAG,CAAC,EAAU,GAC/E,GACA,MAAM,EAAiB,GAAG,CAAC,EAAU,CACjC,KAAM,EAFe,AAEf,eAAe,CAAC,KAAK,CAC3B,KAAM,aACN,CACJ,EAAG,EAEX,GAAG,KAAK,CAAC,AAAC,GAAQ,QAAQ,IAAI,CAAC,CAAC,yBAAyB,CAAC,CAAE,EAAO,IAAQ,OAAO,CAAC,GAC7E,EAAuB,CAAC,UAAU,EAAE,EAAA,CAAU,CAgBpD,OAfA,EAAU,kBAAkB,GAAK,CAAC,EAC9B,KAAwB,EAAU,kBAAkB,EAAE,AAGtD,MAAM,EAAU,kBAAkB,CAAC,EAAqB,CAE5D,EAAU,kBAAkB,CAAC,EAAqB,CAAG,EAAgB,OAAO,CAAC,KACzE,IAAI,GAGE,AAAkE,OAAjE,EAAgC,EAAU,kBAAA,AAAkB,EAAY,KAAK,EAAI,CAA6B,CAAC,EAAA,AAAqB,GAAG,AAG9I,OAAO,EAAU,kBAAkB,CAAC,EAAqB,AAC7D,GACO,CACX,CA6rBO,SAAS,EAAW,CAAO,EAE9B,GAx0BO,CAw0BH,AAx0BqC,cAAxB,CAAC,EAAkB,CAw0Bd,OAGtB,IAAM,EM/zBH,AN+zBc,SM/zBL,AAAkB,CAAa,EAC3C,IAAM,EAAkB,EAAA,KAAW,CACnC,AAAC,GAAM,EAAE,EACT,OAAO,SAAS,AAAY,CAAQ,CAAE,CAAO,MAYrC,EACA,EAZJ,GAAI,GAAW,EAAQ,MAAM,CAQzB,CAR2B,MAQpB,EAAc,EAAU,GAKnC,GAAwB,UAApB,EAAgC,KAAzB,GAA0B,EAI9B,CAKH,IAAM,EAA8B,AATM,UAS1B,OAAO,GAAyB,aAAoB,IAAM,IAAI,QAAQ,EAAU,GAAW,EAC3G,GAAI,AAAmB,UAAX,MAAM,EAAiC,SAAnB,EAAQ,MAAM,EAAe,EAAQ,SAAS,CAK1E,CAL4E,MAKrE,EAAc,EAAU,GAEnC,EA7CD,KAAK,IA6CO,KA7CE,CAAC,CAClB,EAAQ,MAAM,CACd,MAAM,IAAI,CAAC,EAAQ,OAAO,CAAC,OAAO,IAClC,EAAQ,IAAI,CA0CoB,AAzChC,EAAQ,QAAQ,CAChB,EAAQ,WAAW,CACnB,EAAQ,QAAQ,CAChB,EAAQ,cAAc,CACtB,EAAQ,SAAS,CACpB,EAqCO,EAAM,EAAQ,GAAG,AACrB,MAjBI,EAvCW,SAuCA,sCAvC+C,AAwC1D,EAAM,EAiBV,IAAM,EAAe,EAAgB,GACrC,IAAI,IAAI,EAAI,EAAG,EAAI,EAAa,MAAM,CAAE,EAAI,EAAG,GAAK,EAAE,CAClD,EA3D4G,CA2DtG,CAAC,EAAK,EAAQ,CAAG,CAAY,CAAC,EAAE,CACtC,GAAI,IAAQ,EACR,OAAO,CADW,CACH,IAAI,CAAC,KAChB,IAAM,EAAW,CAAY,CAAC,EAAE,CAAC,EAAE,CACnC,GAAI,CAAC,EAAU,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,sBAAuB,oBAAqB,CACtG,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAKA,GAAM,CAAC,EAAS,EAAQ,CAAG,EAAc,GAEzC,OADA,CAAY,CAAC,EAAE,CAAC,EAAE,CAAG,EACd,CACX,EAER,CAGA,IAAM,EAAU,EAAc,EAAU,GAClC,EAAQ,CACV,EACA,EACA,KACH,CAED,OADA,EAAa,IAAI,CAAC,GACX,EAAQ,IAAI,CAAC,AAAC,IAKjB,GAAM,CAAC,EAAS,EAAQ,CAAG,EAAc,GAEzC,OADA,CAAK,CAAC,EAAE,CAAG,EACJ,CACX,EACJ,CACJ,EAEA,ANgvBuC,WAAW,KAAK,CAEnD,YAAW,KAAK,CAnsBb,AAmsBgB,KMlvBiB,IN+CH,AAArB,CAAgC,CAAE,kBAAE,CAAgB,sBAAE,CAAoB,CAAE,EAExF,IAAM,EAAU,eAAe,AAAM,CAAK,CAAE,CAAI,MACxC,EAAc,MACd,EACJ,GAAI,CAEA,CADA,EAAM,IAAI,IAAI,aAAiB,QAAU,EAAM,GAAG,CAAG,EAAA,EACjD,QAAQ,CAAG,GACf,EAAI,QAAQ,CAAG,EACnB,CAAE,KAAO,CAEL,OAAM,CACV,CACA,IAAM,EAAW,CAAQ,MAAP,EAAc,KAAK,EAAI,EAAI,IAAA,AAAI,GAAK,GAChD,EAAS,CAAS,MAAR,CAAe,EAAyC,AAAhC,GAAJ,IAAK,EAAe,EAAK,MAAM,AAAN,EAAkB,KAAK,EAAI,EAAa,WAAW,EAAE,GAAK,MAGjH,EAAa,CAAS,MAAR,CAAe,EAAqC,AAA5B,GAAJ,IAAK,EAAa,EAAK,IAAA,AAAI,EAAY,KAAK,EAAI,EAAW,QAAQ,KAAM,EAC3G,EAAoD,MAAzC,QAAQ,GAAG,CAAC,wBAAwB,CAK/C,EAAa,OAAa,EAAY,YAAY,UAAU,CAAG,YAAY,GAAG,GAC9E,EAAY,EAAiB,QAAQ,GACrC,EAAgB,EAAqB,QAAQ,GAE/C,EAAc,EAAgB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GAAiB,KAC9D,GACA,EAAY,QADC,CACQ,GAEzB,IAAM,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAAG,KAAK,CAAC,EAAa,EAAA,kBAAkB,CAAC,aAAa,CAAG,EAAA,aAAa,CAAC,KAAK,CAAE,UAClG,EACA,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,SAAU,CACN,QACA,EACA,EACH,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,KACvB,WAAY,CACR,WAAY,EACZ,cAAe,EACf,gBAAwB,MAAP,EAAc,KAAK,EAAI,EAAI,QAAQ,CACpD,gBAAiB,CAAQ,MAAP,EAAc,KAAK,EAAI,EAAI,IAAA,AAAI,QAAK,CAC1D,CACJ,EAAG,cACK,MAgCA,EAwDA,EAwMA,EAGA,EA2HA,EAxYA,EApBJ,GAAI,GAMA,CAAC,GAKD,EAAU,GAXE,CAoBM,EAdN,KAKS,CAVrB,CAUuB,MAVhB,EAAY,EAAO,GAa9B,IAAM,EAAiB,GAA0B,UAAjB,OAAO,GAA8C,UAAxB,OAAO,EAAM,MAAM,CAC1E,EAAiB,AAAC,GAGb,CADe,MAAR,EAAe,AACb,KADkB,EAAI,CAAI,CAAC,EAAA,AAAM,IAChC,EAAiB,CAAK,CAAC,EAAM,CAAG,IAAA,CAAI,CAGnD,EAAe,AAAC,IAClB,IAAI,EAAY,EAAa,EAC7B,OAAO,AAAmG,OAA5F,GAAS,MAAR,CAAe,EAAS,AAA4B,GAAhC,IAAK,EAAa,EAAK,IAAA,AAAI,EAAY,KAAK,EAAI,CAAU,CAAC,EAAM,EAA4B,MAAR,CAAe,EAAS,AAA6B,GAAjC,GAAK,GAAc,EAAK,IAAA,AAAI,EAAY,KAAK,EAAI,CAAW,CAAC,EAAM,CAAG,EAAiB,AAA8B,OAA7B,EAAc,EAAM,IAAI,AAAJ,EAAgB,KAAK,EAAI,CAAW,CAAC,EAAM,MAAG,CAC1S,EAGM,EAA0B,EAAa,cACzC,EAAyB,EACvB,EA9LX,AA8LkB,SA9LT,AAAa,CAAI,CAAE,CAAW,EAC1C,IAAM,EAAY,EAAE,CACd,EAAc,EAAE,CACtB,IAAI,IAAI,EAAI,EAAG,EAAI,EAAK,MAAM,CAAE,IAAI,CAChC,IAAM,EAAM,CAAI,CAAC,EAAE,CAcnB,GAbmB,UAAf,AAAyB,OAAlB,EACP,EAAY,IAAI,CAAC,KACb,EACA,OAAQ,gCACZ,GACO,EAAI,MAAM,CAAG,EAAA,yBAAyB,CAC7C,CAD+C,CACnC,IAAI,CAAC,CACb,MACA,OAAQ,CAAC,uBAAuB,EAAE,EAAA,yBAAyB,CAAA,CAAE,AACjE,GAEA,EAAU,IAAI,CAAC,GAEf,EAAU,MAAM,CAAG,EAAA,wBAAwB,CAAE,CAC7C,QAAQ,IAAI,CAAC,CAAC,oCAAoC,EAAE,EAAY,eAAe,CAAC,CAAE,EAAK,KAAK,CAAC,GAAG,IAAI,CAAC,OACrG,KACJ,CACJ,CACA,GAAI,EAAY,MAAM,CAAG,EAErB,CAFwB,GAEnB,GAAM,CAAE,KAAG,QAAE,CAAM,CAAE,GAD1B,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,EAAY,EAAE,CAAC,EACjC,GAC1B,QAAQ,CAD8B,EAC3B,CAAC,CAAC,MAAM,EAAE,EAAI,EAAE,EAAE,EAAA,CAAQ,EAG7C,OAAO,CACX,EAgKsC,EAAa,SAAW,EAAE,CAAE,CAAC,MAAM,EAAE,EAAM,QAAQ,GAAA,CAAI,EAEjF,GAAI,EACA,OAAO,EAAc,IAAI,AADV,EAEX,IAAK,YACL,IAAK,oBAEL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACL,IAAK,QACL,IAAK,gBACD,EAAkB,CAO1B,CAEJ,GAAI,GACI,MAAM,OAAO,CADA,AACC,GAAO,CAErB,IAAM,EAAgB,EAAgB,IAAI,GAAK,CAAD,CAAiB,IAAI,CAAG,EAAA,AAAE,EACxE,IAAK,IAAM,KAAO,EACV,AAAC,EAAc,CADA,OACQ,CAAC,IACxB,EAD8B,AAChB,IAAI,CAAC,EAG/B,CAEJ,IAAM,EAAgC,MAAjB,EAAwB,KAAK,EAAI,EAAc,YAAY,CAC5E,EAAqB,EAAU,UAAU,CACzC,GAES,YAFM,OACR,EAAc,IAAI,EAIjB,GAAqB,gBAAA,EAejC,IAAM,EAAiB,CAAC,CAAC,EAAU,iBAAiB,CAChD,EAA0B,EAAe,SACzC,EAAc,GAEqB,UAAnC,OAAO,GAAwC,KAAkC,IAA3B,IAG1B,KAHkE,WAG9F,GAAwE,IAA3B,CAAgC,EACjD,aAA5B,CAA0C,GAAC,EAAyB,IAAgC,IAA3B,CAA2B,CAAK,IAErG,EAAe,CAAC,kBAAkB,EAHiG,AAG/F,EAAwB,mBAAmB,EAAE,EAAuB,gCAAgC,CAAC,CACzI,OAA0B,EAC1B,OAAyB,GAGjC,IAAM,EACsB,aAA5B,GAAsE,aAA5B,CAA0C,EAE7D,mBAAvB,GAAkE,kBAAvB,EAMrC,EAA+B,CAAC,GAAsB,CAAC,GAA2B,CAAC,GAA0B,EAAU,YAAY,CAG7G,gBAA5B,GAA6C,CAXoI,IAWlG,IAA3B,EAChD,EAAyB,GAClB,EAFiF,EAElD,CAAA,GAA8B,CACpE,GAAyB,EAEzB,CAA4B,gBAA0C,aAA5B,CAA4B,GAAY,AAClF,GAAc,CAAC,OAAO,EAAE,EAAA,CAAA,AAAyB,EAErD,EA9SL,AA8SuB,SA9Sd,AAAmB,CAAa,CAAE,CAAK,EACnD,GAAI,CACA,IAAI,EACJ,IAAsB,IAAlB,EACA,CADyB,CACF,EAAA,OAFA,OAEc,MAClC,GAA6B,UAAzB,OAAO,GAA8B,CAAC,MAAM,IAAkB,EAAgB,CAAC,EACtF,CADyF,CAClE,OACpB,GAAI,KAAyB,IAAlB,EACd,MAAM,CADuC,MAChC,cAAc,CAAC,AAAI,MAAM,CAAC,0BAA0B,EAAE,EAAc,MAAM,EAAE,EAAM,yCAAyC,CAAC,EAAG,oBAAqB,CAC7J,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,OAAO,CACX,CAAE,MAAO,EAAK,CAEV,GAAI,aAAe,OAAS,EAAI,OAAO,CAAC,QAAQ,CAAC,sBAC7C,CADoE,KAC9D,EAEV,MACJ,CACJ,AAFe,EA0RkC,EAAwB,EAAU,KAAK,EAC5E,IAAM,EAAW,EAAe,WAC1B,EAAoE,YAAtD,OAAO,AAAa,MAAZ,EAAmB,KAAK,EAAI,EAAS,GAAA,AAAG,EAAmB,EAAW,IAAI,QAAQ,GAAY,CAAC,GACrH,EAAuB,EAAY,GAAG,CAAC,kBAAoB,EAAY,GAAG,CAAC,UAC3E,EAAsB,CAAC,CACzB,MACA,OACH,CAAC,QAAQ,CAAC,CAAiD,AAAhD,OAAC,EAAkB,EAAe,SAAA,CAAS,CAAY,KAAK,EAAI,EAAgB,WAAW,EAAA,CAAE,EAAK,OAUxG,EACN,KAAsB,YACM,CADO,EAClC,GAE2B,OAFa,KAEzC,CAA4B,CAAS,KAAK,EAChB,GAA1B,EACI,EAAc,CALmD,CAK3C,CAAC,IAAwB,CAAA,CAAmB,EAAK,CAAoB,MAAnB,EAA0B,KAAK,AAF/B,EAEmC,EAAgB,UAAA,AAAU,KAAM,EAC3I,EAA2B,GAa/B,GAZI,CAAC,GAAe,AANoG,IAUhH,EAAU,oBAJ4B,GAIL,CACjC,CADmC,EACR,EAE3B,GAAc,GAKlB,GAA4B,KAAkB,MAC9C,KADyD,EAClD,EAAc,IAAI,EACrB,IAAK,YACL,IAAK,oBAIL,IAAK,mBAKD,OAJI,IACA,EAAY,OADC,AACM,GACnB,EAAc,MAEX,EAAmB,EAAc,YAAY,CAAE,EAAU,KAAK,CAAE,UAU/E,CAEJ,OAAO,GACH,IAAK,iBAEG,EAAc,8BACd,KAER,KAAK,gBAEG,GAAgC,gBAA5B,GAA6C,KAA2B,IAApB,GAAmC,EAAkB,EACzG,CAD4G,KACtG,OAAO,cAAc,CAAK,AAAJ,MAAU,CAAC,uCAAuC,EAAE,EAAS,gDAAgD,CAAC,EAAG,oBAAqB,CAC9J,MAAO,OACP,YAAY,EACZ,aAAc,EAClB,GAEJ,EAAc,6BACd,KAER,KAAK,aAEG,GAAgC,YAAY,CAAxC,EACA,MAAM,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,oCAAoC,EAAE,EAAS,6CAA6C,CAAC,EAAG,oBAAqB,CACxJ,MAAO,OACP,WAAY,GACZ,cAAc,CAClB,GAEJ,KAER,KAAK,eAEO,KAAkC,IAA3B,OAA0C,CAA2B,GAAG,CAC/E,EAAc,2BACd,EAAkB,EAAA,cAAc,CAWhD,CAsBA,GArBI,CAsBJ,IAtB+B,IAApB,EACoB,OADa,QAsBnB,GArBjB,CAA0C,EAAC,EAGb,cAH6B,MAGT,CAA3C,GACP,EAAkB,EAClB,EAAc,iCACP,GACP,EAAkB,EAClB,EAAc,OAFS,SAGhB,GACP,EAAkB,EAClB,EAAc,IAFM,cAKpB,EAAc,aACd,EAAkB,EAAkB,EAAgB,UAAU,CAAG,EAAA,cAAc,GAd/E,EAAkB,EAAA,cAAc,CAChC,EAAc,8BAeX,AAAC,IACR,EAAc,CAAC,MADM,MACM,EAAE,EAAA,CAAA,AAAiB,EAIlD,CAAC,CAAC,EAAU,WAAW,EAAI,AAAoB,CAAC,KAAK,EACrD,CAAC,GAGD,GAAmB,EAAkB,EAAgB,KAHrC,KAG+C,CAAE,CAG7D,GAAwB,IAApB,EAAuB,CACvB,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,EAV6F,UAWlG,IAAK,UAV8D,SAWnE,IAAK,oBAKD,OAJI,IACA,EAAY,OAAO,AADN,GAEb,EAAc,MAEX,EAAmB,EAAc,YAAY,CAAE,EAAU,KAAK,CAAE,UAU/E,CAEJ,EAA0B,EAAW,EAAe,CAAC,oBAAoB,EAAE,EAAM,CAAC,EAAE,EAAU,KAAK,CAAA,CAAE,CACzG,CAII,GAAmB,IAA4B,IAC/C,EAAgB,UAAU,CADsC,AACnC,CAAA,CAErC,CACA,IAAM,EAAmD,UAA3B,OAAO,GAAgC,EAAkB,EAEjF,kBAAE,CAAgB,CAAE,CAAG,EACzB,GAAe,EAEnB,GAAI,EACA,OAAO,EAAc,IAAI,AADV,EAEX,IAAK,UACL,IAAK,QACL,IAAK,gBACD,EAAe,EAAc,YAAY,GAAI,EAC7C,EAA2B,EAAc,wBAAwB,AAWzE,CAEJ,GAAI,IAAqB,GAAyB,CAAA,CAAwB,CACtE,EADyE,CACrE,CACA,EAAW,IAFK,EAEC,EAAiB,gBAAgB,CAAC,EAAU,EAAiB,EAAQ,EAC1F,CAAE,MAAO,EAAK,CACV,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,CAAE,EACtD,CAEJ,IAAM,EAAW,EAAU,WAAW,EAAI,EAC1C,EAAU,WAAW,CAAG,EAAW,EACnC,IAAI,EAAe,KAAK,EAClB,EAAkB,MAAO,EAAS,KACpC,IAAM,EAAqB,CACvB,QACA,cACA,UACA,YACA,YACA,SACA,OACA,WACA,WACA,iBACA,SACA,YAEG,EAAU,EAAE,CAAG,CACd,SACH,CACJ,CACD,GAAI,EAAgB,CAChB,IAAM,EAAW,EACX,EAAa,CACf,KAAM,EAAS,OAAO,EAAI,EAAS,IAAI,AAC3C,EACA,IAAK,IAAM,KAAS,EAEhB,CAAU,CAAC,EAAM,CAAG,CAAQ,CAAC,EAAM,CAEvC,EAAQ,IAAI,CAJ2B,OAInB,EAAS,GAAG,CAAE,EACtC,MAAO,GAAI,EAAM,CACb,GAAM,CAAE,SAAO,MAAE,CAAI,QAAE,CAAM,CAAE,GAAG,EAAY,CAAG,EACjD,EAAO,CACH,GAAG,CAAU,CACb,KAAM,GAAW,EACjB,OAAQ,OAAU,EAAY,CAClC,CACJ,CAEA,IAAM,EAAa,CACf,GAAG,CAAI,CACP,KAAM,CACF,GAAW,MAAR,EAAe,KAAK,EAAI,EAAK,IAAI,CACpC,UAAW,kBACX,CACJ,CACJ,EACA,OAAO,EAAY,EAAO,GAAY,IAAI,CAAC,MAAO,IAY9C,GAXI,CAAC,GAAW,GACZ,EAAiB,EAAW,CACxB,IAFoB,EAEb,EACP,IAAK,EACL,YAAa,GAAuB,EACpC,YAAiC,IAApB,GAAyB,EAAsB,OAAS,oBACrE,EACA,OAAQ,EAAI,MAAM,CAClB,OAAQ,EAAW,MAAM,EAAI,KACjC,GAEe,MAAf,EAAI,MAAM,EAAY,GAAoB,IAAa,GAAyB,CAAA,CAAwB,CAAG,CAC3G,CADsD,GAChD,EAAuB,GAAmB,EAAA,cAAc,CAAG,EAAA,cAAc,CAAG,EAC5E,EAAyB,EAAwB,CACnD,YAAY,EACZ,oBACA,OACA,2BACA,CACJ,OAAI,EACJ,OAAwB,MAAjB,EAAwB,KAAK,EAAI,EAAc,IAAI,EACtD,IAAK,YACL,IAAK,mBACL,IAAK,oBACD,OAAO,EAA8B,EAAK,EAAU,EAAwB,EAAkB,EAAsB,EACxH,KAAK,gBACL,IAAK,mBACL,IAAK,UACL,IAAK,QACL,IAAK,gBACL,IAAK,iBACL,UAAK,EACD,OAAO,EAA4B,EAAW,EAAK,EAAU,EAAwB,EAAkB,EAA0B,EAAsB,EAAO,EAGtK,CACJ,CAIA,OADA,MAAM,IACC,CACX,GAAG,KAAK,CAAC,AAAC,IAEN,MADA,IACM,CACV,EACJ,EAEI,GAAyB,EACzB,GAAoB,EACxB,GAAI,GAAY,EAAkB,CAC9B,IAAI,EAKJ,GAJI,GAAgB,IAChB,EAAkB,EAAyB,GAAG,CAAC,GAC/C,GAAoB,GAEpB,GAAyB,CAAC,CAJgB,CAIC,CAC3C,EAAe,MAAM,EAAiB,IAAI,CAAC,GAC3C,IAAM,EAAQ,EAAU,oBAAoB,CAAG,KAAO,MAAM,EAAiB,GAAG,CAAC,EAAU,CACvF,KAAM,EAAA,oBAAoB,CAAC,KAAK,CAChC,WAAY,WACZ,WACA,OACA,EACA,SAA0B,MAAhB,EAAuB,KAAK,EAAI,EAAa,IAAI,AAC/D,GACA,GAAI,GAA4B,EAC5B,OAAO,EAAc,IAAI,AADkB,EAEvC,IAAK,YACL,IAAK,mBACL,IAAK,oBAMD,MAAM,CAAA,EAAA,EAAA,6BAAA,AAA6B,GAW3C,CAQJ,GANI,EACA,KADO,CACD,IAGN,EAAsB,yCAEtB,CAAU,MAAT,EAAgB,KAAK,EAAI,EAAM,KAAA,AAAK,GAAK,EAAM,KAAK,CAAC,IAAI,GAAK,EAAA,eAAe,CAAC,KAAK,CAGpF,CAHsF,EAGlF,EAAU,YAAY,EAAI,EAAM,OAAO,CACvC,CADyC,EAChB,MACtB,CACH,GAAI,EAAM,OAAO,EAAE,CACf,EAAU,kBAAkB,GAAK,CAAC,EAC9B,CAAC,EAAU,kBAAkB,CAAC,EAAS,EAAE,CACzC,IAAM,EAAoB,GAAgB,GAAM,IAAI,CAAC,MAAO,IAAY,CAChE,KAAM,CADyD,KACnD,EAAS,WAAW,GAChC,QAAS,EAAS,OAAO,CACzB,OAAQ,EAAS,MAAM,CACvB,WAAY,EAAS,UAAU,CACnC,CAAC,EAAG,OAAO,CAAC,KACZ,EAAU,kBAAkB,GAAK,CAAC,EAClC,OAAO,EAAU,kBAAkB,CAAC,GAAY,GAAG,AACvD,GAGA,EAAkB,KAAK,CAAC,QAAQ,KAAK,EACrC,EAAU,kBAAkB,CAAC,EAAS,CAAG,CAC7C,CAEJ,EAAkB,EAAM,KAAK,CAAC,IAAI,AACtC,CAER,CACA,GAAI,EAAiB,CACb,GACA,EAAiB,EAAW,CACxB,IAFQ,EAED,EACP,IAAK,cACL,EACA,YAAa,EAAoB,MAAQ,mBACzC,EACA,OAAQ,EAAgB,MAAM,EAAI,IAClC,OAAQ,AAAC,CAAQ,QAAO,KAAK,EAAI,EAAK,MAAA,AAAM,GAAK,KACrD,GAEJ,IAAM,EAAW,IAAI,SAAS,OAAO,IAAI,CAAC,EAAgB,IAAI,CAAE,UAAW,CACvE,QAAS,EAAgB,OAAO,CAChC,OAAQ,EAAgB,MAAM,AAClC,GAIA,OAHA,OAAO,cAAc,CAAC,EAAU,MAAO,CACnC,MAAO,EAAgB,GAAG,AAC9B,GACO,CACX,CACJ,CACA,GAAI,EAAU,kBAAkB,EAAI,GAAwB,UAAhB,OAAO,EAAmB,CAClE,GAAM,OAAE,CAAK,CAAE,CAAG,EAGlB,GAAc,aAAV,EAAsB,CAEtB,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,YACL,IAAK,mBACL,IAAK,oBAKD,OAJI,IACA,EAAY,OADC,AACM,GACnB,EAAc,MAEX,EAAmB,EAAc,YAAY,CAAE,EAAU,KAAK,CAAE,UAU/E,CAEJ,EAA0B,EAAW,EAAe,CAAC,eAAe,EAAE,EAAM,CAAC,EAAE,EAAU,KAAK,CAAA,CAAE,CACpG,CACA,IAAM,EAAgB,SAAU,EAC1B,MAAE,EAAO,CAAC,CAAC,CAAE,CAAG,EACtB,GAA+B,UAA3B,OAAO,EAAK,UAAU,EAAiB,GAAmB,EAAK,UAAU,CAAG,EAAgB,UAAU,CAAE,CACxG,GAAwB,IAApB,EAAK,UAAU,CAAQ,CAEvB,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,YACL,IAAK,mBACL,IAAK,oBACD,OAAO,EAAmB,EAAc,YAAY,CAAE,EAAU,KAAK,CAAE,UAU/E,CAEJ,EAA0B,EAAW,EAAe,CAAC,oBAAoB,EAAE,EAAM,CAAC,EAAE,EAAU,KAAK,CAAA,CAAE,CACzG,CACI,AAAC,EAAU,WAAW,EAAwB,GAAG,CAAvB,EAAK,UAAU,GACzC,EAAgB,UAAU,CAAG,EAAK,UAAA,AAAU,CAEpD,CACI,GAAe,OAAO,EAAK,IAAI,AACvC,CAIA,IAAI,IAAY,EAgDZ,OAAO,GAAgB,EAAO,EAhDM,EACpC,IAAM,EAAuB,EAC7B,EAAU,kBAAkB,GAAK,CAAC,EAClC,IAAI,EAAoB,EAAU,kBAAkB,CAAC,EAAqB,CAC1E,GAAI,EAAmB,CACnB,IAAM,EAAoB,MAAM,EAChC,OAAO,IAAI,SAAS,EAAkB,IAAI,CAAE,CACxC,QAAS,EAAkB,OAAO,CAClC,OAAQ,EAAkB,MAAM,CAChC,WAAY,EAAkB,UAAU,AAC5C,EACJ,CASA,IAAM,EAAkB,GAAgB,EAAM,GAI7C,IAAI,CAAC,GAsBN,MAFA,CAnBA,EAAoB,CAL8C,CAK9B,IAAI,CAAC,MAAO,IAC5C,IAAM,EAAW,CAAS,CAAC,EAAE,CAC7B,MAAO,CACH,KAAM,MAAM,EAAS,WAAW,GAChC,CATwH,OAS/G,EAAS,OAAO,CACzB,OAAQ,EAAS,MAAM,CACvB,WAAY,EAAS,UAAU,AACnC,CACJ,GAAG,OAAO,CAAC,KACP,IAAI,GAGE,AAAkE,OAAjE,EAAgC,EAAU,kBAAA,AAAkB,EAAY,KAAK,EAAI,CAA6B,CAAC,EAAA,AAAqB,GAAG,AAG9I,OAAO,EAAU,kBAAkB,CAAC,EAAqB,AAC7D,EAAA,EAGkB,KAAK,CAAC,KAAK,GAC7B,EAAU,kBAAkB,CAAC,EAAqB,CAAG,EAC9C,EAAgB,IAAI,CAAC,AAAC,GAAY,CAAS,CAAC,EAAE,CACzD,CAGJ,GACA,GAAI,AAJO,EAKP,GAAI,CACA,OAFS,AAEF,MAAM,CACjB,QAAS,CACD,GACA,EAAY,OAAO,CADN,CAGrB,CAEJ,OAAO,CACX,EAeA,OAVA,EAAQ,aAAa,EAAG,EACxB,EAAQ,oBAAoB,CAAG,IAAI,EACnC,EAAQ,kBAAkB,CAAG,EAC7B,UAAU,CAAC,EAAkB,EAAG,EAGhC,OAAO,cAAc,CAAC,EAAS,OAAQ,CACnC,MAAO,QACP,UAAU,CACd,GACO,CACX,EAU4C,EAAU,EACtD,EAEA,uCAAuC", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}