module.exports=[49846,a=>{"use strict";a.s(["default",()=>v],49846);var b=a.i(87924),c=a.i(72131),d=a.i(50944),e=a.i(11642);let f=(0,c.createContext)(void 0);function g({children:a}){let[d,e]=(0,c.useState)([]),[g,h]=(0,c.useState)(null),[i,j]=(0,c.useState)(!0),k=async()=>{try{let a=await fetch("/api/groups");if(a.ok){let b=await a.json();e(b.groups)}else console.error("Failed to fetch groups")}catch(a){console.error("Failed to refresh groups:",a)}},l=async a=>{let b=await fetch("/api/groups",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!b.ok)throw Error((await b.json()).error||"Failed to create group");let c=await b.json();return await k(),c.group},m=async(a,b)=>{let c=await fetch(`/api/groups/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!c.ok)throw Error((await c.json()).error||"Failed to update group");let d=await c.json();return await k(),g?.id===a&&h(d.group),d.group},n=async a=>{let b=await fetch(`/api/groups/${a}`,{method:"DELETE"});if(!b.ok)throw Error((await b.json()).error||"Failed to delete group");g?.id===a&&h(null),await k()},o=async(a,b,c="MEMBER")=>{let d=await fetch(`/api/groups/${a}/members`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:b,role:c})});if(!d.ok)throw Error((await d.json()).error||"Failed to add member");if(await k(),g?.id===a){let b=await fetch(`/api/groups/${a}`);b.ok&&h((await b.json()).group)}};return(0,c.useEffect)(()=>{k().finally(()=>j(!1))},[]),(0,b.jsx)(f.Provider,{value:{groups:d,selectedGroup:g,loading:i,createGroup:l,updateGroup:m,deleteGroup:n,selectGroup:a=>{h(a)},refreshGroups:k,addMember:o},children:a})}function h(){let a=(0,c.useContext)(f);if(void 0===a)throw Error("useGroups must be used within a GroupProvider");return a}function i({isOpen:a,onClose:d}){let[e,f]=(0,c.useState)(""),[g,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(!1),[l,m]=(0,c.useState)(!1),[n,o]=(0,c.useState)(""),{createGroup:p}=h(),q=async a=>{a.preventDefault(),o(""),m(!0);try{await p({name:e,description:g||void 0,isPrivate:j}),f(""),i(""),k(!1),d()}catch(a){o(a instanceof Error?a.message:"Failed to create group")}finally{m(!1)}},r=()=>{l||(f(""),i(""),k(!1),o(""),d())};return a?(0,b.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,b.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Create New Group"}),(0,b.jsx)("button",{onClick:r,disabled:l,className:"text-gray-400 hover:text-gray-600 disabled:opacity-50",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),n&&(0,b.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:n}),(0,b.jsxs)("form",{onSubmit:q,children:[(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Group Name *"}),(0,b.jsx)("input",{id:"name",type:"text",value:e,onChange:a=>f(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter group name",required:!0,maxLength:100})]}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),(0,b.jsx)("textarea",{id:"description",value:g,onChange:a=>i(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter group description (optional)",rows:3,maxLength:500})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsxs)("label",{className:"flex items-center",children:[(0,b.jsx)("input",{type:"checkbox",checked:j,onChange:a=>k(a.target.checked),className:"mr-2"}),(0,b.jsx)("span",{className:"text-gray-700 text-sm",children:"Private Group"})]}),(0,b.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Private groups require invitation to join"})]}),(0,b.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,b.jsx)("button",{type:"button",onClick:r,disabled:l,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50",children:"Cancel"}),(0,b.jsx)("button",{type:"submit",disabled:l||!e.trim(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:l?"Creating...":"Create Group"})]})]})]})}):null}function j(){let{groups:a,selectedGroup:d,selectGroup:f,loading:g}=h(),{user:j}=(0,e.useAuth)(),[k,l]=(0,c.useState)(!1);return g?(0,b.jsx)("div",{className:"w-80 bg-white border-r border-gray-200 p-4",children:(0,b.jsxs)("div",{className:"animate-pulse",children:[(0,b.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,b.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((a,c)=>(0,b.jsx)("div",{className:"h-16 bg-gray-200 rounded"},c))})]})}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"w-80 bg-white border-r border-gray-200 flex flex-col",children:[(0,b.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,b.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Groups"}),(0,b.jsx)("button",{onClick:()=>l(!0),className:"bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full",title:"Create new group",children:(0,b.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]})}),(0,b.jsx)("div",{className:"flex-1 overflow-y-auto",children:0===a.length?(0,b.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,b.jsx)("p",{className:"mb-2",children:"No groups yet"}),(0,b.jsx)("button",{onClick:()=>l(!0),className:"text-blue-500 hover:text-blue-600 text-sm",children:"Create your first group"})]}):(0,b.jsx)("div",{className:"p-2",children:a.map(a=>(0,b.jsx)("div",{onClick:()=>f(a),className:`p-3 rounded-lg cursor-pointer mb-2 transition-colors ${d?.id===a.id?"bg-blue-50 border border-blue-200":"hover:bg-gray-50"}`,children:(0,b.jsxs)("div",{className:"flex items-start justify-between",children:[(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:a.name}),a.isPrivate&&(0,b.jsx)("svg",{className:"w-3 h-3 text-gray-400 ml-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"})})]}),a.description&&(0,b.jsx)("p",{className:"text-xs text-gray-500 truncate mt-1",children:a.description}),(0,b.jsxs)("div",{className:"flex items-center mt-2 text-xs text-gray-400",children:[(0,b.jsxs)("span",{children:[a._count.members," members"]}),(0,b.jsx)("span",{className:"mx-1",children:"•"}),(0,b.jsxs)("span",{children:[a._count.messages," messages"]}),(0,b.jsx)("span",{className:"mx-1",children:"•"}),(0,b.jsxs)("span",{children:[a._count.notes," notes"]})]})]}),a.ownerId===j?.id&&(0,b.jsx)("div",{className:"ml-2",children:(0,b.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Owner"})})]})},a.id))})})]}),(0,b.jsx)(i,{isOpen:k,onClose:()=>l(!1)})]})}let k=(0,c.createContext)(void 0);function l({children:a}){let[d,e]=(0,c.useState)([]),[f,g]=(0,c.useState)(!1),[h,i]=(0,c.useState)(!1),[j,l]=(0,c.useState)(!1),[m,n]=(0,c.useState)(1),[o,p]=(0,c.useState)(null),q=async(a,b=1)=>{a!==o&&(e([]),n(1),p(a),b=1),g(!0);try{let c=await fetch(`/api/groups/${a}/messages?page=${b}&limit=50`);if(c.ok){let a=await c.json();1===b?e(a.messages):e(b=>[...a.messages,...b]),l(a.pagination.hasMore),n(b)}else console.error("Failed to load messages")}catch(a){console.error("Failed to load messages:",a)}finally{g(!1)}},r=async(a,b)=>{i(!0);try{let c=await fetch(`/api/groups/${a}/messages`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:b})});if(!c.ok){let a=await c.json();throw Error(a.error||"Failed to send message")}let d=await c.json();e(a=>[...a,d.data])}catch(a){throw console.error("Failed to send message:",a),a}finally{i(!1)}};return(0,b.jsx)(k.Provider,{value:{messages:d,loading:f,sending:h,sendMessage:r,loadMessages:q,clearMessages:()=>{e([]),n(1),l(!1),p(null)},hasMore:j,currentPage:m},children:a})}let m=(0,c.createContext)(void 0);function n({children:a}){let[d,e]=(0,c.useState)([]),[f,g]=(0,c.useState)(null),[h,i]=(0,c.useState)(!1),[j,k]=(0,c.useState)(!1),l=async a=>{i(!0);try{let b=await fetch(`/api/groups/${a}/notes`);if(b.ok){let a=await b.json();e(a.notes)}else console.error("Failed to load notes")}catch(a){console.error("Failed to load notes:",a)}finally{i(!1)}},n=async a=>{k(!0);try{let b=await fetch(`/api/notes/${a}`);if(b.ok){let a=await b.json();g(a.note)}else console.error("Failed to load note")}catch(a){console.error("Failed to load note:",a)}finally{k(!1)}},o=async(a,b)=>{let c=await fetch(`/api/groups/${a}/notes`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!c.ok)throw Error((await c.json()).error||"Failed to create note");let d=await c.json();return await l(a),d.note},p=async(a,b)=>{let c=await fetch(`/api/notes/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!c.ok)throw Error((await c.json()).error||"Failed to update note");let d=await c.json();return f?.id===a&&g(d.note),e(c=>c.map(c=>c.id===a?{...c,...b}:c)),d.note},q=async a=>{let b=await fetch(`/api/notes/${a}`,{method:"DELETE"});if(!b.ok)throw Error((await b.json()).error||"Failed to delete note");f?.id===a&&g(null),e(b=>b.filter(b=>b.id!==a))},r=async(a,b,c,d)=>{let e=await fetch(`/api/notes/${a}/blocks`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:b,content:c,order:d})});if(!e.ok)throw Error((await e.json()).error||"Failed to create block");let g=await e.json();return f?.id===a&&await n(a),g.block},s=async(a,b)=>{let c=await fetch(`/api/blocks/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!c.ok)throw Error((await c.json()).error||"Failed to update block");let d=await c.json();return f&&g(c=>c?{...c,blocks:c.blocks.map(c=>c.id===a?{...c,...b}:c)}:null),d.block},t=async a=>{let b=await fetch(`/api/blocks/${a}`,{method:"DELETE"});if(!b.ok)throw Error((await b.json()).error||"Failed to delete block");f&&g(b=>b?{...b,blocks:b.blocks.filter(b=>b.id!==a)}:null)},u=async(a,b)=>{let c=await fetch(`/api/notes/${a}/blocks`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({blocks:b})});if(!c.ok)throw Error((await c.json()).error||"Failed to reorder blocks");f?.id===a&&await n(a)};return(0,b.jsx)(m.Provider,{value:{notes:d,selectedNote:f,loading:h,loadingNote:j,createNote:o,updateNote:p,deleteNote:q,selectNote:a=>{g(a),a?.id&&n(a.id)},loadNotes:l,loadNote:n,clearNotes:()=>{e([]),g(null)},createBlock:r,updateBlock:s,deleteBlock:t,reorderBlocks:u},children:a})}function o(){let a=(0,c.useContext)(m);if(void 0===a)throw Error("useNotes must be used within a NotesProvider");return a}function p({group:a}){let{messages:d,loading:f,sending:g,sendMessage:h,loadMessages:i,hasMore:j,currentPage:l}=function(){let a=(0,c.useContext)(k);if(void 0===a)throw Error("useMessages must be used within a MessageProvider");return a}(),{user:m}=(0,e.useAuth)(),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)(""),r=(0,c.useRef)(null),s=(0,c.useRef)(null);(0,c.useEffect)(()=>{a&&i(a.id)},[a.id]),(0,c.useEffect)(()=>{t()},[d]);let t=()=>{r.current?.scrollIntoView({behavior:"smooth"})},u=async b=>{if(b.preventDefault(),!n.trim()||g)return;q("");let c=n.trim();o("");try{await h(a.id,c)}catch(a){q(a instanceof Error?a.message:"Failed to send message"),o(c)}},v=async()=>{j&&!f&&await i(a.id,l+1)};return(0,b.jsxs)("div",{className:"flex flex-col h-full",children:[(0,b.jsxs)("div",{ref:s,className:"flex-1 overflow-y-auto p-4 space-y-4",children:[j&&(0,b.jsx)("div",{className:"text-center",children:(0,b.jsx)("button",{onClick:v,disabled:f,className:"text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50",children:f?"Loading...":"Load older messages"})}),d.map((a,c)=>{var e,f;let g=(e=a,!(f=c>0?d[c-1]:null)||new Date(e.createdAt).toDateString()!==new Date(f.createdAt).toDateString()),h=a.authorId===m?.id;return(0,b.jsxs)("div",{children:[g&&(0,b.jsx)("div",{className:"flex items-center justify-center my-4",children:(0,b.jsx)("div",{className:"bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full",children:(a=>{let b=new Date(a),c=new Date,d=new Date(c);return(d.setDate(d.getDate()-1),b.toDateString()===c.toDateString())?"Today":b.toDateString()===d.toDateString()?"Yesterday":b.toLocaleDateString()})(a.createdAt)})}),(0,b.jsx)("div",{className:`flex ${h?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`max-w-xs lg:max-w-md ${h?"order-2":"order-1"}`,children:[!h&&(0,b.jsx)("div",{className:"text-xs text-gray-700 mb-1 font-medium",children:a.author.name||a.author.username}),(0,b.jsxs)("div",{className:`px-4 py-2 rounded-lg ${h?"bg-blue-500 text-white":"bg-gray-100 text-gray-900 border border-gray-200"}`,children:[(0,b.jsx)("div",{className:"text-sm whitespace-pre-wrap",children:a.content.includes("```")?(0,b.jsx)("div",{children:a.content.split("```").map((a,c)=>c%2==0?(0,b.jsx)("span",{children:a},c):(0,b.jsx)("code",{className:"block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto",children:a},c))}):a.content}),(0,b.jsx)("div",{className:`text-xs mt-1 ${h?"text-blue-100":"text-gray-600"}`,children:new Date(a.createdAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]})})]},a.id)}),f&&0===d.length&&(0,b.jsxs)("div",{className:"text-center text-gray-500",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,b.jsx)("p",{className:"mt-2",children:"Loading messages..."})]}),!f&&0===d.length&&(0,b.jsx)("div",{className:"text-center text-gray-500 py-8",children:(0,b.jsx)("p",{children:"No messages yet. Start the conversation!"})}),(0,b.jsx)("div",{ref:r})]}),(0,b.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[p&&(0,b.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm",children:p}),(0,b.jsxs)("form",{onSubmit:u,className:"flex space-x-2",children:[(0,b.jsx)("input",{type:"text",value:n,onChange:a=>o(a.target.value),placeholder:"Type a message...",className:"flex-1 border border-gray-300 rounded-lg px-4 py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500",disabled:g,maxLength:2e3}),(0,b.jsx)("button",{type:"submit",disabled:!n.trim()||g,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?"Sending...":"Send"})]})]})]})}function q({isOpen:a,onClose:d,groupId:e}){let[f,g]=(0,c.useState)(""),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(!1),[l,m]=(0,c.useState)(""),{createNote:n}=o(),p=async a=>{a.preventDefault(),m(""),k(!0);try{await n(e,{title:f,description:h||void 0}),g(""),i(""),d()}catch(a){m(a instanceof Error?a.message:"Failed to create note")}finally{k(!1)}},q=()=>{j||(g(""),i(""),m(""),d())};return a?(0,b.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,b.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Create New Note"}),(0,b.jsx)("button",{onClick:q,disabled:j,className:"text-gray-400 hover:text-gray-600 disabled:opacity-50",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),l&&(0,b.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:l}),(0,b.jsxs)("form",{onSubmit:p,children:[(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"title",children:"Note Title *"}),(0,b.jsx)("input",{id:"title",type:"text",value:f,onChange:a=>g(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter note title",required:!0,maxLength:200})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),(0,b.jsx)("textarea",{id:"description",value:h,onChange:a=>i(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter note description (optional)",rows:3,maxLength:500})]}),(0,b.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,b.jsx)("button",{type:"button",onClick:q,disabled:j,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50",children:"Cancel"}),(0,b.jsx)("button",{type:"submit",disabled:j||!f.trim(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:j?"Creating...":"Create Note"})]})]})]})}):null}function r({block:a,noteId:d,isLast:e,onAddBlock:f}){let{updateBlock:g,deleteBlock:h}=o(),[i,j]=(0,c.useState)(a.content),[k,l]=(0,c.useState)(!1),[m,n]=(0,c.useState)(!1),p=(0,c.useRef)(null);(0,c.useEffect)(()=>{j(a.content)},[a.content]),(0,c.useEffect)(()=>{k&&p.current&&(p.current.focus(),p.current.style.height="auto",p.current.style.height=p.current.scrollHeight+"px")},[k]);let q=async()=>{try{await g(a.id,{content:i}),l(!1)}catch(a){console.error("Failed to update block:",a),alert("Failed to update block")}},r=b=>{"Enter"!==b.key||b.shiftKey?"Escape"===b.key&&(j(a.content),l(!1)):(b.preventDefault(),e&&i.trim()?f("TEXT"):q())},s=async()=>{if(confirm("Are you sure you want to delete this block?"))try{await h(a.id)}catch(a){console.error("Failed to delete block:",a),alert("Failed to delete block")}},t=async b=>{try{await g(a.id,{type:b}),n(!1)}catch(a){console.error("Failed to update block type:",a),alert("Failed to update block type")}},u=a=>{switch(a){case"HEADING":return"text-xl font-bold text-gray-900";case"CODE":return"code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto";case"QUOTE":return"border-l-4 border-blue-400 pl-4 italic text-gray-700 bg-blue-50 py-2 rounded-r";default:return"text-gray-900"}};return(0,b.jsxs)("div",{className:"group relative flex items-start space-x-2 py-1",children:[(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)("button",{onClick:()=>n(!m),className:"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity",title:"Change block type",children:(a=>{switch(a){case"HEADING":return"📰";case"BULLET_LIST":return"•";case"NUMBERED_LIST":return"1.";case"CODE":return"💻";case"QUOTE":return"💬";default:return"📝"}})(a.type)}),m&&(0,b.jsx)("div",{className:"absolute left-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10",children:(0,b.jsxs)("div",{className:"py-1",children:[(0,b.jsx)("button",{onClick:()=>t("TEXT"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📝 Text"}),(0,b.jsx)("button",{onClick:()=>t("HEADING"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📰 Heading"}),(0,b.jsx)("button",{onClick:()=>t("BULLET_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"• Bullet List"}),(0,b.jsx)("button",{onClick:()=>t("NUMBERED_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"1. Numbered List"}),(0,b.jsx)("button",{onClick:()=>t("CODE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💻 Code"}),(0,b.jsx)("button",{onClick:()=>t("QUOTE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💬 Quote"})]})})]}),(0,b.jsx)("div",{className:"flex-1 min-w-0",children:(()=>{if(k){let c="CODE"===a.type?"w-full border-none outline-none resize-none code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto":`w-full border-none outline-none resize-none ${u(a.type)} bg-transparent`;return(0,b.jsx)("textarea",{ref:p,value:i,onChange:a=>j(a.target.value),onKeyDown:r,onBlur:q,className:c,placeholder:"CODE"===a.type?"Enter your code here...":"Type something...",rows:"CODE"===a.type?3:1})}if(!i.trim())return(0,b.jsx)("div",{onClick:()=>l(!0),className:"text-gray-400 cursor-text py-2",children:"Type something..."});let c="BULLET_LIST"===a.type?i.split("\n").map(a=>a.trim()?`• ${a}`:a).join("\n"):"NUMBERED_LIST"===a.type?i.split("\n").map((a,b)=>a.trim()?`${b+1}. ${a}`:a).join("\n"):i;return(0,b.jsx)("div",{onClick:()=>l(!0),className:`cursor-text py-2 whitespace-pre-wrap ${u(a.type)}`,children:c})})()}),(0,b.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,b.jsx)("button",{onClick:s,className:"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500",title:"Delete block",children:(0,b.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})})]})}function s({note:a}){let{selectedNote:d,updateNote:e,createBlock:f,loadNote:g,loadingNote:h}=o(),[i,j]=(0,c.useState)(!1),[k,l]=(0,c.useState)(a.title),[m,n]=(0,c.useState)(a.description||""),p=d||a;(0,c.useEffect)(()=>{a.id&&g(a.id)},[a.id]);let q=async()=>{try{await e(p.id,{title:k,description:m||void 0}),j(!1)}catch(a){console.error("Failed to update note:",a),alert("Failed to update note")}},s=async(a="TEXT")=>{try{let b=p.blocks.length;await f(p.id,a,"",b)}catch(a){console.error("Failed to create block:",a),alert("Failed to create block")}};return(0,b.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,b.jsxs)("div",{className:"bg-white border-b border-gray-200 p-6",children:[i?(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsx)("input",{type:"text",value:k,onChange:a=>l(a.target.value),className:"text-2xl font-bold text-gray-900 w-full border-none outline-none bg-transparent",placeholder:"Note title",autoFocus:!0}),(0,b.jsx)("textarea",{value:m,onChange:a=>n(a.target.value),className:"text-gray-600 w-full border-none outline-none bg-transparent resize-none",placeholder:"Add a description...",rows:2}),(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)("button",{onClick:q,className:"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm",children:"Save"}),(0,b.jsx)("button",{onClick:()=>{l(p.title),n(p.description||""),j(!1)},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm",children:"Cancel"})]})]}):(0,b.jsxs)("div",{onClick:()=>j(!0),className:"cursor-pointer",children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2 hover:bg-gray-50 p-2 rounded",children:p.title}),p.description&&(0,b.jsx)("p",{className:"text-gray-600 hover:bg-gray-50 p-2 rounded",children:p.description})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between mt-4 text-sm text-gray-500",children:[(0,b.jsxs)("div",{children:["Created by ",(0,b.jsx)("span",{className:"font-medium",children:p.author.name||p.author.username})]}),(0,b.jsxs)("div",{children:["Last updated ",new Date(p.updatedAt).toLocaleDateString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),(0,b.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,b.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(h||!p.blocks)&&(0,b.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,b.jsx)("span",{className:"ml-2 text-gray-500",children:"Loading note content..."})]}),p.blocks&&(0,b.jsx)("div",{className:"space-y-2",children:p.blocks.map((a,c)=>(0,b.jsx)(r,{block:a,noteId:p.id,isLast:c===p.blocks.length-1,onAddBlock:s},a.id))}),p.blocks&&(0,b.jsx)("div",{className:"mt-4",children:(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)("button",{onClick:()=>s("TEXT"),className:"flex items-center space-x-2 text-gray-500 hover:text-gray-700 p-2 rounded hover:bg-gray-100",children:[(0,b.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,b.jsx)("span",{className:"text-sm",children:"Add block"})]}),(0,b.jsxs)("div",{className:"relative group",children:[(0,b.jsx)("button",{className:"text-gray-400 hover:text-gray-600 p-1",children:(0,b.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})}),(0,b.jsx)("div",{className:"absolute left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10",children:(0,b.jsxs)("div",{className:"py-1",children:[(0,b.jsx)("button",{onClick:()=>s("TEXT"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📝 Text"}),(0,b.jsx)("button",{onClick:()=>s("HEADING"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📰 Heading"}),(0,b.jsx)("button",{onClick:()=>s("BULLET_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"• Bullet List"}),(0,b.jsx)("button",{onClick:()=>s("NUMBERED_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"1. Numbered List"}),(0,b.jsx)("button",{onClick:()=>s("CODE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💻 Code"}),(0,b.jsx)("button",{onClick:()=>s("QUOTE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💬 Quote"})]})})]})]})})]})})]})}function t({group:a}){let{notes:d,selectedNote:e,loading:f,loadNotes:g,selectNote:h,deleteNote:i}=o(),[j,k]=(0,c.useState)(!1),[l,m]=(0,c.useState)(!0);(0,c.useEffect)(()=>{a&&g(a.id)},[a.id]);let n=async a=>{if(confirm("Are you sure you want to delete this note?"))try{await i(a)}catch(a){console.error("Failed to delete note:",a),alert("Failed to delete note")}};return e&&!l?(0,b.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,b.jsx)("div",{className:"bg-white border-b border-gray-200 p-4",children:(0,b.jsx)("button",{onClick:()=>m(!0),className:"text-blue-500 hover:text-blue-600 text-sm mb-2",children:"← Back to Notes"})}),(0,b.jsx)(s,{note:e})]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,b.jsx)("div",{className:"bg-white border-b border-gray-200 p-4",children:(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Notes"}),(0,b.jsx)("button",{onClick:()=>k(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm",children:"New Note"})]})}),(0,b.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:f?(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,b.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading notes..."})]}):0===d.length?(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("svg",{className:"w-16 h-16 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Notes Yet"}),(0,b.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your first note to start collaborating"}),(0,b.jsx)("button",{onClick:()=>k(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm",children:"Create Note"})]}):(0,b.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:d.map(a=>(0,b.jsx)("div",{className:"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200",onClick:()=>{h(a),m(!1)},children:(0,b.jsxs)("div",{className:"p-4",children:[(0,b.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 truncate",children:a.title}),(0,b.jsx)("button",{onClick:b=>{b.stopPropagation(),n(a.id)},className:"text-gray-400 hover:text-red-500 p-1",title:"Delete note",children:(0,b.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}),a.description&&(0,b.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:a.description}),(0,b.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"font-medium",children:a.author.name||a.author.username}),(0,b.jsx)("span",{className:"mx-1",children:"•"}),(0,b.jsxs)("span",{children:[a._count.blocks," blocks"]})]}),(0,b.jsx)("span",{children:new Date(a.updatedAt).toLocaleDateString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]})},a.id))})})]}),(0,b.jsx)(q,{isOpen:j,onClose:()=>k(!1),groupId:a.id})]})}function u(){let{selectedGroup:a,addMember:d}=h(),{user:f}=(0,e.useAuth)(),[g,i]=(0,c.useState)(!1),[j,k]=(0,c.useState)(""),[m,o]=(0,c.useState)("MEMBER"),[q,r]=(0,c.useState)(!1),[s,u]=(0,c.useState)(""),[v,w]=(0,c.useState)("chat");if(!a)return(0,b.jsx)("div",{className:"flex-1 flex items-center justify-center bg-gray-50",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("svg",{className:"w-16 h-16 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Group"}),(0,b.jsx)("p",{className:"text-gray-500",children:"Choose a group from the sidebar to view details and start chatting"})]})});let x=a.ownerId===f?.id||a.members.some(a=>a.userId===f?.id&&["OWNER","ADMIN"].includes(a.role)),y=async b=>{b.preventDefault(),u(""),r(!0);try{await d(a.id,j,m),k(""),o("MEMBER"),i(!1)}catch(a){u(a instanceof Error?a.message:"Failed to add member")}finally{r(!1)}};return(0,b.jsx)(l,{children:(0,b.jsx)(n,{children:(0,b.jsxs)("div",{className:"flex-1 flex flex-col bg-gray-50",children:[(0,b.jsxs)("div",{className:"bg-white border-b border-gray-200 p-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:a.name}),a.isPrivate&&(0,b.jsx)("svg",{className:"w-4 h-4 text-gray-400 ml-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"})})]}),a.description&&(0,b.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a.description})]}),x&&(0,b.jsx)("button",{onClick:()=>i(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm",children:"Add Member"})]}),(0,b.jsx)("div",{className:"mt-4",children:(0,b.jsxs)("nav",{className:"flex space-x-8",children:[(0,b.jsx)("button",{onClick:()=>w("chat"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"chat"===v?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Chat"}),(0,b.jsxs)("button",{onClick:()=>w("members"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"members"===v?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Members (",a._count.members,")"]}),(0,b.jsxs)("button",{onClick:()=>w("notes"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"notes"===v?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Notes (",a._count.notes,")"]})]})})]}),(0,b.jsxs)("div",{className:"flex-1 flex flex-col",children:["chat"===v&&(0,b.jsx)(p,{group:a}),"members"===v&&(0,b.jsx)("div",{className:"flex-1 p-6 overflow-y-auto",children:(0,b.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,b.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-8",children:[(0,b.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a._count.members}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Members"})]}),(0,b.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a._count.messages}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Messages"})]}),(0,b.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:a._count.notes}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Notes"})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,b.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Members"})}),(0,b.jsx)("div",{className:"p-4",children:(0,b.jsx)("div",{className:"space-y-3",children:a.members.map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,b.jsx)("span",{className:"text-sm font-medium text-gray-700",children:(a.user.name||a.user.username).charAt(0).toUpperCase()})}),(0,b.jsxs)("div",{className:"ml-3",children:[(0,b.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.user.name||a.user.username}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:a.user.email})]})]}),(0,b.jsx)("div",{className:"flex items-center space-x-2",children:(0,b.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${"OWNER"===a.role?"bg-green-100 text-green-800":"ADMIN"===a.role?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:a.role})})]},a.id))})})]})]})}),"notes"===v&&(0,b.jsx)(t,{group:a})]}),g&&(0,b.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,b.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add Member"}),(0,b.jsx)("button",{onClick:()=>i(!1),className:"text-gray-400 hover:text-gray-600",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s&&(0,b.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:s}),(0,b.jsxs)("form",{onSubmit:y,children:[(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Email Address"}),(0,b.jsx)("input",{type:"email",value:j,onChange:a=>k(a.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter user's email",required:!0})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Role"}),(0,b.jsxs)("select",{value:m,onChange:a=>o(a.target.value),className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",children:[(0,b.jsx)("option",{value:"MEMBER",children:"Member"}),(0,b.jsx)("option",{value:"ADMIN",children:"Admin"})]})]}),(0,b.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,b.jsx)("button",{type:"button",onClick:()=>i(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50",children:"Cancel"}),(0,b.jsx)("button",{type:"submit",disabled:q,className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:q?"Adding...":"Add Member"})]})]})]})})]})})})}function v(){let{user:a,loading:f,logout:h}=(0,e.useAuth)(),i=(0,d.useRouter)();if((0,c.useEffect)(()=>{f||a||i.push("/auth")},[a,f,i]),f)return(0,b.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,b.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});if(!a)return null;let k=async()=>{await h(),i.push("/auth")};return(0,b.jsx)(g,{children:(0,b.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[(0,b.jsx)("header",{className:"bg-white shadow",children:(0,b.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,b.jsx)("div",{className:"flex items-center",children:(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"MyBinder"})}),(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsxs)("div",{className:"text-sm text-gray-700",children:["Welcome, ",(0,b.jsx)("span",{className:"font-medium",children:a.name||a.username})]}),(0,b.jsx)("button",{onClick:k,className:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm",children:"Logout"})]})]})})}),(0,b.jsxs)("div",{className:"flex-1 flex",children:[(0,b.jsx)(j,{}),(0,b.jsx)(u,{})]})]})})}}];

//# sourceMappingURL=src_app_dashboard_page_tsx_196c74b5._.js.map