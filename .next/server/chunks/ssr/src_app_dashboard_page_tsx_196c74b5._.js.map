{"version": 3, "sources": ["turbopack:///[project]/src/app/dashboard/page.tsx", "turbopack:///[project]/src/contexts/GroupContext.tsx", "turbopack:///[project]/src/components/groups/CreateGroupModal.tsx", "turbopack:///[project]/src/components/groups/GroupList.tsx", "turbopack:///[project]/src/contexts/MessageContext.tsx", "turbopack:///[project]/src/contexts/NotesContext.tsx", "turbopack:///[project]/src/components/messages/ChatInterface.tsx", "turbopack:///[project]/src/components/notes/CreateNoteModal.tsx", "turbopack:///[project]/src/components/notes/BlockEditor.tsx", "turbopack:///[project]/src/components/notes/NoteEditor.tsx", "turbopack:///[project]/src/components/notes/NotesInterface.tsx", "turbopack:///[project]/src/components/groups/GroupDetail.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { GroupProvider } from '@/contexts/GroupContext'\nimport GroupList from '@/components/groups/GroupList'\nimport GroupDetail from '@/components/groups/GroupDetail'\n\nexport default function DashboardPage() {\n  const { user, loading, logout } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect to auth\n  }\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/auth')\n  }\n\n  return (\n    <GroupProvider>\n      <div className=\"min-h-screen bg-gray-50 flex flex-col\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-4\">\n              <div className=\"flex items-center\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">MyBinder</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-sm text-gray-700\">\n                  Welcome, <span className=\"font-medium\">{user.name || user.username}</span>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm\"\n                >\n                  Logout\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex\">\n          <GroupList />\n          <GroupDetail />\n        </div>\n      </div>\n    </GroupProvider>\n  )\n}\n", "'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { GroupWithMembers, CreateGroupData } from '@/types'\n\ninterface GroupContextType {\n  groups: GroupWithMembers[]\n  selectedGroup: GroupWithMembers | null\n  loading: boolean\n  createGroup: (data: CreateGroupData) => Promise<GroupWithMembers>\n  updateGroup: (groupId: string, data: Partial<CreateGroupData>) => Promise<GroupWithMembers>\n  deleteGroup: (groupId: string) => Promise<void>\n  selectGroup: (group: GroupWithMembers | null) => void\n  refreshGroups: () => Promise<void>\n  addMember: (groupId: string, email: string, role?: 'MEMBER' | 'ADMIN') => Promise<void>\n}\n\nconst GroupContext = createContext<GroupContextType | undefined>(undefined)\n\nexport function GroupProvider({ children }: { children: React.ReactNode }) {\n  const [groups, setGroups] = useState<GroupWithMembers[]>([])\n  const [selectedGroup, setSelectedGroup] = useState<GroupWithMembers | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  const refreshGroups = async () => {\n    try {\n      const response = await fetch('/api/groups')\n      if (response.ok) {\n        const data = await response.json()\n        setGroups(data.groups)\n      } else {\n        console.error('Failed to fetch groups')\n      }\n    } catch (error) {\n      console.error('Failed to refresh groups:', error)\n    }\n  }\n\n  const createGroup = async (data: CreateGroupData): Promise<GroupWithMembers> => {\n    const response = await fetch('/api/groups', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to create group')\n    }\n\n    const result = await response.json()\n    await refreshGroups()\n    return result.group\n  }\n\n  const updateGroup = async (groupId: string, data: Partial<CreateGroupData>): Promise<GroupWithMembers> => {\n    const response = await fetch(`/api/groups/${groupId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to update group')\n    }\n\n    const result = await response.json()\n    await refreshGroups()\n    \n    // Update selected group if it's the one being updated\n    if (selectedGroup?.id === groupId) {\n      setSelectedGroup(result.group)\n    }\n    \n    return result.group\n  }\n\n  const deleteGroup = async (groupId: string): Promise<void> => {\n    const response = await fetch(`/api/groups/${groupId}`, {\n      method: 'DELETE',\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to delete group')\n    }\n\n    // Clear selected group if it's the one being deleted\n    if (selectedGroup?.id === groupId) {\n      setSelectedGroup(null)\n    }\n    \n    await refreshGroups()\n  }\n\n  const selectGroup = (group: GroupWithMembers | null) => {\n    setSelectedGroup(group)\n  }\n\n  const addMember = async (groupId: string, email: string, role: 'MEMBER' | 'ADMIN' = 'MEMBER'): Promise<void> => {\n    const response = await fetch(`/api/groups/${groupId}/members`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email, role }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to add member')\n    }\n\n    await refreshGroups()\n    \n    // Refresh selected group if it's the one being updated\n    if (selectedGroup?.id === groupId) {\n      const groupResponse = await fetch(`/api/groups/${groupId}`)\n      if (groupResponse.ok) {\n        const groupData = await groupResponse.json()\n        setSelectedGroup(groupData.group)\n      }\n    }\n  }\n\n  useEffect(() => {\n    refreshGroups().finally(() => setLoading(false))\n  }, [])\n\n  return (\n    <GroupContext.Provider value={{\n      groups,\n      selectedGroup,\n      loading,\n      createGroup,\n      updateGroup,\n      deleteGroup,\n      selectGroup,\n      refreshGroups,\n      addMember,\n    }}>\n      {children}\n    </GroupContext.Provider>\n  )\n}\n\nexport function useGroups() {\n  const context = useContext(GroupContext)\n  if (context === undefined) {\n    throw new Error('useGroups must be used within a GroupProvider')\n  }\n  return context\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useGroups } from '@/contexts/GroupContext'\n\ninterface CreateGroupModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport default function CreateGroupModal({ isOpen, onClose }: CreateGroupModalProps) {\n  const [name, setName] = useState('')\n  const [description, setDescription] = useState('')\n  const [isPrivate, setIsPrivate] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const { createGroup } = useGroups()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await createGroup({\n        name,\n        description: description || undefined,\n        isPrivate,\n      })\n      \n      // Reset form and close modal\n      setName('')\n      setDescription('')\n      setIsPrivate(false)\n      onClose()\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create group')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setName('')\n      setDescription('')\n      setIsPrivate(false)\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md mx-4\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-bold text-gray-900\">Create New Group</h2>\n          <button\n            onClick={handleClose}\n            disabled={loading}\n            className=\"text-gray-400 hover:text-gray-600 disabled:opacity-50\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"name\">\n              Group Name *\n            </label>\n            <input\n              id=\"name\"\n              type=\"text\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter group name\"\n              required\n              maxLength={100}\n            />\n          </div>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"description\">\n              Description\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter group description (optional)\"\n              rows={3}\n              maxLength={500}\n            />\n          </div>\n\n          <div className=\"mb-6\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={isPrivate}\n                onChange={(e) => setIsPrivate(e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-gray-700 text-sm\">Private Group</span>\n            </label>\n            <p className=\"text-xs text-gray-500 mt-1\">\n              Private groups require invitation to join\n            </p>\n          </div>\n\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              disabled={loading}\n              className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || !name.trim()}\n              className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Creating...' : 'Create Group'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useGroups } from '@/contexts/GroupContext'\nimport { useAuth } from '@/contexts/AuthContext'\nimport CreateGroupModal from './CreateGroupModal'\n\nexport default function GroupList() {\n  const { groups, selectedGroup, selectGroup, loading } = useGroups()\n  const { user } = useAuth()\n  const [showCreateModal, setShowCreateModal] = useState(false)\n\n  if (loading) {\n    return (\n      <div className=\"w-80 bg-white border-r border-gray-200 p-4\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-4\"></div>\n          <div className=\"space-y-3\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <div className=\"w-80 bg-white border-r border-gray-200 flex flex-col\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Groups</h2>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full\"\n              title=\"Create new group\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Groups List */}\n        <div className=\"flex-1 overflow-y-auto\">\n          {groups.length === 0 ? (\n            <div className=\"p-4 text-center text-gray-500\">\n              <p className=\"mb-2\">No groups yet</p>\n              <button\n                onClick={() => setShowCreateModal(true)}\n                className=\"text-blue-500 hover:text-blue-600 text-sm\"\n              >\n                Create your first group\n              </button>\n            </div>\n          ) : (\n            <div className=\"p-2\">\n              {groups.map((group) => (\n                <div\n                  key={group.id}\n                  onClick={() => selectGroup(group)}\n                  className={`p-3 rounded-lg cursor-pointer mb-2 transition-colors ${\n                    selectedGroup?.id === group.id\n                      ? 'bg-blue-50 border border-blue-200'\n                      : 'hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center\">\n                        <h3 className=\"text-sm font-medium text-gray-900 truncate\">\n                          {group.name}\n                        </h3>\n                        {group.isPrivate && (\n                          <svg className=\"w-3 h-3 text-gray-400 ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                            <path fillRule=\"evenodd\" d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\" clipRule=\"evenodd\" />\n                          </svg>\n                        )}\n                      </div>\n                      {group.description && (\n                        <p className=\"text-xs text-gray-500 truncate mt-1\">\n                          {group.description}\n                        </p>\n                      )}\n                      <div className=\"flex items-center mt-2 text-xs text-gray-400\">\n                        <span>{group._count.members} members</span>\n                        <span className=\"mx-1\">•</span>\n                        <span>{group._count.messages} messages</span>\n                        <span className=\"mx-1\">•</span>\n                        <span>{group._count.notes} notes</span>\n                      </div>\n                    </div>\n                    {group.ownerId === user?.id && (\n                      <div className=\"ml-2\">\n                        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                          Owner\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      <CreateGroupModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n      />\n    </>\n  )\n}\n", "'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { MessageWithAuthor } from '@/types'\n\ninterface MessageContextType {\n  messages: MessageWithAuthor[]\n  loading: boolean\n  sending: boolean\n  sendMessage: (groupId: string, content: string) => Promise<void>\n  loadMessages: (groupId: string, page?: number) => Promise<void>\n  clearMessages: () => void\n  hasMore: boolean\n  currentPage: number\n}\n\nconst MessageContext = createContext<MessageContextType | undefined>(undefined)\n\nexport function MessageProvider({ children }: { children: React.ReactNode }) {\n  const [messages, setMessages] = useState<MessageWithAuthor[]>([])\n  const [loading, setLoading] = useState(false)\n  const [sending, setSending] = useState(false)\n  const [hasMore, setHasMore] = useState(false)\n  const [currentPage, setCurrentPage] = useState(1)\n  const [currentGroupId, setCurrentGroupId] = useState<string | null>(null)\n\n  const loadMessages = async (groupId: string, page: number = 1) => {\n    if (groupId !== currentGroupId) {\n      // Reset state when switching groups\n      setMessages([])\n      setCurrentPage(1)\n      setCurrentGroupId(groupId)\n      page = 1\n    }\n\n    setLoading(true)\n    try {\n      const response = await fetch(`/api/groups/${groupId}/messages?page=${page}&limit=50`)\n      if (response.ok) {\n        const data = await response.json()\n        \n        if (page === 1) {\n          setMessages(data.messages)\n        } else {\n          // Append older messages for pagination\n          setMessages(prev => [...data.messages, ...prev])\n        }\n        \n        setHasMore(data.pagination.hasMore)\n        setCurrentPage(page)\n      } else {\n        console.error('Failed to load messages')\n      }\n    } catch (error) {\n      console.error('Failed to load messages:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const sendMessage = async (groupId: string, content: string) => {\n    setSending(true)\n    try {\n      const response = await fetch(`/api/groups/${groupId}/messages`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ content }),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to send message')\n      }\n\n      const result = await response.json()\n      \n      // Add new message to the end of the list\n      setMessages(prev => [...prev, result.data])\n    } catch (error) {\n      console.error('Failed to send message:', error)\n      throw error\n    } finally {\n      setSending(false)\n    }\n  }\n\n  const clearMessages = () => {\n    setMessages([])\n    setCurrentPage(1)\n    setHasMore(false)\n    setCurrentGroupId(null)\n  }\n\n  return (\n    <MessageContext.Provider value={{\n      messages,\n      loading,\n      sending,\n      sendMessage,\n      loadMessages,\n      clearMessages,\n      hasMore,\n      currentPage,\n    }}>\n      {children}\n    </MessageContext.Provider>\n  )\n}\n\nexport function useMessages() {\n  const context = useContext(MessageContext)\n  if (context === undefined) {\n    throw new Error('useMessages must be used within a MessageProvider')\n  }\n  return context\n}\n", "'use client'\n\nimport React, { createContext, useContext, useState } from 'react'\nimport { NoteWithBlocks, CreateNoteData, NoteBlockWithAuthor } from '@/types'\n\ninterface NotesContextType {\n  notes: NoteWithBlocks[]\n  selectedNote: NoteWithBlocks | null\n  loading: boolean\n  loadingNote: boolean\n  createNote: (groupId: string, data: CreateNoteData) => Promise<NoteWithBlocks>\n  updateNote: (noteId: string, data: Partial<CreateNoteData>) => Promise<NoteWithBlocks>\n  deleteNote: (noteId: string) => Promise<void>\n  selectNote: (note: NoteWithBlocks | null) => void\n  loadNotes: (groupId: string) => Promise<void>\n  loadNote: (noteId: string) => Promise<void>\n  clearNotes: () => void\n  // Block operations\n  createBlock: (noteId: string, type: string, content: string, order: number) => Promise<NoteBlockWithAuthor>\n  updateBlock: (blockId: string, data: { content?: string; type?: string }) => Promise<NoteBlockWithAuthor>\n  deleteBlock: (blockId: string) => Promise<void>\n  reorderBlocks: (noteId: string, blocks: { id: string; order: number }[]) => Promise<void>\n}\n\nconst NotesContext = createContext<NotesContextType | undefined>(undefined)\n\nexport function NotesProvider({ children }: { children: React.ReactNode }) {\n  const [notes, setNotes] = useState<NoteWithBlocks[]>([])\n  const [selectedNote, setSelectedNote] = useState<NoteWithBlocks | null>(null)\n  const [loading, setLoading] = useState(false)\n  const [loadingNote, setLoadingNote] = useState(false)\n\n  const loadNotes = async (groupId: string) => {\n    setLoading(true)\n    try {\n      const response = await fetch(`/api/groups/${groupId}/notes`)\n      if (response.ok) {\n        const data = await response.json()\n        setNotes(data.notes)\n      } else {\n        console.error('Failed to load notes')\n      }\n    } catch (error) {\n      console.error('Failed to load notes:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadNote = async (noteId: string) => {\n    setLoadingNote(true)\n    try {\n      const response = await fetch(`/api/notes/${noteId}`)\n      if (response.ok) {\n        const data = await response.json()\n        setSelectedNote(data.note)\n      } else {\n        console.error('Failed to load note')\n      }\n    } catch (error) {\n      console.error('Failed to load note:', error)\n    } finally {\n      setLoadingNote(false)\n    }\n  }\n\n  const createNote = async (groupId: string, data: CreateNoteData): Promise<NoteWithBlocks> => {\n    const response = await fetch(`/api/groups/${groupId}/notes`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to create note')\n    }\n\n    const result = await response.json()\n    await loadNotes(groupId)\n    return result.note\n  }\n\n  const updateNote = async (noteId: string, data: Partial<CreateNoteData>): Promise<NoteWithBlocks> => {\n    const response = await fetch(`/api/notes/${noteId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to update note')\n    }\n\n    const result = await response.json()\n    \n    // Update selected note if it's the one being updated\n    if (selectedNote?.id === noteId) {\n      setSelectedNote(result.note)\n    }\n    \n    // Update notes list\n    setNotes(prev => prev.map(note => \n      note.id === noteId ? { ...note, ...data } : note\n    ))\n    \n    return result.note\n  }\n\n  const deleteNote = async (noteId: string): Promise<void> => {\n    const response = await fetch(`/api/notes/${noteId}`, {\n      method: 'DELETE',\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to delete note')\n    }\n\n    // Clear selected note if it's the one being deleted\n    if (selectedNote?.id === noteId) {\n      setSelectedNote(null)\n    }\n    \n    // Remove from notes list\n    setNotes(prev => prev.filter(note => note.id !== noteId))\n  }\n\n  const selectNote = (note: NoteWithBlocks | null) => {\n    setSelectedNote(note)\n    // Load full note details with blocks if note is provided\n    if (note?.id) {\n      loadNote(note.id)\n    }\n  }\n\n  const clearNotes = () => {\n    setNotes([])\n    setSelectedNote(null)\n  }\n\n  // Block operations\n  const createBlock = async (noteId: string, type: string, content: string, order: number): Promise<NoteBlockWithAuthor> => {\n    const response = await fetch(`/api/notes/${noteId}/blocks`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ type, content, order }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to create block')\n    }\n\n    const result = await response.json()\n    \n    // Refresh the selected note to get updated blocks\n    if (selectedNote?.id === noteId) {\n      await loadNote(noteId)\n    }\n    \n    return result.block\n  }\n\n  const updateBlock = async (blockId: string, data: { content?: string; type?: string }): Promise<NoteBlockWithAuthor> => {\n    const response = await fetch(`/api/blocks/${blockId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to update block')\n    }\n\n    const result = await response.json()\n    \n    // Update the block in selected note\n    if (selectedNote) {\n      setSelectedNote(prev => prev ? {\n        ...prev,\n        blocks: prev.blocks.map(block => \n          block.id === blockId ? { ...block, ...data } : block\n        )\n      } : null)\n    }\n    \n    return result.block\n  }\n\n  const deleteBlock = async (blockId: string): Promise<void> => {\n    const response = await fetch(`/api/blocks/${blockId}`, {\n      method: 'DELETE',\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to delete block')\n    }\n\n    // Remove block from selected note\n    if (selectedNote) {\n      setSelectedNote(prev => prev ? {\n        ...prev,\n        blocks: prev.blocks.filter(block => block.id !== blockId)\n      } : null)\n    }\n  }\n\n  const reorderBlocks = async (noteId: string, blocks: { id: string; order: number }[]): Promise<void> => {\n    const response = await fetch(`/api/notes/${noteId}/blocks`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ blocks }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to reorder blocks')\n    }\n\n    // Refresh the selected note to get updated order\n    if (selectedNote?.id === noteId) {\n      await loadNote(noteId)\n    }\n  }\n\n  return (\n    <NotesContext.Provider value={{\n      notes,\n      selectedNote,\n      loading,\n      loadingNote,\n      createNote,\n      updateNote,\n      deleteNote,\n      selectNote,\n      loadNotes,\n      loadNote,\n      clearNotes,\n      createBlock,\n      updateBlock,\n      deleteBlock,\n      reorderBlocks,\n    }}>\n      {children}\n    </NotesContext.Provider>\n  )\n}\n\nexport function useNotes() {\n  const context = useContext(NotesContext)\n  if (context === undefined) {\n    throw new Error('useNotes must be used within a NotesProvider')\n  }\n  return context\n}\n", "'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useMessages } from '@/contexts/MessageContext'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { GroupWithMembers } from '@/types'\n\ninterface ChatInterfaceProps {\n  group: GroupWithMembers\n}\n\nexport default function ChatInterface({ group }: ChatInterfaceProps) {\n  const { messages, loading, sending, sendMessage, loadMessages, hasMore, currentPage } = useMessages()\n  const { user } = useAuth()\n  const [newMessage, setNewMessage] = useState('')\n  const [error, setError] = useState('')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const messagesContainerRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    if (group) {\n      loadMessages(group.id)\n    }\n  }, [group.id])\n\n  useEffect(() => {\n    // Scroll to bottom when new messages arrive\n    scrollToBottom()\n  }, [messages])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newMessage.trim() || sending) return\n\n    setError('')\n    const messageContent = newMessage.trim()\n    setNewMessage('')\n\n    try {\n      await sendMessage(group.id, messageContent)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to send message')\n      setNewMessage(messageContent) // Restore message on error\n    }\n  }\n\n  const loadMoreMessages = async () => {\n    if (hasMore && !loading) {\n      await loadMessages(group.id, currentPage + 1)\n    }\n  }\n\n  const formatTime = (date: string | Date) => {\n    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n  }\n\n  const formatDate = (date: string | Date) => {\n    const messageDate = new Date(date)\n    const today = new Date()\n    const yesterday = new Date(today)\n    yesterday.setDate(yesterday.getDate() - 1)\n\n    if (messageDate.toDateString() === today.toDateString()) {\n      return 'Today'\n    } else if (messageDate.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday'\n    } else {\n      return messageDate.toLocaleDateString()\n    }\n  }\n\n  const shouldShowDateSeparator = (currentMessage: any, previousMessage: any) => {\n    if (!previousMessage) return true\n    \n    const currentDate = new Date(currentMessage.createdAt).toDateString()\n    const previousDate = new Date(previousMessage.createdAt).toDateString()\n    \n    return currentDate !== previousDate\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Messages Container */}\n      <div \n        ref={messagesContainerRef}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4\"\n      >\n        {/* Load More Button */}\n        {hasMore && (\n          <div className=\"text-center\">\n            <button\n              onClick={loadMoreMessages}\n              disabled={loading}\n              className=\"text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50\"\n            >\n              {loading ? 'Loading...' : 'Load older messages'}\n            </button>\n          </div>\n        )}\n\n        {/* Messages */}\n        {messages.map((message, index) => {\n          const previousMessage = index > 0 ? messages[index - 1] : null\n          const showDateSeparator = shouldShowDateSeparator(message, previousMessage)\n          const isOwnMessage = message.authorId === user?.id\n\n          return (\n            <div key={message.id}>\n              {/* Date Separator */}\n              {showDateSeparator && (\n                <div className=\"flex items-center justify-center my-4\">\n                  <div className=\"bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full\">\n                    {formatDate(message.createdAt)}\n                  </div>\n                </div>\n              )}\n\n              {/* Message */}\n              <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>\n                <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>\n                  {!isOwnMessage && (\n                    <div className=\"text-xs text-gray-700 mb-1 font-medium\">\n                      {message.author.name || message.author.username}\n                    </div>\n                  )}\n                  <div\n                    className={`px-4 py-2 rounded-lg ${\n                      isOwnMessage\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-900 border border-gray-200'\n                    }`}\n                  >\n                    <div className=\"text-sm whitespace-pre-wrap\">\n                      {message.content.includes('```') ? (\n                        <div>\n                          {message.content.split('```').map((part, index) =>\n                            index % 2 === 0 ? (\n                              <span key={index}>{part}</span>\n                            ) : (\n                              <code key={index} className=\"block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto\">\n                                {part}\n                              </code>\n                            )\n                          )}\n                        </div>\n                      ) : (\n                        message.content\n                      )}\n                    </div>\n                    <div className={`text-xs mt-1 ${isOwnMessage ? 'text-blue-100' : 'text-gray-600'}`}>\n                      {formatTime(message.createdAt)}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n\n        {/* Loading indicator */}\n        {loading && messages.length === 0 && (\n          <div className=\"text-center text-gray-500\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"></div>\n            <p className=\"mt-2\">Loading messages...</p>\n          </div>\n        )}\n\n        {/* Empty state */}\n        {!loading && messages.length === 0 && (\n          <div className=\"text-center text-gray-500 py-8\">\n            <p>No messages yet. Start the conversation!</p>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message Input */}\n      <div className=\"border-t border-gray-200 p-4\">\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm\">\n            {error}\n          </div>\n        )}\n        \n        <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            placeholder=\"Type a message...\"\n            className=\"flex-1 border border-gray-300 rounded-lg px-4 py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500\"\n            disabled={sending}\n            maxLength={2000}\n          />\n          <button\n            type=\"submit\"\n            disabled={!newMessage.trim() || sending}\n            className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {sending ? 'Sending...' : 'Send'}\n          </button>\n        </form>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useNotes } from '@/contexts/NotesContext'\n\ninterface CreateNoteModalProps {\n  isOpen: boolean\n  onClose: () => void\n  groupId: string\n}\n\nexport default function CreateNoteModal({ isOpen, onClose, groupId }: CreateNoteModalProps) {\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const { createNote } = useNotes()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await createNote(groupId, {\n        title,\n        description: description || undefined,\n      })\n      \n      // Reset form and close modal\n      setTitle('')\n      setDescription('')\n      onClose()\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create note')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setTitle('')\n      setDescription('')\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md mx-4\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-bold text-gray-900\">Create New Note</h2>\n          <button\n            onClick={handleClose}\n            disabled={loading}\n            className=\"text-gray-400 hover:text-gray-600 disabled:opacity-50\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"title\">\n              Note Title *\n            </label>\n            <input\n              id=\"title\"\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter note title\"\n              required\n              maxLength={200}\n            />\n          </div>\n\n          <div className=\"mb-6\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"description\">\n              Description\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter note description (optional)\"\n              rows={3}\n              maxLength={500}\n            />\n          </div>\n\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              disabled={loading}\n              className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || !title.trim()}\n              className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Creating...' : 'Create Note'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useNotes } from '@/contexts/NotesContext'\nimport { NoteBlockWithAuthor } from '@/types'\n\ninterface BlockEditorProps {\n  block: NoteBlockWithAuthor\n  noteId: string\n  isLast: boolean\n  onAddBlock: (type?: string) => void\n}\n\nexport default function BlockEditor({ block, noteId, isLast, onAddBlock }: BlockEditorProps) {\n  const { updateBlock, deleteBlock } = useNotes()\n  const [content, setContent] = useState(block.content)\n  const [isEditing, setIsEditing] = useState(false)\n  const [showMenu, setShowMenu] = useState(false)\n  const textareaRef = useRef<HTMLTextAreaElement>(null)\n\n  useEffect(() => {\n    setContent(block.content)\n  }, [block.content])\n\n  useEffect(() => {\n    if (isEditing && textareaRef.current) {\n      textareaRef.current.focus()\n      // Auto-resize textarea\n      textareaRef.current.style.height = 'auto'\n      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'\n    }\n  }, [isEditing])\n\n  const handleSave = async () => {\n    try {\n      await updateBlock(block.id, { content })\n      setIsEditing(false)\n    } catch (error) {\n      console.error('Failed to update block:', error)\n      alert('Failed to update block')\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      if (isLast && content.trim()) {\n        onAddBlock('TEXT')\n      } else {\n        handleSave()\n      }\n    } else if (e.key === 'Escape') {\n      setContent(block.content)\n      setIsEditing(false)\n    }\n  }\n\n  const handleDelete = async () => {\n    if (confirm('Are you sure you want to delete this block?')) {\n      try {\n        await deleteBlock(block.id)\n      } catch (error) {\n        console.error('Failed to delete block:', error)\n        alert('Failed to delete block')\n      }\n    }\n  }\n\n  const handleTypeChange = async (newType: string) => {\n    try {\n      await updateBlock(block.id, { type: newType })\n      setShowMenu(false)\n    } catch (error) {\n      console.error('Failed to update block type:', error)\n      alert('Failed to update block type')\n    }\n  }\n\n  const getBlockIcon = (type: string) => {\n    switch (type) {\n      case 'HEADING': return '📰'\n      case 'BULLET_LIST': return '•'\n      case 'NUMBERED_LIST': return '1.'\n      case 'CODE': return '💻'\n      case 'QUOTE': return '💬'\n      default: return '📝'\n    }\n  }\n\n  const getBlockStyle = (type: string) => {\n    switch (type) {\n      case 'HEADING':\n        return 'text-xl font-bold text-gray-900'\n      case 'CODE':\n        return 'code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto'\n      case 'QUOTE':\n        return 'border-l-4 border-blue-400 pl-4 italic text-gray-700 bg-blue-50 py-2 rounded-r'\n      default:\n        return 'text-gray-900'\n    }\n  }\n\n  const renderContent = () => {\n    if (isEditing) {\n      const editingStyle = block.type === 'CODE'\n        ? 'w-full border-none outline-none resize-none code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto'\n        : `w-full border-none outline-none resize-none ${getBlockStyle(block.type)} bg-transparent`\n\n      return (\n        <textarea\n          ref={textareaRef}\n          value={content}\n          onChange={(e) => setContent(e.target.value)}\n          onKeyDown={handleKeyDown}\n          onBlur={handleSave}\n          className={editingStyle}\n          placeholder={block.type === 'CODE' ? 'Enter your code here...' : 'Type something...'}\n          rows={block.type === 'CODE' ? 3 : 1}\n        />\n      )\n    }\n\n    if (!content.trim()) {\n      return (\n        <div \n          onClick={() => setIsEditing(true)}\n          className=\"text-gray-400 cursor-text py-2\"\n        >\n          Type something...\n        </div>\n      )\n    }\n\n    const formattedContent = block.type === 'BULLET_LIST' \n      ? content.split('\\n').map(line => line.trim() ? `• ${line}` : line).join('\\n')\n      : block.type === 'NUMBERED_LIST'\n      ? content.split('\\n').map((line, i) => line.trim() ? `${i + 1}. ${line}` : line).join('\\n')\n      : content\n\n    return (\n      <div \n        onClick={() => setIsEditing(true)}\n        className={`cursor-text py-2 whitespace-pre-wrap ${getBlockStyle(block.type)}`}\n      >\n        {formattedContent}\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"group relative flex items-start space-x-2 py-1\">\n      {/* Block Type Icon & Menu */}\n      <div className=\"relative\">\n        <button\n          onClick={() => setShowMenu(!showMenu)}\n          className=\"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity\"\n          title=\"Change block type\"\n        >\n          {getBlockIcon(block.type)}\n        </button>\n        \n        {showMenu && (\n          <div className=\"absolute left-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10\">\n            <div className=\"py-1\">\n              <button\n                onClick={() => handleTypeChange('TEXT')}\n                className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                📝 Text\n              </button>\n              <button\n                onClick={() => handleTypeChange('HEADING')}\n                className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                📰 Heading\n              </button>\n              <button\n                onClick={() => handleTypeChange('BULLET_LIST')}\n                className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                • Bullet List\n              </button>\n              <button\n                onClick={() => handleTypeChange('NUMBERED_LIST')}\n                className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                1. Numbered List\n              </button>\n              <button\n                onClick={() => handleTypeChange('CODE')}\n                className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                💻 Code\n              </button>\n              <button\n                onClick={() => handleTypeChange('QUOTE')}\n                className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                💬 Quote\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Block Content */}\n      <div className=\"flex-1 min-w-0\">\n        {renderContent()}\n      </div>\n\n      {/* Block Actions */}\n      <div className=\"opacity-0 group-hover:opacity-100 transition-opacity\">\n        <button\n          onClick={handleDelete}\n          className=\"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500\"\n          title=\"Delete block\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n          </svg>\n        </button>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotes } from '@/contexts/NotesContext'\nimport { NoteWithBlocks } from '@/types'\nimport BlockEditor from './BlockEditor'\n\ninterface NoteEditorProps {\n  note: NoteWithBlocks\n}\n\nexport default function NoteEditor({ note: initialNote }: NoteEditorProps) {\n  const { selectedNote, updateNote, createBlock, loadNote, loadingNote } = useNotes()\n  const [isEditingTitle, setIsEditingTitle] = useState(false)\n  const [title, setTitle] = useState(initialNote.title)\n  const [description, setDescription] = useState(initialNote.description || '')\n\n  const note = selectedNote || initialNote\n\n  useEffect(() => {\n    if (initialNote.id) {\n      loadNote(initialNote.id)\n    }\n  }, [initialNote.id])\n\n  const handleSaveTitle = async () => {\n    try {\n      await updateNote(note.id, { title, description: description || undefined })\n      setIsEditingTitle(false)\n    } catch (error) {\n      console.error('Failed to update note:', error)\n      alert('Failed to update note')\n    }\n  }\n\n  const handleAddBlock = async (type: string = 'TEXT') => {\n    try {\n      const newOrder = note.blocks.length\n      await createBlock(note.id, type, '', newOrder)\n    } catch (error) {\n      console.error('Failed to create block:', error)\n      alert('Failed to create block')\n    }\n  }\n\n  const formatDate = (date: string) => {\n    return new Date(date).toLocaleDateString([], { \n      year: 'numeric', \n      month: 'short', \n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col\">\n      {/* Note Header */}\n      <div className=\"bg-white border-b border-gray-200 p-6\">\n        {isEditingTitle ? (\n          <div className=\"space-y-4\">\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              className=\"text-2xl font-bold text-gray-900 w-full border-none outline-none bg-transparent\"\n              placeholder=\"Note title\"\n              autoFocus\n            />\n            <textarea\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              className=\"text-gray-600 w-full border-none outline-none bg-transparent resize-none\"\n              placeholder=\"Add a description...\"\n              rows={2}\n            />\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={handleSaveTitle}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm\"\n              >\n                Save\n              </button>\n              <button\n                onClick={() => {\n                  setTitle(note.title)\n                  setDescription(note.description || '')\n                  setIsEditingTitle(false)\n                }}\n                className=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm\"\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        ) : (\n          <div onClick={() => setIsEditingTitle(true)} className=\"cursor-pointer\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2 hover:bg-gray-50 p-2 rounded\">\n              {note.title}\n            </h1>\n            {note.description && (\n              <p className=\"text-gray-600 hover:bg-gray-50 p-2 rounded\">\n                {note.description}\n              </p>\n            )}\n          </div>\n        )}\n        \n        <div className=\"flex items-center justify-between mt-4 text-sm text-gray-500\">\n          <div>\n            Created by <span className=\"font-medium\">{note.author.name || note.author.username}</span>\n          </div>\n          <div>\n            Last updated {formatDate(note.updatedAt)}\n          </div>\n        </div>\n      </div>\n\n      {/* Note Content */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"max-w-4xl mx-auto p-6\">\n          {/* Loading State */}\n          {(loadingNote || !note.blocks) && (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n              <span className=\"ml-2 text-gray-500\">Loading note content...</span>\n            </div>\n          )}\n\n          {/* Blocks */}\n          {note.blocks && (\n            <div className=\"space-y-2\">\n              {note.blocks.map((block, index) => (\n                <BlockEditor\n                  key={block.id}\n                  block={block}\n                  noteId={note.id}\n                  isLast={index === note.blocks.length - 1}\n                  onAddBlock={handleAddBlock}\n                />\n              ))}\n            </div>\n          )}\n\n          {/* Add Block Button */}\n          {note.blocks && (\n            <div className=\"mt-4\">\n              <div className=\"flex items-center space-x-2\">\n                <button\n                  onClick={() => handleAddBlock('TEXT')}\n                  className=\"flex items-center space-x-2 text-gray-500 hover:text-gray-700 p-2 rounded hover:bg-gray-100\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                  <span className=\"text-sm\">Add block</span>\n                </button>\n              \n              {/* Block Type Selector */}\n              <div className=\"relative group\">\n                <button className=\"text-gray-400 hover:text-gray-600 p-1\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10\">\n                  <div className=\"py-1\">\n                    <button\n                      onClick={() => handleAddBlock('TEXT')}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      📝 Text\n                    </button>\n                    <button\n                      onClick={() => handleAddBlock('HEADING')}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      📰 Heading\n                    </button>\n                    <button\n                      onClick={() => handleAddBlock('BULLET_LIST')}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      • Bullet List\n                    </button>\n                    <button\n                      onClick={() => handleAddBlock('NUMBERED_LIST')}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      1. Numbered List\n                    </button>\n                    <button\n                      onClick={() => handleAddBlock('CODE')}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      💻 Code\n                    </button>\n                    <button\n                      onClick={() => handleAddBlock('QUOTE')}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      💬 Quote\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotes } from '@/contexts/NotesContext'\nimport { GroupWithMembers } from '@/types'\nimport CreateNoteModal from './CreateNoteModal'\nimport NoteEditor from './NoteEditor'\n\ninterface NotesInterfaceProps {\n  group: GroupWithMembers\n}\n\nexport default function NotesInterface({ group }: NotesInterfaceProps) {\n  const { notes, selectedNote, loading, loadNotes, selectNote, deleteNote } = useNotes()\n  const [showCreateModal, setShowCreateModal] = useState(false)\n  const [showNotesList, setShowNotesList] = useState(true)\n\n  useEffect(() => {\n    if (group) {\n      loadNotes(group.id)\n    }\n  }, [group.id])\n\n  const handleDeleteNote = async (noteId: string) => {\n    if (confirm('Are you sure you want to delete this note?')) {\n      try {\n        await deleteNote(noteId)\n      } catch (error) {\n        console.error('Failed to delete note:', error)\n        alert('Failed to delete note')\n      }\n    }\n  }\n\n  const formatDate = (date: string) => {\n    return new Date(date).toLocaleDateString([], { \n      year: 'numeric', \n      month: 'short', \n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  if (selectedNote && !showNotesList) {\n    return (\n      <div className=\"flex-1 flex flex-col\">\n        <div className=\"bg-white border-b border-gray-200 p-4\">\n          <button\n            onClick={() => setShowNotesList(true)}\n            className=\"text-blue-500 hover:text-blue-600 text-sm mb-2\"\n          >\n            ← Back to Notes\n          </button>\n        </div>\n        <NoteEditor note={selectedNote} />\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <div className=\"flex-1 flex flex-col\">\n        {/* Header */}\n        <div className=\"bg-white border-b border-gray-200 p-4\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Notes</h2>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\"\n            >\n              New Note\n            </button>\n          </div>\n        </div>\n\n        {/* Notes List */}\n        <div className=\"flex-1 overflow-y-auto p-4\">\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"></div>\n              <p className=\"mt-2 text-gray-500\">Loading notes...</p>\n            </div>\n          ) : notes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Notes Yet</h3>\n              <p className=\"text-gray-500 mb-4\">Create your first note to start collaborating</p>\n              <button\n                onClick={() => setShowCreateModal(true)}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\"\n              >\n                Create Note\n              </button>\n            </div>\n          ) : (\n            <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n              {notes.map((note) => (\n                <div\n                  key={note.id}\n                  className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200\"\n                  onClick={() => {\n                    selectNote(note)\n                    setShowNotesList(false)\n                  }}\n                >\n                  <div className=\"p-4\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                        {note.title}\n                      </h3>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          handleDeleteNote(note.id)\n                        }}\n                        className=\"text-gray-400 hover:text-red-500 p-1\"\n                        title=\"Delete note\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                        </svg>\n                      </button>\n                    </div>\n                    \n                    {note.description && (\n                      <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                        {note.description}\n                      </p>\n                    )}\n                    \n                    <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <span className=\"font-medium\">\n                          {note.author.name || note.author.username}\n                        </span>\n                        <span className=\"mx-1\">•</span>\n                        <span>{note._count.blocks} blocks</span>\n                      </div>\n                      <span>{formatDate(note.updatedAt)}</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      <CreateNoteModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        groupId={group.id}\n      />\n    </>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useGroups } from '@/contexts/GroupContext'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { MessageProvider } from '@/contexts/MessageContext'\nimport { NotesProvider } from '@/contexts/NotesContext'\nimport ChatInterface from '@/components/messages/ChatInterface'\nimport NotesInterface from '@/components/notes/NotesInterface'\n\nexport default function GroupDetail() {\n  const { selectedGroup, addMember } = useGroups()\n  const { user } = useAuth()\n  const [showAddMember, setShowAddMember] = useState(false)\n  const [memberEmail, setMemberEmail] = useState('')\n  const [memberRole, setMemberRole] = useState<'MEMBER' | 'ADMIN'>('MEMBER')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [activeTab, setActiveTab] = useState<'chat' | 'members' | 'notes'>('chat')\n\n  if (!selectedGroup) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n          </svg>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Select a Group</h3>\n          <p className=\"text-gray-500\">Choose a group from the sidebar to view details and start chatting</p>\n        </div>\n      </div>\n    )\n  }\n\n  const isOwnerOrAdmin = selectedGroup.ownerId === user?.id || \n    selectedGroup.members.some(m => m.userId === user?.id && ['OWNER', 'ADMIN'].includes(m.role))\n\n  const handleAddMember = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await addMember(selectedGroup.id, memberEmail, memberRole)\n      setMemberEmail('')\n      setMemberRole('MEMBER')\n      setShowAddMember(false)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to add member')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <MessageProvider>\n      <NotesProvider>\n        <div className=\"flex-1 flex flex-col bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white border-b border-gray-200 p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <div className=\"flex items-center\">\n                <h1 className=\"text-xl font-semibold text-gray-900\">{selectedGroup.name}</h1>\n                {selectedGroup.isPrivate && (\n                  <svg className=\"w-4 h-4 text-gray-400 ml-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                )}\n              </div>\n              {selectedGroup.description && (\n                <p className=\"text-sm text-gray-600 mt-1\">{selectedGroup.description}</p>\n              )}\n            </div>\n            {isOwnerOrAdmin && (\n              <button\n                onClick={() => setShowAddMember(true)}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\"\n              >\n                Add Member\n              </button>\n            )}\n          </div>\n\n          {/* Tabs */}\n          <div className=\"mt-4\">\n            <nav className=\"flex space-x-8\">\n              <button\n                onClick={() => setActiveTab('chat')}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'chat'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Chat\n              </button>\n              <button\n                onClick={() => setActiveTab('members')}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'members'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Members ({selectedGroup._count.members})\n              </button>\n              <button\n                onClick={() => setActiveTab('notes')}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'notes'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Notes ({selectedGroup._count.notes})\n              </button>\n            </nav>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 flex flex-col\">\n          {activeTab === 'chat' && (\n            <ChatInterface group={selectedGroup} />\n          )}\n\n          {activeTab === 'members' && (\n            <div className=\"flex-1 p-6 overflow-y-auto\">\n              <div className=\"max-w-4xl mx-auto\">\n                {/* Group Stats */}\n                <div className=\"grid grid-cols-3 gap-4 mb-8\">\n                  <div className=\"bg-white p-4 rounded-lg shadow\">\n                    <div className=\"text-2xl font-bold text-blue-600\">{selectedGroup._count.members}</div>\n                    <div className=\"text-sm text-gray-600\">Members</div>\n                  </div>\n                  <div className=\"bg-white p-4 rounded-lg shadow\">\n                    <div className=\"text-2xl font-bold text-green-600\">{selectedGroup._count.messages}</div>\n                    <div className=\"text-sm text-gray-600\">Messages</div>\n                  </div>\n                  <div className=\"bg-white p-4 rounded-lg shadow\">\n                    <div className=\"text-2xl font-bold text-purple-600\">{selectedGroup._count.notes}</div>\n                    <div className=\"text-sm text-gray-600\">Notes</div>\n                  </div>\n                </div>\n\n                {/* Members List */}\n                <div className=\"bg-white rounded-lg shadow\">\n                  <div className=\"p-4 border-b border-gray-200\">\n                    <h2 className=\"text-lg font-medium text-gray-900\">Members</h2>\n                  </div>\n                  <div className=\"p-4\">\n                    <div className=\"space-y-3\">\n                      {selectedGroup.members.map((member) => (\n                        <div key={member.id} className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center\">\n                            <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                              <span className=\"text-sm font-medium text-gray-700\">\n                                {(member.user.name || member.user.username).charAt(0).toUpperCase()}\n                              </span>\n                            </div>\n                            <div className=\"ml-3\">\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {member.user.name || member.user.username}\n                              </p>\n                              <p className=\"text-xs text-gray-500\">{member.user.email}</p>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                              member.role === 'OWNER' ? 'bg-green-100 text-green-800' :\n                              member.role === 'ADMIN' ? 'bg-blue-100 text-blue-800' :\n                              'bg-gray-100 text-gray-800'\n                            }`}>\n                              {member.role}\n                            </span>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'notes' && (\n            <NotesInterface group={selectedGroup} />\n          )}\n        </div>\n\n        {/* Add Member Modal */}\n        {showAddMember && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg p-6 w-full max-w-md mx-4\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h2 className=\"text-xl font-bold text-gray-900\">Add Member</h2>\n                <button\n                  onClick={() => setShowAddMember(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n\n              {error && (\n                <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n                  {error}\n                </div>\n              )}\n\n              <form onSubmit={handleAddMember}>\n                <div className=\"mb-4\">\n                  <label className=\"block text-gray-700 text-sm font-bold mb-2\">\n                    Email Address\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={memberEmail}\n                    onChange={(e) => setMemberEmail(e.target.value)}\n                    className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n                    placeholder=\"Enter user's email\"\n                    required\n                  />\n                </div>\n\n                <div className=\"mb-6\">\n                  <label className=\"block text-gray-700 text-sm font-bold mb-2\">\n                    Role\n                  </label>\n                  <select\n                    value={memberRole}\n                    onChange={(e) => setMemberRole(e.target.value as 'MEMBER' | 'ADMIN')}\n                    className=\"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n                  >\n                    <option value=\"MEMBER\">Member</option>\n                    <option value=\"ADMIN\">Admin</option>\n                  </select>\n                </div>\n\n                <div className=\"flex justify-end space-x-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowAddMember(false)}\n                    className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={loading}\n                    className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\"\n                  >\n                    {loading ? 'Adding...' : 'Add Member'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n        </div>\n      </NotesProvider>\n    </MessageProvider>\n  )\n}\n"], "names": [], "mappings": "qFAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OCaA,IAAM,EAAe,CAAA,EAAA,EAAA,aAAA,AAAa,EAA+B,QAE1D,SAAS,EAAc,UAAE,CAAQ,CAAiC,EACvE,GAAM,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAqB,EAAE,EACrD,CAAC,EAAe,EAAiB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAA0B,MACtE,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAEjC,EAAgB,UACpB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,eAC7B,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAU,EAAK,MAAM,CACvB,MACE,CADK,OACG,KAAK,CAAC,yBAElB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,4BAA6B,EAC7C,CACF,EAEM,EAAc,MAAO,IACzB,IAAM,EAAW,MAAM,MAAM,cAAe,CAC1C,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,EACvB,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,0BAGjC,IAAM,EAAS,MAAM,EAAS,IAAI,GAElC,OADA,MAAM,IACC,EAAO,KAAK,AACrB,EAEM,EAAc,MAAO,EAAiB,KAC1C,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAA,CAAS,CAAE,CACrD,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,EACvB,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,0BAGjC,IAAM,EAAS,MAAM,EAAS,IAAI,GAQlC,OAPA,MAAM,IAGF,GAAe,KAAO,GACxB,EAAiB,EAAO,EADS,GACJ,EAGxB,EAAO,KAAK,AACrB,EAEM,EAAc,MAAO,IACzB,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAA,CAAS,CAAE,CACrD,OAAQ,QACV,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,0BAI7B,GAAe,KAAO,GACxB,EAAiB,IADgB,EAInC,MAAM,GACR,EAMM,EAAY,MAAO,EAAiB,EAAe,EAA2B,QAAQ,IAC1F,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAQ,QAAQ,CAAC,CAAE,CAC7D,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,OAAE,OAAO,CAAK,EACrC,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,wBAMjC,GAHA,MAAM,IAGF,GAAe,KAAO,EAAS,CACjC,IAAM,EAAgB,MAAM,MAAM,CAAC,YAAY,EAAE,EAAA,CAAS,EACtD,EAAc,EAAE,EAAE,AAEpB,EAAiB,CADC,MAAM,EAAc,IAAI,EAAA,EACf,KAAK,CAEpC,CACF,EAMA,MAJA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,IAAgB,OAAO,CAAC,IAAM,EAAW,IAC3C,EAAG,EAAE,EAGH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAa,QAAQ,CAAA,CAAC,MAAO,QAC5B,gBACA,UACA,cACA,cACA,cACA,EACA,YA1CgB,AAAC,IACnB,EAAiB,EACnB,gBAyCI,YACA,CACF,WACG,GAGP,CAEO,SAAS,IACd,IAAM,EAAU,CAAA,EAAA,EAAA,UAAU,AAAV,EAAW,GAC3B,QAAgB,IAAZ,EACF,KADyB,CACnB,AAAI,MAAM,iDAElB,OAAO,CACT,CCnJe,SAAS,EAAiB,QAAE,CAAM,CAAE,SAAO,CAAyB,EACjF,GAAM,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC3B,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACzC,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,aAAE,CAAW,CAAE,CAAG,IAElB,EAAe,MAAO,IAC1B,EAAE,cAAc,GAChB,EAAS,IACT,GAAW,GAEX,GAAI,CACF,MAAM,EAAY,MAChB,EACA,YAAa,QAAe,YAC5B,CACF,GAGA,EAAQ,IACR,EAAe,IACf,GAAa,GACb,GACF,CAAE,MAAO,EAAK,CACZ,EAAS,aAAe,MAAQ,EAAI,OAAO,CAAG,yBAChD,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAAc,KACb,IACH,EAAQ,GADI,CAEZ,EAAe,IACf,GAAa,GACb,EAAS,IACT,IAEJ,SAEA,AAAK,EAGH,CAAA,CAHE,CAGF,EAAA,CAHW,EAGX,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2CAAkC,qBAChD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,SAAU,EACV,UAAU,iEAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gCAK1E,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gFACZ,IAIL,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,YACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,gBAAO,iBAG7E,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,OACH,KAAK,OACL,MAAO,EACP,SAAW,AAAD,GAAO,EAAQ,EAAE,MAAM,CAAC,KAAK,EACvC,UAAU,6HACV,YAAY,mBACZ,QAAQ,CAAA,CAAA,EACR,UAAW,SAIf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,uBAAc,gBAGpF,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,GAAG,cACH,MAAO,EACP,SAAU,AAAC,GAAM,EAAe,EAAE,MAAM,CAAC,KAAK,EAC9C,UAAU,6HACV,YAAY,qCACZ,KAAM,EACN,UAAW,SAIf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,UAAU,8BACf,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,WACL,QAAS,EACT,SAAU,AAAC,GAAM,EAAa,EAAE,MAAM,CAAC,OAAO,EAC9C,UAAU,SAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iCAAwB,qBAE1C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,iDAK5C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,QAAS,EACT,SAAU,EACV,UAAU,uGACX,WAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAW,CAAC,EAAK,IAAI,GAC/B,UAAU,sHAET,EAAU,cAAgB,4BArFnB,IA4FtB,CCzIe,SAAS,IACtB,GAAM,QAAE,CAAM,eAAE,CAAa,aAAE,CAAW,SAAE,CAAO,CAAE,CAAG,IAClD,MAAE,CAAI,CAAE,CAAG,CAAA,EAAA,EAAA,OAAO,AAAP,IACX,CAAC,EAAiB,EAAmB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,UAEvD,AAAI,EAEA,CAAA,EAAA,EAAA,EAFS,CAET,EAAC,MAAA,CAAI,UAAU,sDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,sBAAa,CAAC,GAAG,CAAC,CAAC,EAAG,IACrB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAY,UAAU,4BAAb,WASpB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+CAAsC,WACpD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAmB,IAClC,UAAU,4DACV,MAAM,4BAEN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4BAO7E,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCACM,IAAlB,EAAO,MAAM,CACZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0CACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gBAAO,kBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAmB,GAClC,UAAU,qDACX,+BAKH,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,eACZ,EAAO,GAAG,CAAE,AAAD,GACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,QAAS,IAAM,EAAY,GAC3B,UAAW,CAAC,qDAAqD,EAC/D,GAAe,KAAO,EAAM,EAAE,CAC1B,oCACA,mBAAA,CACJ,UAEF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,sDACX,EAAM,IAAI,GAEZ,EAAM,SAAS,EACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6BAA6B,KAAK,eAAe,QAAQ,qBACtE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAS,UAAU,EAAE,yGAAyG,SAAS,iBAIlJ,EAAM,WAAW,EAChB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,+CACV,EAAM,WAAW,GAGtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yDACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WAAM,EAAM,MAAM,CAAC,OAAO,CAAC,cAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,gBAAO,MACvB,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WAAM,EAAM,MAAM,CAAC,QAAQ,CAAC,eAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,gBAAO,MACvB,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WAAM,EAAM,MAAM,CAAC,KAAK,CAAC,kBAG7B,EAAM,OAAO,GAAK,GAAM,IACvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,2GAAkG,gBAnCnH,EAAM,EAAE,UAgDzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,OAAQ,EACR,QAAS,IAAM,GAAmB,OAI1C,CCpGA,IAAM,EAAiB,CAAA,EAAA,EAAA,aAAA,AAAa,OAAiC,GAE9D,SAAS,EAAgB,UAAE,CAAQ,CAAiC,EACzE,GAAM,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAA8B,EAAE,EAC1D,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAS,GACzC,CAAC,EAAgB,EAAkB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAgB,MAE9D,EAAe,MAAO,EAAiB,EAAe,CAAC,IACvD,IAAY,IAEd,EAAY,EAAE,EACd,EAAe,GACf,CAJ8B,CAIZ,GAClB,EAAO,GAGT,GAAW,GACX,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAQ,eAAe,EAAE,EAAK,SAAS,CAAC,EACpF,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAEnB,GAAG,CAAZ,EACF,EAAY,EAAK,QAAQ,EAGzB,EAAY,GAAQ,IAAI,EAAK,QAAQ,IAAK,EAAK,EAGjD,EAAW,EAAK,UAAU,CAAC,OAAO,EAClC,EAAe,EACjB,MACE,CADK,OACG,KAAK,CAAC,0BAElB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,2BAA4B,EAC5C,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAAc,MAAO,EAAiB,KAC1C,GAAW,GACX,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAQ,SAAS,CAAC,CAAE,CAC9D,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,SAAE,CAAQ,EACjC,GAEA,GAAI,CAAC,EAAS,EAAE,CAAE,CAChB,IAAM,EAAQ,MAAM,EAAS,IAAI,EACjC,OAAM,AAAI,MAAM,EAAM,KAAK,EAAI,yBACjC,CAEA,IAAM,EAAS,MAAM,EAAS,IAAI,GAGlC,EAAY,GAAQ,IAAI,EAAM,EAAO,IAAI,CAAC,CAC5C,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,0BAA2B,GACnC,CACR,QAAU,CACR,GAAW,EACb,CACF,EASA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAe,QAAQ,CAAA,CAAC,MAAO,UAC9B,UACA,UACA,cACA,EACA,eACA,cAdkB,KACpB,EAAY,EAAE,EACd,EAAe,GACf,EAAW,IACX,EAAkB,KACpB,UAUI,cACA,CACF,WACG,GAGP,CCrFA,IAAM,EAAe,CAAA,EAAA,EAAA,aAAA,AAAa,OAA+B,GAE1D,SAAS,EAAc,CAAE,UAAQ,CAAiC,EACvE,GAAM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAmB,EAAE,EACjD,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAgC,MAClE,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,GAAS,GAEzC,EAAY,MAAO,IACvB,EAAW,IACX,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAQ,MAAM,CAAC,EAC3D,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAS,EAAK,KAAK,CACrB,MACE,CADK,OACG,KAAK,CAAC,uBAElB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wBAAyB,EACzC,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAAW,MAAO,IACtB,GAAe,GACf,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,CAAC,WAAW,EAAE,EAAA,CAAQ,EACnD,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAgB,EAAK,IAAI,CAC3B,MACE,CADK,OACG,KAAK,CAAC,sBAElB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,uBAAwB,EACxC,QAAU,CACR,GAAe,EACjB,CACF,EAEM,EAAa,MAAO,EAAiB,KACzC,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAQ,MAAM,CAAC,CAAE,CAC3D,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,EACvB,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,yBAGjC,IAAM,EAAS,MAAM,EAAS,IAAI,GAElC,OADA,MAAM,EAAU,GACT,EAAO,IAAI,AACpB,EAEM,EAAa,MAAO,EAAgB,KACxC,IAAM,EAAW,MAAM,MAAM,CAAC,WAAW,EAAE,EAAA,CAAQ,CAAE,CACnD,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,EACvB,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MADI,AACE,OADI,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,yBAGjC,IAAM,EAAS,MAAM,EAAS,IAAI,GAYlC,OATI,GAAc,KAAO,GACvB,EAAgB,EAAO,CADQ,GACJ,EAI7B,EAAS,GAAQ,EAAK,GAAG,CAAC,GACxB,EAAK,EAAE,GAAK,EAAS,CAAE,GAAG,CAAI,CAAE,GAAG,CAAI,AAAC,EAAI,IAGvC,EAAO,IAAI,AACpB,EAEM,EAAa,MAAO,IACxB,IAAM,EAAW,MAAM,MAAM,CAAC,WAAW,EAAE,EAAA,CAAQ,CAAE,CACnD,OAAQ,QACV,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,yBAI7B,GAAc,KAAO,GACvB,EAAgB,GADe,GAKjC,EAAS,GAAQ,EAAK,MAAM,CAAC,GAAQ,EAAK,EAAE,GAAK,GACnD,EAgBM,EAAc,MAAO,EAAgB,EAAc,EAAiB,KACxE,IAAM,EAAW,MAAM,MAAM,CAAC,WAAW,EAAE,EAAO,OAAO,CAAC,CAAE,CAC1D,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CAAE,OAAM,gBAAS,CAAM,EAC9C,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,0BAGjC,IAAM,EAAS,MAAM,EAAS,IAAI,GAOlC,OAJI,GAAc,KAAO,GACvB,KAD+B,CACzB,EAAS,GAGV,EAAO,KAAK,AACrB,EAEM,EAAc,MAAO,EAAiB,KAC1C,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAA,CAAS,CAAE,CACrD,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,EACvB,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,0BAGjC,IAAM,EAAS,MAAM,EAAS,IAAI,GAYlC,OATI,GACF,EAAgB,GAAQ,EAAO,CAC7B,GAAG,AAFW,CAEP,CACP,OAAQ,EAAK,MAAM,CAAC,GAAG,CAAC,GACtB,EAAM,EAAE,GAAK,EAAU,CAAE,GAAG,CAAK,CAAE,GAAG,CAAI,AAAC,EAAI,EAEnD,EAAI,MAGC,EAAO,KAAK,AACrB,EAEM,EAAc,MAAO,IACzB,IAAM,EAAW,MAAM,MAAM,CAAC,YAAY,EAAE,EAAA,CAAS,CAAE,CACrD,OAAQ,QACV,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MADI,AACE,OADI,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,0BAI7B,GACF,EAAgB,GAAQ,EAAO,CAC7B,GAAG,AAFW,CAEP,CACP,OAAQ,EAAK,MAAM,CAAC,MAAM,CAAC,GAAS,EAAM,EAAE,GAAK,EACnD,EAAI,KAER,EAEM,EAAgB,MAAO,EAAgB,KAC3C,IAAM,EAAW,MAAM,MAAM,CAAC,WAAW,EAAE,EAAO,OAAO,CAAC,CAAE,CAC1D,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,QAAE,CAAO,EAChC,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,2BAI7B,IAAc,KAAO,GACvB,KAD+B,CACzB,EAAS,EAEnB,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAa,QAAQ,CAAA,CAAC,MAAO,OAC5B,eACA,UACA,cACA,aACA,EACA,aACA,aACA,WAnHe,AAAC,IAClB,EAAgB,GAEZ,GAAM,IAAI,AACZ,EAAS,EAAK,EAAE,CAEpB,YA8GI,EACA,WACA,WA9Ge,KACjB,EAAS,EAAE,EACX,EAAgB,KAClB,cA4GI,cACA,cACA,gBACA,CACF,WACG,GAGP,CAEO,SAAS,IACd,IAAM,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,GAC3B,QAAgB,IAAZ,EACF,KADyB,CACnB,AAAI,MAAM,gDAElB,OAAO,CACT,CCjQe,SAAS,EAAc,OAAE,CAAK,CAAsB,EACjE,GAAM,UAAE,CAAQ,SAAE,CAAO,SAAE,CAAO,aAAE,CAAW,cAAE,CAAY,SAAE,CAAO,aAAE,CAAW,CAAE,CFmGhF,AEnGmF,SFmG1E,EACd,IAAM,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,GAC3B,QAAgB,IAAZ,EACF,KADyB,CACnB,AAAI,MAAM,qDAElB,OAAO,CACT,IExGQ,MAAE,CAAI,CAAE,CAAG,CAAA,EAAA,EAAA,OAAA,AAAO,IAClB,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACvC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,EAAiB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MACxC,EAAuB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAEpD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACJ,GACF,EAAa,EAAM,AADV,EACY,CAEzB,EAAG,CAAC,EAAM,EAAE,CAAC,EAEb,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KAER,GACF,EAAG,CAAC,EAAS,EAEb,IAAM,EAAiB,KACrB,EAAe,OAAO,EAAE,eAAe,CAAE,SAAU,QAAS,EAC9D,EAEM,EAAoB,MAAO,IAE/B,GADA,EAAE,cAAc,GACZ,CAAC,EAAW,IAAI,IAAM,EAAS,OAEnC,EAAS,IACT,IAAM,EAAiB,EAAW,IAAI,GACtC,EAAc,IAEd,GAAI,CACF,MAAM,EAAY,EAAM,EAAE,CAAE,EAC9B,CAAE,MAAO,EAAK,CACZ,EAAS,aAAe,MAAQ,EAAI,OAAO,CAAG,0BAC9C,EAAc,EAChB,CACF,EAEM,EAAmB,UAJS,AAK5B,GAAW,CAAC,GACd,MADuB,AACjB,EAAa,EAAM,EAAE,CAAE,EAAc,EAE/C,EA8BA,CAtC6D,KAuC3D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,IAAK,EACL,UAAU,iDAGT,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,SAAU,EACV,UAAU,yEAET,EAAU,aAAe,0BAM/B,EAAS,GAAG,CAAC,CAAC,EAAS,aAEtB,IAAM,GAhCmB,EAgCyB,EA/BxD,EADoD,EAChD,AA8B0B,CA9BzB,CA8BiC,CAC2B,CADvB,CAAQ,CAAC,EAAQ,CAC3B,CAD6B,CAAG,OAzBzD,AALe,AAEF,IAAI,GAFK,EAEA,EAAe,KAGrB,IAH8B,EAAE,YAAY,KAC9C,IAAI,KAAK,EAAgB,SAAS,EAAE,YAAY,IA6BzD,EAAe,EAAQ,QAAQ,GAAK,GAAM,GAEhD,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WAEE,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEACZ,CAxDA,AAAC,IAClB,IAAM,EAAc,IAAI,KAAK,GACvB,EAAQ,IAAI,KACZ,EAAY,IAAI,KAAK,SAG3B,CAFA,EAAU,OAAO,CAAC,EAAU,OAAO,GAAK,GAEpC,EAAY,YAAY,KAAO,EAAM,YAAY,IAAI,AAChD,QACE,EAAY,YAAY,KAAO,EAAU,YAAY,GACvD,CAD2D,WAG3D,EAAY,kBAAkB,EAEzC,GA2C8B,EAAQ,SAAS,MAMnC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,KAAK,EAAE,EAAe,cAAgB,gBAAA,CAAiB,UACtE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,qBAAqB,EAAE,EAAe,UAAY,UAAA,CAAW,WAC3E,CAAC,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACZ,EAAQ,MAAM,CAAC,IAAI,EAAI,EAAQ,MAAM,CAAC,QAAQ,GAGnD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAW,CAAC,qBAAqB,EAC/B,EACI,yBACA,mDAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACZ,EAAQ,OAAO,CAAC,QAAQ,CAAC,OACxB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACE,EAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,EAAM,IACvC,EAAQ,GAAM,EACZ,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAkB,GAAR,GAEX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAiB,UAAU,8FACzB,GADQ,MAOjB,EAAQ,OAAO,GAGnB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,aAAa,EAAE,EAAe,gBAAkB,gBAAA,CAAiB,UAhG3F,CAiGY,GAjGR,KAiGmB,AAjGd,EAiGsB,SAAS,EAjGzB,kBAAkB,CAAC,EAAE,CAAE,CAAE,KAAM,UAAW,OAAQ,SAAU,eAsDhE,EAAQ,EAAE,CAkDxB,GAGC,GAAW,AAAoB,MAAX,MAAM,EACzB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yEACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gBAAO,2BAKvB,CAAC,GAA+B,IAApB,EAAS,MAAM,EAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0CACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,+CAIP,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,IAAK,OAIZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yCACZ,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wFACZ,IAIL,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAmB,UAAU,2BAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,YAAY,oBACZ,UAAU,kMACV,SAAU,EACV,UAAW,MAEb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,CAAC,EAAW,IAAI,IAAM,EAChC,UAAU,yHAET,EAAU,aAAe,iBAMtC,CCvMe,SAAS,EAAgB,QAAE,CAAM,SAAE,CAAO,CAAE,SAAO,CAAwB,EACxF,GAAM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAS,IAC7B,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACzC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,YAAE,CAAU,CAAE,CAAG,IAEjB,EAAe,MAAO,IAC1B,EAAE,cAAc,GAChB,EAAS,IACT,EAAW,IAEX,GAAI,CACF,MAAM,EAAW,EAAS,CACxB,QACA,YAAa,QAAe,CAC9B,GAGA,EAAS,IACT,EAAe,IACf,GACF,CAAE,MAAO,EAAK,CACZ,EAAS,aAAe,MAAQ,EAAI,OAAO,CAAG,wBAChD,QAAU,CACR,GAAW,EACb,CACF,EAEM,EAAc,KACb,IACH,EAAS,GADG,CAEZ,EAAe,IACf,EAAS,IACT,IAEJ,SAEA,AAAK,EAGH,CAAA,CAHE,CAGF,EAAA,CAHW,EAGX,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2CAAkC,oBAChD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,SAAU,EACV,UAAU,iEAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gCAK1E,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gFACZ,IAIL,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,YACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,iBAAQ,iBAG9E,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,OACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,UAAU,6HACV,YAAY,mBACZ,QAAQ,CAAA,CAAA,EACR,UAAW,SAIf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CAA6C,QAAQ,uBAAc,gBAGpF,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,GAAG,cACH,MAAO,EACP,SAAW,AAAD,GAAO,EAAe,EAAE,MAAM,CAAC,KAAK,EAC9C,UAAU,6HACV,YAAY,oCACZ,KAAM,EACN,UAAW,SAIf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,QAAS,EACT,SAAU,EACV,UAAU,uGACX,WAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAW,CAAC,EAAM,IAAI,GAChC,UAAU,sHAET,EAAU,cAAgB,2BAtEnB,IA6EtB,CCjHe,SAAS,EAAY,CAAE,OAAK,QAAE,CAAM,QAAE,CAAM,CAAE,YAAU,CAAoB,EACzF,GAAM,aAAE,CAAW,aAAE,CAAW,CAAE,CAAG,IAC/B,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAM,OAAO,EAC9C,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,GAAS,GACrC,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACnC,EAAc,CAAA,EAAA,EAAA,MAAA,AAAM,EAAsB,MAEhD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,EAAW,EAAM,OAAO,CAC1B,EAAG,CAAC,EAAM,OAAO,CAAC,EAElB,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACJ,GAAa,EAAY,OAAO,EAAE,CACpC,EAAY,OAAO,CAAC,KAAK,GAEzB,EAAY,OAAO,CAAC,KAAK,CAAC,MAAM,CAAG,OACnC,EAAY,OAAO,CAAC,KAAK,CAAC,MAAM,CAAG,EAAY,OAAO,CAAC,YAAY,CAAG,KAE1E,EAAG,CAAC,EAAU,EAEd,IAAM,EAAa,UACjB,GAAI,CACF,MAAM,EAAY,EAAM,EAAE,CAAE,SAAE,CAAQ,GACtC,GAAa,EACf,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0BAA2B,GACzC,MAAM,yBACR,CACF,EAEM,EAAgB,AAAC,IACP,UAAV,CAAqB,CAAnB,GAAG,EAAiB,EAAE,QAAQ,CAOf,CAPiB,SAOP,CAApB,EAAE,GAAG,GACd,EAAW,EAAM,OAAO,EACxB,GAAa,KARb,EAAE,cAAc,GACZ,GAAU,EAAQ,IAAI,GACxB,CAD4B,CACjB,QAEX,IAMN,EAEM,EAAe,UACnB,GAAI,QAAQ,+CACV,CAD0D,EACtD,CACF,MAAM,EAAY,EAAM,EAAE,CAC5B,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0BAA2B,GACzC,MAAM,yBACR,CAEJ,EAEM,EAAmB,MAAO,IAC9B,GAAI,CACF,MAAM,EAAY,EAAM,EAAE,CAAE,CAAE,KAAM,CAAQ,GAC5C,GAAY,EACd,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,+BAAgC,GAC9C,MAAM,8BACR,CACF,EAaM,EAAgB,AAAC,IACrB,OAAQ,GACN,IAAK,UACH,MAAO,iCACT,KAAK,OACH,MAAO,4EACT,KAAK,QACH,MAAO,gFACT,SACE,MAAO,eACX,CACF,EAiDA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAY,CAAC,GAC5B,UAAU,kIACV,MAAM,6BAEL,CAhFY,AAAC,IACpB,OAAQ,GACN,IAAK,UAAW,MAAO,IACvB,KAAK,cAAe,MAAO,GAC3B,KAAK,gBAAiB,MAAO,IAC7B,KAAK,OAAQ,MAAO,IACpB,KAAK,QAAS,MAAO,IACrB,SAAS,MAAO,IAClB,EACF,EAuEsB,EAAM,IAAI,IAGzB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAiB,QAChC,UAAU,oFACX,YAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAiB,WAChC,UAAU,oFACX,eAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAiB,eAChC,UAAU,oFACX,kBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAiB,iBAChC,UAAU,oFACX,qBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAiB,QAChC,UAAU,oFACX,YAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAiB,SAChC,UAAU,oFACX,qBAST,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0BACZ,CAzGe,KACpB,GAAI,EAAW,CACb,IAAM,EAA8B,SAAf,EAAM,IAAI,CAC3B,yHACA,CAAC,4CAA4C,EAAE,EAAc,EAAM,IAAI,EAAE,eAAe,CAAC,CAE7F,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,IAAK,EACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAW,EAAE,MAAM,CAAC,KAAK,EAC1C,UAAW,EACX,OAAQ,EACR,UAAW,EACX,YAA4B,SAAf,EAAM,IAAI,CAAc,0BAA4B,oBACjE,KAAqB,SAAf,EAAM,IAAI,CAAc,EAAI,GAGxC,CAEA,GAAI,CAAC,EAAQ,IAAI,GACf,CADmB,KAEjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,QAAS,IAAM,GAAa,GAC5B,UAAU,0CACX,sBAML,IAAM,EAAkC,gBAAf,EAAM,IAAI,CAC/B,EAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,GAAQ,EAAK,IAAI,GAAK,CAAC,EAAE,EAAE,EAAA,CAAM,CAAG,GAAM,IAAI,CAAC,MACxD,kBAAf,EAAM,IAAI,CACV,EAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAM,IAAM,EAAK,IAAI,GAAK,CAAA,EAAG,EAAI,EAAE,EAAE,EAAE,EAAA,CAAM,CAAG,GAAM,IAAI,CAAC,MACpF,EAEJ,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,QAAS,IAAM,GAAa,GAC5B,UAAW,CAAC,qCAAqC,EAAE,EAAc,EAAM,IAAI,EAAA,CAAG,UAE7E,IAGP,MAgEI,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gEACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,4EACV,MAAM,wBAEN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yIAMjF,CCrNe,SAAS,EAAW,CAAE,KAAM,CAAW,CAAmB,EACvE,GAAM,cAAE,CAAY,YAAE,CAAU,CAAE,aAAW,UAAE,CAAQ,aAAE,CAAW,CAAE,CAAG,IACnE,CAAC,EAAgB,EAAkB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAC/C,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAY,KAAK,EAC9C,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAY,WAAW,EAAI,IAEpE,EAAO,GAAgB,EAE7B,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACJ,EAAY,EAAE,EAAE,AAClB,EAAS,EAAY,EAAE,CAE3B,EAAG,CAAC,EAAY,EAAE,CAAC,EAEnB,IAAM,EAAkB,UACtB,GAAI,CACF,MAAM,EAAW,EAAK,EAAE,CAAE,OAAE,EAAO,YAAa,QAAe,CAAU,GACzE,GAAkB,EACpB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,yBAA0B,GACxC,MAAM,wBACR,CACF,EAEM,EAAiB,MAAO,EAAe,MAAM,IACjD,GAAI,CACF,IAAM,EAAW,EAAK,MAAM,CAAC,MAC7B,AADmC,OAC7B,EAAY,EAAK,EAAE,CAAE,EAAM,GAAI,EACvC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0BAA2B,GACzC,MAAM,yBACR,CACF,EAYA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACZ,EACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,UAAU,kFACV,YAAY,aACZ,SAAS,CAAA,CAAA,IAEX,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAe,EAAE,MAAM,CAAC,KAAK,EAC9C,UAAU,2EACV,YAAY,uBACZ,KAAM,IAER,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,8EACX,SAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,KACP,EAAS,EAAK,KAAK,EACnB,EAAe,EAAK,WAAW,EAAI,IACnC,GAAkB,EACpB,EACA,UAAU,iFACX,iBAML,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,QAAS,IAAM,GAAkB,GAAO,UAAU,2BACrD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,8EACX,EAAK,KAAK,GAEZ,EAAK,WAAW,EACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sDACV,EAAK,WAAW,MAMzB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WAAI,cACQ,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uBAAe,EAAK,MAAM,CAAC,IAAI,EAAI,EAAK,MAAM,CAAC,QAAQ,MAEpF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WAAI,gBAlEJ,IAAI,KAAK,AAmEiB,EAAK,SAAS,EAnEzB,kBAAkB,CAAC,EAAE,CAAE,CAC3C,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,SACV,YAmEE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAEZ,CAAC,GAAe,CAAC,EAAK,MAAA,AAAM,GAC3B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iEACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8BAAqB,+BAKxC,EAAK,MAAM,EACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAK,MAAM,CAAC,GAAG,CAAC,CAAC,EAAO,IACvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAEC,MAAO,EACP,OAAQ,EAAK,EAAE,CACf,OAAQ,IAAU,EAAK,MAAM,CAAC,MAAM,CAAG,EACvC,WAAY,GAJP,EAAM,EAAE,KAWpB,EAAK,MAAM,EACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAe,QAC9B,UAAU,wGAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,qBAEvE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,mBAAU,iBAI9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,iDAChB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,uBAGzE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8KACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAe,QAC9B,UAAU,oFACX,YAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAe,WAC9B,UAAU,oFACX,eAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAe,eAC9B,UAAU,oFACX,kBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAe,iBAC9B,UAAU,oFACX,qBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAe,QAC9B,UAAU,oFACX,YAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAe,SAC9B,UAAU,oFACX,iCAarB,CCzMe,SAAS,EAAe,OAAE,CAAK,CAAuB,EACnE,GAAM,OAAE,CAAK,cAAE,CAAY,SAAE,CAAO,WAAE,CAAS,CAAE,YAAU,YAAE,CAAU,CAAE,CAAG,IACtE,CAAC,EAAiB,EAAmB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjD,CAAC,EAAe,EAAiB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAEnD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACJ,GACF,EAAU,EADD,AACO,EAAE,CAEtB,EAAG,CAAC,EAAM,EAAE,CAAC,EAEb,IAAM,EAAmB,MAAO,IAC9B,GAAI,QAAQ,8CACV,CADyD,EACrD,CACF,MAAM,EAAW,EACnB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,yBAA0B,GACxC,MAAM,wBACR,CAEJ,SAYA,AAAI,GAAgB,CAAC,EAEjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAF+B,IAE/B,CAAI,UAAU,iCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAiB,GAChC,UAAU,0DACX,sBAIH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAW,KAAM,OAMtB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+CAAsC,UACpD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAmB,GAClC,UAAU,8EACX,kBAOL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sCACZ,EACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yEACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,wBAEjB,IAAjB,EAAM,MAAM,CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAAuC,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC9F,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,2HAEvE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,kDAAyC,iBACvD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,kDAClC,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAmB,GAClC,UAAU,8EACX,mBAKH,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDACZ,EAAM,GAAG,CAAC,AAAC,GACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAU,qGACV,QAAS,KACP,EAAW,GACX,EAAiB,GACnB,WAEA,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,sDACX,EAAK,KAAK,GAEb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,AAAC,IACR,EAAE,eAAe,GACjB,EAAiB,EAAK,EAAE,CAC1B,EACA,UAAU,uCACV,MAAM,uBAEN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wIAK1E,EAAK,WAAW,EACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mDACV,EAAK,WAAW,GAIrB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uBACb,EAAK,MAAM,CAAC,IAAI,EAAI,EAAK,MAAM,CAAC,QAAQ,GAE3C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,gBAAO,MACvB,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WAAM,EAAK,MAAM,CAAC,MAAM,CAAC,gBAE5B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UA1GZ,CA0GkB,GA1Gd,KAAK,AA0GoB,EAAK,SAAS,EA1G5B,kBAAkB,CAAC,EAAE,CAAE,CAC3C,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,SACV,YA4DmB,EAAK,EAAE,UAkDxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,OAAQ,EACR,QAAS,IAAM,GAAmB,GAClC,QAAS,EAAM,EAAE,KAIzB,CCpJe,SAAS,IACtB,GAAM,eAAE,CAAa,CAAE,WAAS,CAAE,CAAG,IAC/B,MAAE,CAAI,CAAE,CAAG,CAAA,EAAA,EAAA,OAAA,AAAO,IAClB,CAAC,EAAe,EAAiB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAC7C,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACzC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAqB,UAC3D,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAA+B,QAEzE,GAAI,CAAC,EACH,MACE,CAAA,EAAA,EAAA,EAFgB,CAEhB,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAAuC,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC9F,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6QAEvE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,kDAAyC,mBACvD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,4EAMrC,IAAM,EAAiB,EAAc,OAAO,GAAK,GAAM,IACrD,EAAc,OAAO,CAAC,IAAI,CAAC,GAAK,EAAE,MAAM,GAAK,GAAM,IAAM,CAAC,QAAS,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,GAEvF,EAAkB,MAAO,IAC7B,EAAE,cAAc,GAChB,EAAS,IACT,GAAW,GAEX,GAAI,CACF,MAAM,EAAU,EAAc,EAAE,CAAE,EAAa,GAC/C,EAAe,IACf,EAAc,UACd,GAAiB,EACnB,CAAE,MAAO,EAAK,CACZ,EAAS,aAAe,MAAQ,EAAI,OAAO,CAAG,uBAChD,QAAU,CACR,GAAW,EACb,CACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4CAEf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+CAAuC,EAAc,IAAI,GACtE,EAAc,SAAS,EACtB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6BAA6B,KAAK,eAAe,QAAQ,qBACtE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAS,UAAU,EAAE,yGAAyG,SAAS,iBAIlJ,EAAc,WAAW,EACxB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA8B,EAAc,WAAW,MAGvE,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAiB,GAChC,UAAU,8EACX,kBAOL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,QAC5B,UAAW,CAAC,yCAAyC,EACrC,SAAd,EACI,gCACA,6EAAA,CACJ,UACH,SAGD,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,WAC5B,UAAW,CAAC,yCAAyC,EACrC,YAAd,EACI,gCACA,6EAAA,CACJ,WACH,YACW,EAAc,MAAM,CAAC,OAAO,CAAC,OAEzC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,SAC5B,UAAW,CAAC,yCAAyC,EACrC,UAAd,EACI,gCACA,6EAAA,CACJ,WACH,UACS,EAAc,MAAM,CAAC,KAAK,CAAC,eAO3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCACE,SAAd,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAc,MAAO,IAGT,YAAd,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAoC,EAAc,MAAM,CAAC,OAAO,GAC/E,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,eAEzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CAAqC,EAAc,MAAM,CAAC,QAAQ,GACjF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,gBAEzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CAAsC,EAAc,MAAM,CAAC,KAAK,GAC/E,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,gBAK3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,cAEpD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,eACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAc,OAAO,CAAC,GAAG,CAAC,AAAC,GAC1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAoB,UAAU,8CAC7B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6EACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6CACb,CAAC,EAAO,IAAI,CAAC,IAAI,EAAI,EAAO,IAAI,CAAC,QAAA,AAAQ,EAAE,MAAM,CAAC,GAAG,WAAW,OAGrE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,6CACV,EAAO,IAAI,CAAC,IAAI,EAAI,EAAO,IAAI,CAAC,QAAQ,GAE3C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAyB,EAAO,IAAI,CAAC,KAAK,SAG3D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,oEAAoE,EACpE,UAAhB,EAAO,IAAI,CAAe,8BAC1B,AAAgB,YAAT,IAAI,CAAe,4BAC1B,4BAAA,CACA,UACC,EAAO,IAAI,OApBR,EAAO,EAAE,eAgClB,UAAd,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAe,MAAO,OAK1B,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2CAAkC,eAChD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAiB,GAChC,UAAU,6CAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gCAK1E,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gFACZ,IAIL,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,YACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,sDAA6C,kBAG9D,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,QACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAe,EAAE,MAAM,CAAC,KAAK,EAC9C,UAAU,6HACV,YAAY,qBACZ,QAAQ,CAAA,CAAA,OAIZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,sDAA6C,SAG9D,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,UAAU,uHAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,WACvB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,iBAAQ,gBAI1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAM,GAAiB,GAChC,UAAU,mFACX,WAGD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,EACV,UAAU,0FAET,EAAU,YAAc,gCAW7C,CXjQe,SAAS,IACtB,GAAM,MAAE,CAAI,SAAE,CAAO,QAAE,CAAM,CAAE,CAAG,CAAA,EAAA,EAAA,OAAO,AAAP,IAC5B,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAQxB,GANA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACJ,AAAC,GAAY,GACf,EAAO,CADc,EAAP,CACH,CAAC,QAEhB,EAAG,CAAC,EAAM,EAAS,EAAO,EAEtB,EACF,MACE,CAFS,AAET,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2EACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,oBAM1C,GAAI,CAAC,EACH,IADS,GACF,KAAK,AAGd,IAAM,EAAe,UACnB,MAAM,EAJ8B,EAKpC,EAAO,IAAI,CAAC,QACd,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDAEb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,2BAChB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,eAEnD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAAwB,YAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uBAAe,EAAK,IAAI,EAAI,EAAK,QAAQ,MAEpE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,sFACX,qBAST,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,UAKX"}