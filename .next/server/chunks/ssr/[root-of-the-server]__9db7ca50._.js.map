{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/src/app/page.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (user) {\n        router.push('/dashboard')\n      } else {\n        router.push('/auth')\n      }\n    }\n  }, [user, loading, router])\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n        <p className=\"mt-4 text-gray-600\">Loading MyBinder...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext", "HooksClientContext", "ServerInsertedHtml"], "mappings": "+iBAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACC,gBAAgB,+BCFvCJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACE,kBAAkB,+BCFzCL,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACG,kBAAkB,iECAzC,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,GAAM,MAAE,CAAI,SAAE,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,OAAA,AAAO,IAC3B,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAYxB,MAVA,CAAA,EAAA,EAAA,SAAS,AAAT,EAAU,KACH,IACC,EACF,EAAO,CAFG,CACF,EACG,CAAC,cAEZ,EAAO,IAAI,CAAC,SAGlB,EAAG,CAAC,EAAM,EAAS,EAAO,EAGxB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2EACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,4BAI1C", "ignoreList": [0, 1, 2]}