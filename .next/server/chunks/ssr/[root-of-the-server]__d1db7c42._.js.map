{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { create<PERSON>ontext, useContext, useEffect, useState } from 'react'\nimport { User } from '@/types'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  login: (email: string, password: string) => Promise<void>\n  register: (email: string, username: string, password: string, name?: string) => Promise<void>\n  logout: () => Promise<void>\n  refreshUser: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  const refreshUser = async () => {\n    try {\n      const response = await fetch('/api/auth/me')\n      if (response.ok) {\n        const data = await response.json()\n        setUser(data.user)\n      } else {\n        setUser(null)\n      }\n    } catch (error) {\n      console.error('Failed to refresh user:', error)\n      setUser(null)\n    }\n  }\n\n  const login = async (email: string, password: string) => {\n    const response = await fetch('/api/auth/login', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email, password }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Login failed')\n    }\n\n    const data = await response.json()\n    setUser(data.user)\n  }\n\n  const register = async (email: string, username: string, password: string, name?: string) => {\n    const response = await fetch('/api/auth/register', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email, username, password, name }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Registration failed')\n    }\n\n    // After successful registration, log the user in\n    await login(email, password)\n  }\n\n  const logout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' })\n    } catch (error) {\n      console.error('Logout error:', error)\n    } finally {\n      setUser(null)\n    }\n  }\n\n  useEffect(() => {\n    refreshUser().finally(() => setLoading(false))\n  }, [])\n\n  return (\n    <AuthContext.Provider value={{\n      user,\n      loading,\n      login,\n      register,\n      logout,\n      refreshUser,\n    }}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAcA,MAAM,4BAAc,IAAA,sNAAa,EAA8B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;YACnB,OAAO;gBACL,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,QAAQ;QACV;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,MAAM,WAAW,MAAM,MAAM,mBAAmB;YAC9C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,KAAK,IAAI;IACnB;IAEA,MAAM,WAAW,OAAO,OAAe,UAAkB,UAAkB;QACzE,MAAM,WAAW,MAAM,MAAM,sBAAsB;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;gBAAU;gBAAU;YAAK;QACzD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,iDAAiD;QACjD,MAAM,MAAM,OAAO;IACrB;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;QACV;IACF;IAEA,IAAA,kNAAS,EAAC;QACR,cAAc,OAAO,CAAC,IAAM,WAAW;IACzC,GAAG,EAAE;IAEL,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}