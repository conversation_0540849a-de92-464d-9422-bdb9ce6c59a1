{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/src/contexts/AuthContext.tsx"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "'use client'\n\nimport React, { create<PERSON>ontext, useContext, useEffect, useState } from 'react'\nimport { User } from '@/types'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  login: (email: string, password: string) => Promise<void>\n  register: (email: string, username: string, password: string, name?: string) => Promise<void>\n  logout: () => Promise<void>\n  refreshUser: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  const refreshUser = async () => {\n    try {\n      const response = await fetch('/api/auth/me')\n      if (response.ok) {\n        const data = await response.json()\n        setUser(data.user)\n      } else {\n        setUser(null)\n      }\n    } catch (error) {\n      console.error('Failed to refresh user:', error)\n      setUser(null)\n    }\n  }\n\n  const login = async (email: string, password: string) => {\n    const response = await fetch('/api/auth/login', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email, password }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Login failed')\n    }\n\n    const data = await response.json()\n    setUser(data.user)\n  }\n\n  const register = async (email: string, username: string, password: string, name?: string) => {\n    const response = await fetch('/api/auth/register', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email, username, password, name }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Registration failed')\n    }\n\n    // After successful registration, log the user in\n    await login(email, password)\n  }\n\n  const logout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' })\n    } catch (error) {\n      console.error('Logout error:', error)\n    } finally {\n      setUser(null)\n    }\n  }\n\n  useEffect(() => {\n    refreshUser().finally(() => setLoading(false))\n  }, [])\n\n  return (\n    <AuthContext.Provider value={{\n      user,\n      loading,\n      login,\n      register,\n      logout,\n      refreshUser,\n    }}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React"], "mappings": "0NA0BQG,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,+BCFxCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,sFCA9B,EAAA,EAAA,CAAA,CAAA,OAYA,IAAM,EAAc,CAAA,EAAA,EAAA,aAAA,AAAa,OAA8B,GAExD,SAAS,EAAa,UAAE,CAAQ,CAAiC,EACtE,GAAM,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAc,MACxC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAEjC,EAAc,UAClB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,gBAC7B,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAQ,EAAK,IAAI,CACnB,MACE,CADK,CACG,KAEZ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0BAA2B,GACzC,EAAQ,KACV,CACF,EAEM,EAAQ,MAAO,EAAe,KAClC,IAAM,EAAW,MAAM,MAAM,kBAAmB,CAC9C,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,OAAE,WAAO,CAAS,EACzC,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,gBAIjC,EAAQ,CADK,MAAM,EAAS,IAAI,EAAA,EACnB,IAAI,CACnB,EAEM,EAAW,MAAO,EAAe,EAAkB,EAAkB,KACzE,IAAM,EAAW,MAAM,MAAM,qBAAsB,CACjD,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,OAAE,WAAO,WAAU,OAAU,CAAK,EACzD,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,KAEV,AAAI,MAAM,CADF,MAAM,EAAS,IAAI,EAAA,EACX,KAAK,EAAI,sBAIjC,OAAM,EAAM,EAAO,EACrB,EAEM,EAAS,UACb,GAAI,CACF,MAAM,MAAM,mBAAoB,CAAE,OAAQ,MAAO,EACnD,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,gBAAiB,EACjC,QAAU,CACR,EAAQ,KACV,CACF,EAMA,MAJA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,IAAc,OAAO,CAAC,IAAM,GAAW,GACzC,EAAG,EAAE,EAGH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAY,QAAQ,CAAA,CAAC,MAAO,MAC3B,EACA,gBACA,WACA,SACA,cACA,CACF,WACG,GAGP,CAEO,SAAS,IACd,IAAM,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,GAC3B,QAAgB,IAAZ,EACF,KADyB,CACnB,AAAI,MAAM,+CAElB,OAAO,CACT", "ignoreList": [0, 1, 2]}