module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},11642,a=>{"use strict";a.s(["AuthProvider",()=>e,"useAuth",()=>f]);var b=a.i(87924),c=a.i(72131);let d=(0,c.createContext)(void 0);function e({children:a}){let[e,f]=(0,c.useState)(null),[g,h]=(0,c.useState)(!0),i=async()=>{try{let a=await fetch("/api/auth/me");if(a.ok){let b=await a.json();f(b.user)}else f(null)}catch(a){console.error("Failed to refresh user:",a),f(null)}},j=async(a,b)=>{let c=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:b})});if(!c.ok)throw Error((await c.json()).error||"Login failed");f((await c.json()).user)},k=async(a,b,c,d)=>{let e=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,username:b,password:c,name:d})});if(!e.ok)throw Error((await e.json()).error||"Registration failed");await j(a,c)},l=async()=>{try{await fetch("/api/auth/logout",{method:"POST"})}catch(a){console.error("Logout error:",a)}finally{f(null)}};return(0,c.useEffect)(()=>{i().finally(()=>h(!1))},[]),(0,b.jsx)(d.Provider,{value:{user:e,loading:g,login:j,register:k,logout:l,refreshUser:i},children:a})}function f(){let a=(0,c.useContext)(d);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__0ae2b81b._.js.map