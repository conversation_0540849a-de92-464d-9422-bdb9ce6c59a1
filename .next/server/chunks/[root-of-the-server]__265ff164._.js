module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>a,"requireAuth",()=>s]);var t=e.i(79832),r=e.i(98043);async function a(e){try{let a=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!a)return null;let s=(0,t.verifyToken)(a);if(!s)return null;return await r.prisma.user.findUnique({where:{id:s.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function s(e){return async t=>{let r=await a(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},30090,(e,t,r)=>{},47500,e=>{"use strict";e.s(["handler",()=>k,"patchFetch",()=>I,"routeModule",()=>j,"serverHooks",()=>q,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>b],47500);var t=e.i(47909),r=e.i(74017),a=e.i(96250),s=e.i(59756),n=e.i(61916),o=e.i(69741),i=e.i(16795),u=e.i(87718),l=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),h=e.i(26937),x=e.i(10372),g=e.i(93695);e.i(52474);var m=e.i(220);e.s(["GET",()=>A,"POST",()=>E],70289);var R=e.i(89171),v=e.i(98043),f=e.i(24960),w=e.i(69719);let y=w.z.object({content:w.z.string().min(1,"Message content is required").max(2e3,"Message too long")});async function A(e,{params:t}){try{let r=await (0,f.getAuthenticatedUser)(e);if(!r)return R.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:a}=await t,{searchParams:s}=new URL(e.url),n=parseInt(s.get("page")||"1"),o=parseInt(s.get("limit")||"50"),i=(n-1)*o;if(!await v.prisma.groupMember.findFirst({where:{groupId:a,userId:r.id}}))return R.NextResponse.json({error:"Access denied. You are not a member of this group."},{status:403});let u=await v.prisma.message.findMany({where:{groupId:a},include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}},orderBy:{createdAt:"desc"},take:o,skip:i}),l=await v.prisma.message.count({where:{groupId:a}}),d=Math.ceil(l/o);return R.NextResponse.json({messages:u.reverse(),pagination:{page:n,limit:o,totalMessages:l,totalPages:d,hasMore:n<d}})}catch(e){return console.error("Get messages error:",e),R.NextResponse.json({error:"Failed to fetch messages"},{status:500})}}async function E(e,{params:t}){try{let r=await (0,f.getAuthenticatedUser)(e);if(!r)return R.NextResponse.json({error:"Authentication required"},{status:401});let{groupId:a}=await t,s=await e.json(),{content:n}=y.parse(s);if(!await v.prisma.groupMember.findFirst({where:{groupId:a,userId:r.id}}))return R.NextResponse.json({error:"Access denied. You are not a member of this group."},{status:403});let o=await v.prisma.message.create({data:{content:n,authorId:r.id,groupId:a},include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}}});return R.NextResponse.json({message:"Message sent successfully",data:o})}catch(e){if(e instanceof w.z.ZodError)return R.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Send message error:",e),R.NextResponse.json({error:"Failed to send message"},{status:500})}}var C=e.i(70289);let j=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/groups/[groupId]/messages/route",pathname:"/api/groups/[groupId]/messages",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/groups/[groupId]/messages/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:N,workUnitAsyncStorage:b,serverHooks:q}=j;function I(){return(0,a.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:b})}async function k(e,t,a){var R;let v="/api/groups/[groupId]/messages/route";v=v.replace(/\/index$/,"")||"/";let f=await j.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:A,isDraftMode:E,prerenderManifest:C,routerServerContext:N,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,resolvedPathname:I}=f,k=(0,o.normalizeAppPath)(v),T=!!(C.dynamicRoutes[k]||C.routes[I]);if(T&&!E){let e=!!C.routes[I],t=C.dynamicRoutes[k];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let O=null;!T||j.isDev||E||(O="/index"===(O=I)?"/":O);let P=!0===j.isDev||!T,M=T&&!P,S=e.method||"GET",U=(0,n.getTracer)(),_=U.getActiveScopeSpan(),H={params:y,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!A.experimental.cacheComponents,authInterrupts:!!A.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=A.experimental)?void 0:R.cacheLife,isRevalidate:M,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>j.onRequestError(e,t,a,N)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=u.NextRequestAdapter.fromNodeNextRequest(D,(0,u.signalFromNodeResponse)(t));try{let o=async r=>j.handle($,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${S} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${S} ${e.url}`)}),i=async n=>{var i,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&b&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(n);e.fetchMetrics=H.renderOpts.fetchMetrics;let u=H.renderOpts.pendingWaitUntil;u&&a.waitUntil&&(a.waitUntil(u),u=void 0);let l=H.renderOpts.collectedTags;if(!T)return await (0,p.sendResponse)(D,F,i,H.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[x.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,a=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await j.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:b})},N),t}},g=await j.handleResponse({req:e,nextConfig:A,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,responseGenerator:l,waitUntil:a.waitUntil});if(!T)return null;if((null==g||null==(i=g.value)?void 0:i.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(u=g.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),E&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,c.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&T||R.delete(x.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,h.getCacheControlHeader)(g.cacheControl)),await (0,p.sendResponse)(D,F,new Response(g.value.body,{headers:R,status:g.value.status||200})),null};_?await i(_):await U.withPropagatedContext(e.headers,()=>U.trace(l.BaseServerSpan.handleRequest,{spanName:`${S} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":S,"http.target":e.url}},i))}catch(t){if(_||t instanceof g.NoFallbackError||await j.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:b})}),T)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__265ff164._.js.map