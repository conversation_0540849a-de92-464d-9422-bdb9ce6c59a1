module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},24960,e=>{"use strict";e.s(["getAuthenticatedUser",()=>n,"requireAuth",()=>a]);var t=e.i(79832),r=e.i(98043);async function n(e){try{let n=e.cookies.get("auth-token")?.value||e.headers.get("authorization")?.replace("Bearer ","");if(!n)return null;let a=(0,t.verifyToken)(n);if(!a)return null;return await r.prisma.user.findUnique({where:{id:a.userId},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0,updatedAt:!0}})}catch(e){return console.error("Auth middleware error:",e),null}}function a(e){return async t=>{let r=await n(t);return r?e(t,r):new Response(JSON.stringify({error:"Authentication required"}),{status:401,headers:{"Content-Type":"application/json"}})}}},86840,(e,t,r)=>{},90763,e=>{"use strict";e.s(["handler",()=>I,"patchFetch",()=>k,"routeModule",()=>C,"serverHooks",()=>T,"workAsyncStorage",()=>b,"workUnitAsyncStorage",()=>q],90763);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),u=e.i(87718),l=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var R=e.i(220);e.s(["DELETE",()=>A,"GET",()=>E,"PUT",()=>N],87857);var f=e.i(89171),v=e.i(98043),g=e.i(24960),w=e.i(69719);let y=w.z.object({title:w.z.string().min(1,"Note title is required").max(200,"Title too long").optional(),description:w.z.string().max(500,"Description too long").optional()});async function E(e,{params:t}){try{let r=await (0,g.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{noteId:n}=await t,a=await v.prisma.note.findFirst({where:{id:n,group:{members:{some:{userId:r.id}}}},include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}},group:{select:{id:!0,name:!0}},blocks:{include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}},orderBy:{order:"asc"}}}});if(!a)return f.NextResponse.json({error:"Note not found or access denied"},{status:404});return f.NextResponse.json({note:a})}catch(e){return console.error("Get note error:",e),f.NextResponse.json({error:"Failed to fetch note"},{status:500})}}async function N(e,{params:t}){try{let r=await (0,g.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{noteId:n}=await t,a=await e.json(),o=y.parse(a);if(!await v.prisma.note.findFirst({where:{id:n,group:{members:{some:{userId:r.id}}}}}))return f.NextResponse.json({error:"Note not found or access denied"},{status:404});let s=await v.prisma.note.update({where:{id:n},data:o,include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}},blocks:{include:{author:{select:{id:!0,username:!0,name:!0,avatar:!0}}},orderBy:{order:"asc"}}}});return f.NextResponse.json({message:"Note updated successfully",note:s})}catch(e){if(e instanceof w.z.ZodError)return f.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Update note error:",e),f.NextResponse.json({error:"Failed to update note"},{status:500})}}async function A(e,{params:t}){try{let r=await (0,g.getAuthenticatedUser)(e);if(!r)return f.NextResponse.json({error:"Authentication required"},{status:401});let{noteId:n}=await t;if(!await v.prisma.note.findFirst({where:{id:n,OR:[{authorId:r.id},{group:{OR:[{ownerId:r.id},{members:{some:{userId:r.id,role:{in:["OWNER","ADMIN"]}}}}]}}]}}))return f.NextResponse.json({error:"Note not found or insufficient permissions"},{status:404});return await v.prisma.note.delete({where:{id:n}}),f.NextResponse.json({message:"Note deleted successfully"})}catch(e){return console.error("Delete note error:",e),f.NextResponse.json({error:"Failed to delete note"},{status:500})}}var j=e.i(87857);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/notes/[noteId]/route",pathname:"/api/notes/[noteId]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/notes/[noteId]/route.ts",nextConfigOutput:"",userland:j}),{workAsyncStorage:b,workUnitAsyncStorage:q,serverHooks:T}=C;function k(){return(0,n.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:q})}async function I(e,t,n){var f;let v="/api/notes/[noteId]/route";v=v.replace(/\/index$/,"")||"/";let g=await C.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:N,prerenderManifest:A,routerServerContext:j,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,resolvedPathname:T}=g,k=(0,s.normalizeAppPath)(v),I=!!(A.dynamicRoutes[k]||A.routes[T]);if(I&&!N){let e=!!A.routes[T],t=A.dynamicRoutes[k];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!I||C.isDev||N||(O="/index"===(O=T)?"/":O);let P=!0===C.isDev||!I,U=I&&!P,_=e.method||"GET",S=(0,o.getTracer)(),H=S.getActiveScopeSpan(),D={params:y,prerenderManifest:A,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=E.experimental)?void 0:f.cacheLife,isRevalidate:U,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>C.onRequestError(e,t,n,j)},sharedContext:{buildId:w}},M=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=u.NextRequestAdapter.fromNodeNextRequest(M,(0,u.signalFromNodeResponse)(t));try{let s=async r=>C.handle($,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=S.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${_} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),i=async o=>{var i,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&b&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=D.renderOpts.fetchMetrics;let u=D.renderOpts.pendingWaitUntil;u&&n.waitUntil&&(n.waitUntil(u),u=void 0);let l=D.renderOpts.collectedTags;if(!I)return await (0,p.sendResponse)(M,F,i,D.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,n=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})},j),t}},m=await C.handleResponse({req:e,nextConfig:E,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:A,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,responseGenerator:l,waitUntil:n.waitUntil});if(!I)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(u=m.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),N&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&I||f.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(M,F,new Response(m.value.body,{headers:f,status:m.value.status||200})),null};H?await i(H):await S.withPropagatedContext(e.headers,()=>S.trace(l.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},i))}catch(t){if(H||t instanceof m.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})}),I)throw t;return await (0,p.sendResponse)(M,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9a147acf._.js.map