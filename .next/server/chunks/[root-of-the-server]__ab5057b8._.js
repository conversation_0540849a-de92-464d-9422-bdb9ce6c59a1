module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},96331,(e,t,r)=>{},6706,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>P,"routeModule",()=>b,"serverHooks",()=>N,"workAsyncStorage",()=>A,"workUnitAsyncStorage",()=>j],6706);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),i=e.i(69741),o=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var R=e.i(220);e.s(["POST",()=>y],26170);var v=e.i(89171),g=e.i(98043),f=e.i(79832),w=e.i(69719);let E=w.z.object({email:w.z.string().email("Invalid email format"),username:w.z.string().min(3,"Username must be at least 3 characters"),password:w.z.string().min(6,"Password must be at least 6 characters"),name:w.z.string().optional()});async function y(e){try{let t=await e.json(),{email:r,username:a,password:n,name:s}=E.parse(t);if(await g.prisma.user.findFirst({where:{OR:[{email:r},{username:a}]}}))return v.NextResponse.json({error:"User with this email or username already exists"},{status:400});let i=await (0,f.hashPassword)(n),o=await g.prisma.user.create({data:{email:r,username:a,password:i,name:s},select:{id:!0,email:!0,username:!0,name:!0,avatar:!0,createdAt:!0}});return v.NextResponse.json({message:"User created successfully",user:o})}catch(e){if(e instanceof w.z.ZodError)return v.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Registration error:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}var C=e.i(26170);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/auth/register/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:A,workUnitAsyncStorage:j,serverHooks:N}=b;function P(){return(0,a.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:j})}async function q(e,t,a){var v;let g="/api/auth/register/route";g=g.replace(/\/index$/,"")||"/";let f=await b.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:E,nextConfig:y,isDraftMode:C,prerenderManifest:A,routerServerContext:j,isOnDemandRevalidate:N,revalidateOnlyGenerated:P,resolvedPathname:q}=f,O=(0,i.normalizeAppPath)(g),T=!!(A.dynamicRoutes[O]||A.routes[q]);if(T&&!C){let e=!!A.routes[q],t=A.dynamicRoutes[O];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let k=null;!T||b.isDev||C||(k="/index"===(k=q)?"/":k);let _=!0===b.isDev||!T,S=T&&!_,U=e.method||"GET",H=(0,s.getTracer)(),I=H.getActiveScopeSpan(),M={params:E,prerenderManifest:A,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=y.experimental)?void 0:v.cacheLife,isRevalidate:S,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>b.onRequestError(e,t,a,j)},sharedContext:{buildId:w}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let i=async r=>b.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=H.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${U} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),o=async s=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&P&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!T)return await (0,p.sendResponse)(D,F,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:N})},j),t}},m=await b.handleResponse({req:e,nextConfig:y,cacheKey:k,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:A,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:P,responseGenerator:d,waitUntil:a.waitUntil});if(!T)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&T||v.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(D,F,new Response(m.value.body,{headers:v,status:m.value.status||200})),null};I?await o(I):await H.withPropagatedContext(e.headers,()=>H.trace(d.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},o))}catch(t){if(I||t instanceof m.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:O,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:N})}),T)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ab5057b8._.js.map