var R=require("../../../../../chunks/[turbopack]_runtime.js")("server/app/api/groups/[groupId]/notes/route.js")
R.c("server/chunks/node_modules_next_ee9ad71d._.js")
R.c("server/chunks/node_modules_zod_v4_9fe81a10._.js")
R.c("server/chunks/node_modules_426a7d25._.js")
R.c("server/chunks/[root-of-the-server]__76ba9390._.js")
R.m("[project]/.next-internal/server/app/api/groups/[groupId]/notes/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/groups/[groupId]/notes/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/groups/[groupId]/notes/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
