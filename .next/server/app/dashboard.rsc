1:"$Sreact.fragment"
2:I[57951,["/_next/static/chunks/1dcfb8883dc37f5d.js"],"AuthProvider"]
3:I[39756,["/_next/static/chunks/060f9a97930f3d04.js"],"default"]
4:I[37457,["/_next/static/chunks/060f9a97930f3d04.js"],"default"]
5:I[47257,["/_next/static/chunks/060f9a97930f3d04.js"],"ClientPageRoot"]
6:I[30257,["/_next/static/chunks/1dcfb8883dc37f5d.js","/_next/static/chunks/295d483fd0fe7058.js","/_next/static/chunks/0800e8b99e912037.js"],"default"]
9:I[97367,["/_next/static/chunks/060f9a97930f3d04.js"],"OutletBoundary"]
b:I[11533,["/_next/static/chunks/060f9a97930f3d04.js"],"AsyncMetadataOutlet"]
d:I[97367,["/_next/static/chunks/060f9a97930f3d04.js"],"ViewportBoundary"]
f:I[97367,["/_next/static/chunks/060f9a97930f3d04.js"],"MetadataBoundary"]
10:"$Sreact.suspense"
12:I[68027,["/_next/static/chunks/1dcfb8883dc37f5d.js"],"default"]
:HL["/_next/static/chunks/a4f98ba2bad5c8fd.css","style"]
:HL["/_next/static/media/797e433ab948586e-s.p.dbea232f.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/caa3a2e1cccd8315-s.p.853070df.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
0:{"P":null,"b":"BE8MNRRbVIiU1NrQEItT9","p":"","c":["","dashboard"],"i":false,"f":[[["",{"children":["dashboard",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/a4f98ba2bad5c8fd.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","script","script-0",{"src":"/_next/static/chunks/1dcfb8883dc37f5d.js","async":true,"nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"geist_a71539c9-module__T19VSG__variable geist_mono_8d43a2aa-module__8Li5zG__variable antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L5",null,{"Component":"$6","searchParams":{},"params":{},"promises":["$@7","$@8"]}],[["$","script","script-0",{"src":"/_next/static/chunks/295d483fd0fe7058.js","async":true,"nonce":"$undefined"}],["$","script","script-1",{"src":"/_next/static/chunks/0800e8b99e912037.js","async":true,"nonce":"$undefined"}]],["$","$L9",null,{"children":["$La",["$","$Lb",null,{"promise":"$@c"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Ld",null,{"children":"$Le"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$Lf",null,{"children":["$","div",null,{"hidden":true,"children":["$","$10",null,{"fallback":null,"children":"$L11"}]}]}]]}],false]],"m":"$undefined","G":["$12",[["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/a4f98ba2bad5c8fd.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]]],"s":false,"S":true}
7:{}
8:"$0:f:0:1:2:children:2:children:1:props:children:0:props:params"
e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
a:null
13:I[27201,["/_next/static/chunks/060f9a97930f3d04.js"],"IconMark"]
c:{"metadata":[["$","title","0",{"children":"MyBinder - Group Chat with Note-Taking"}],["$","meta","1",{"name":"description","content":"A collaborative platform for group communication and note-taking"}],["$","link","2",{"rel":"icon","href":"/favicon.ico?favicon.0b3bf435.ico","sizes":"256x256","type":"image/x-icon"}],["$","$L13","3",{}]],"error":null,"digest":"$undefined"}
11:"$c:metadata"
