[{"/home/<USER>/mybinder/src/__tests__/api/auth/login.test.ts": "1", "/home/<USER>/mybinder/src/__tests__/components/auth/LoginForm.test.tsx": "2", "/home/<USER>/mybinder/src/__tests__/components/groups/CreateGroupModal.test.tsx": "3", "/home/<USER>/mybinder/src/__tests__/lib/auth.test.ts": "4", "/home/<USER>/mybinder/src/app/api/auth/login/route.ts": "5", "/home/<USER>/mybinder/src/app/api/auth/logout/route.ts": "6", "/home/<USER>/mybinder/src/app/api/auth/me/route.ts": "7", "/home/<USER>/mybinder/src/app/api/auth/register/route.ts": "8", "/home/<USER>/mybinder/src/app/api/blocks/[blockId]/route.ts": "9", "/home/<USER>/mybinder/src/app/api/groups/[groupId]/members/route.ts": "10", "/home/<USER>/mybinder/src/app/api/groups/[groupId]/messages/route.ts": "11", "/home/<USER>/mybinder/src/app/api/groups/[groupId]/notes/route.ts": "12", "/home/<USER>/mybinder/src/app/api/groups/[groupId]/route.ts": "13", "/home/<USER>/mybinder/src/app/api/groups/route.ts": "14", "/home/<USER>/mybinder/src/app/api/notes/[noteId]/blocks/route.ts": "15", "/home/<USER>/mybinder/src/app/api/notes/[noteId]/route.ts": "16", "/home/<USER>/mybinder/src/app/auth/page.tsx": "17", "/home/<USER>/mybinder/src/app/dashboard/page.tsx": "18", "/home/<USER>/mybinder/src/app/layout.tsx": "19", "/home/<USER>/mybinder/src/app/page.tsx": "20", "/home/<USER>/mybinder/src/components/auth/LoginForm.tsx": "21", "/home/<USER>/mybinder/src/components/auth/RegisterForm.tsx": "22", "/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx": "23", "/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx": "24", "/home/<USER>/mybinder/src/components/groups/GroupList.tsx": "25", "/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx": "26", "/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx": "27", "/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx": "28", "/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx": "29", "/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx": "30", "/home/<USER>/mybinder/src/contexts/AuthContext.tsx": "31", "/home/<USER>/mybinder/src/contexts/GroupContext.tsx": "32", "/home/<USER>/mybinder/src/contexts/MessageContext.tsx": "33", "/home/<USER>/mybinder/src/contexts/NotesContext.tsx": "34", "/home/<USER>/mybinder/src/lib/auth.ts": "35", "/home/<USER>/mybinder/src/lib/middleware.ts": "36", "/home/<USER>/mybinder/src/lib/prisma.ts": "37", "/home/<USER>/mybinder/src/types/index.ts": "38"}, {"size": 4361, "mtime": 1758519672212, "results": "39", "hashOfConfig": "40"}, {"size": 3790, "mtime": 1758519165340, "results": "41", "hashOfConfig": "40"}, {"size": 4828, "mtime": 1758519205057, "results": "42", "hashOfConfig": "40"}, {"size": 2480, "mtime": 1758519227376, "results": "43", "hashOfConfig": "40"}, {"size": 2065, "mtime": 1758516378961, "results": "44", "hashOfConfig": "40"}, {"size": 363, "mtime": 1758516385389, "results": "45", "hashOfConfig": "40"}, {"size": 1342, "mtime": 1758516394725, "results": "46", "hashOfConfig": "40"}, {"size": 1802, "mtime": 1758516354165, "results": "47", "hashOfConfig": "40"}, {"size": 3435, "mtime": 1758517310888, "results": "48", "hashOfConfig": "40"}, {"size": 2942, "mtime": 1758516707149, "results": "49", "hashOfConfig": "40"}, {"size": 3921, "mtime": 1758516997181, "results": "50", "hashOfConfig": "40"}, {"size": 3871, "mtime": 1758517209465, "results": "51", "hashOfConfig": "40"}, {"size": 5123, "mtime": 1758516687949, "results": "52", "hashOfConfig": "40"}, {"size": 3157, "mtime": 1758516603953, "results": "53", "hashOfConfig": "40"}, {"size": 4564, "mtime": 1758517293628, "results": "54", "hashOfConfig": "40"}, {"size": 5226, "mtime": 1758517229489, "results": "55", "hashOfConfig": "40"}, {"size": 1814, "mtime": 1758516464217, "results": "56", "hashOfConfig": "40"}, {"size": 2206, "mtime": 1758516828600, "results": "57", "hashOfConfig": "40"}, {"size": 852, "mtime": 1758516480593, "results": "58", "hashOfConfig": "40"}, {"size": 749, "mtime": 1758516512205, "results": "59", "hashOfConfig": "40"}, {"size": 3634, "mtime": 1758516433773, "results": "60", "hashOfConfig": "40"}, {"size": 4669, "mtime": 1758516450705, "results": "61", "hashOfConfig": "40"}, {"size": 4694, "mtime": 1758516746860, "results": "62", "hashOfConfig": "40"}, {"size": 11794, "mtime": 1758520141668, "results": "63", "hashOfConfig": "40"}, {"size": 4634, "mtime": 1758516767713, "results": "64", "hashOfConfig": "40"}, {"size": 6562, "mtime": 1758517043541, "results": "65", "hashOfConfig": "40"}, {"size": 6989, "mtime": 1758517560653, "results": "66", "hashOfConfig": "40"}, {"size": 4077, "mtime": 1758517483161, "results": "67", "hashOfConfig": "40"}, {"size": 7533, "mtime": 1758517569669, "results": "68", "hashOfConfig": "40"}, {"size": 5986, "mtime": 1758517364752, "results": "69", "hashOfConfig": "40"}, {"size": 2692, "mtime": 1758516418065, "results": "70", "hashOfConfig": "40"}, {"size": 4479, "mtime": 1758516727132, "results": "71", "hashOfConfig": "40"}, {"size": 3223, "mtime": 1758517014552, "results": "72", "hashOfConfig": "40"}, {"size": 7352, "mtime": 1758517338416, "results": "73", "hashOfConfig": "40"}, {"size": 646, "mtime": 1758516269462, "results": "74", "hashOfConfig": "40"}, {"size": 1395, "mtime": 1758516404309, "results": "75", "hashOfConfig": "40"}, {"size": 279, "mtime": 1758516261622, "results": "76", "hashOfConfig": "40"}, {"size": 1214, "mtime": 1758516279564, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "de7olp", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/mybinder/src/__tests__/api/auth/login.test.ts", ["192", "193", "194", "195", "196", "197", "198"], [], "/home/<USER>/mybinder/src/__tests__/components/auth/LoginForm.test.tsx", ["199", "200"], [], "/home/<USER>/mybinder/src/__tests__/components/groups/CreateGroupModal.test.tsx", ["201"], [], "/home/<USER>/mybinder/src/__tests__/lib/auth.test.ts", [], [], "/home/<USER>/mybinder/src/app/api/auth/login/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/auth/logout/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/auth/me/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/auth/register/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/blocks/[blockId]/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/groups/[groupId]/members/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/groups/[groupId]/messages/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/groups/[groupId]/notes/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/groups/[groupId]/route.ts", [], [], "/home/<USER>/mybinder/src/app/api/groups/route.ts", ["202", "203"], [], "/home/<USER>/mybinder/src/app/api/notes/[noteId]/blocks/route.ts", ["204"], [], "/home/<USER>/mybinder/src/app/api/notes/[noteId]/route.ts", [], [], "/home/<USER>/mybinder/src/app/auth/page.tsx", [], [], "/home/<USER>/mybinder/src/app/dashboard/page.tsx", [], [], "/home/<USER>/mybinder/src/app/layout.tsx", [], [], "/home/<USER>/mybinder/src/app/page.tsx", [], [], "/home/<USER>/mybinder/src/components/auth/LoginForm.tsx", ["205"], [], "/home/<USER>/mybinder/src/components/auth/RegisterForm.tsx", [], [], "/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx", [], [], "/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx", [], [], "/home/<USER>/mybinder/src/components/groups/GroupList.tsx", [], [], "/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx", ["206", "207", "208"], [], "/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx", ["209"], [], "/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx", [], [], "/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx", ["210"], [], "/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx", ["211"], [], "/home/<USER>/mybinder/src/contexts/AuthContext.tsx", [], [], "/home/<USER>/mybinder/src/contexts/GroupContext.tsx", [], [], "/home/<USER>/mybinder/src/contexts/MessageContext.tsx", ["212"], [], "/home/<USER>/mybinder/src/contexts/NotesContext.tsx", ["213"], [], "/home/<USER>/mybinder/src/lib/auth.ts", ["214", "215", "216"], [], "/home/<USER>/mybinder/src/lib/middleware.ts", ["217"], [], "/home/<USER>/mybinder/src/lib/prisma.ts", [], [], "/home/<USER>/mybinder/src/types/index.ts", [], [], {"ruleId": "218", "severity": 1, "message": "219", "line": 3, "column": 10, "nodeType": null, "messageId": "220", "endLine": 3, "endColumn": 22}, {"ruleId": "221", "severity": 2, "message": "222", "line": 39, "column": 43, "nodeType": "223", "messageId": "224", "endLine": 39, "endColumn": 64}, {"ruleId": "225", "severity": 2, "message": "226", "line": 70, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 70, "endColumn": 13, "suggestions": "229"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 97, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 97, "endColumn": 13, "suggestions": "230"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 128, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 128, "endColumn": 13, "suggestions": "231"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 145, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 145, "endColumn": 13, "suggestions": "232"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 164, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 164, "endColumn": 13, "suggestions": "233"}, {"ruleId": "218", "severity": 1, "message": "234", "line": 1, "column": 26, "nodeType": null, "messageId": "220", "endLine": 1, "endColumn": 35}, {"ruleId": "218", "severity": 1, "message": "235", "line": 4, "column": 10, "nodeType": null, "messageId": "220", "endLine": 4, "endColumn": 22}, {"ruleId": "218", "severity": 1, "message": "234", "line": 1, "column": 26, "nodeType": null, "messageId": "220", "endLine": 1, "endColumn": 35}, {"ruleId": "225", "severity": 2, "message": "226", "line": 13, "column": 67, "nodeType": "227", "messageId": "228", "endLine": 13, "endColumn": 70, "suggestions": "236"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 71, "column": 68, "nodeType": "227", "messageId": "228", "endLine": 71, "endColumn": 71, "suggestions": "237"}, {"ruleId": "218", "severity": 1, "message": "238", "line": 12, "column": 7, "nodeType": null, "messageId": "220", "endLine": 12, "endColumn": 24}, {"ruleId": "239", "severity": 2, "message": "240", "line": 91, "column": 18, "nodeType": "241", "messageId": "242", "suggestions": "243"}, {"ruleId": "244", "severity": 1, "message": "245", "line": 24, "column": 6, "nodeType": "246", "endLine": 24, "endColumn": 16, "suggestions": "247"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 76, "column": 52, "nodeType": "227", "messageId": "228", "endLine": 76, "endColumn": 55, "suggestions": "248"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 76, "column": 74, "nodeType": "227", "messageId": "228", "endLine": 76, "endColumn": 77, "suggestions": "249"}, {"ruleId": "218", "severity": 1, "message": "250", "line": 14, "column": 46, "nodeType": null, "messageId": "220", "endLine": 14, "endColumn": 52}, {"ruleId": "244", "severity": 1, "message": "251", "line": 24, "column": 6, "nodeType": "246", "endLine": 24, "endColumn": 22, "suggestions": "252"}, {"ruleId": "244", "severity": 1, "message": "253", "line": 22, "column": 6, "nodeType": "246", "endLine": 22, "endColumn": 16, "suggestions": "254"}, {"ruleId": "218", "severity": 1, "message": "255", "line": 3, "column": 44, "nodeType": null, "messageId": "220", "endLine": 3, "endColumn": 53}, {"ruleId": "218", "severity": 1, "message": "255", "line": 3, "column": 44, "nodeType": null, "messageId": "220", "endLine": 3, "endColumn": 53}, {"ruleId": "225", "severity": 2, "message": "226", "line": 14, "column": 40, "nodeType": "227", "messageId": "228", "endLine": 14, "endColumn": 43, "suggestions": "256"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 18, "column": 45, "nodeType": "227", "messageId": "228", "endLine": 18, "endColumn": 48, "suggestions": "257"}, {"ruleId": "218", "severity": 1, "message": "258", "line": 21, "column": 12, "nodeType": null, "messageId": "220", "endLine": 21, "endColumn": 17}, {"ruleId": "225", "severity": 2, "message": "226", "line": 42, "column": 67, "nodeType": "227", "messageId": "228", "endLine": 42, "endColumn": 70, "suggestions": "259"}, "@typescript-eslint/no-unused-vars", "'hashPassword' is defined but never used.", "unusedVar", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["260", "261"], ["262", "263"], ["264", "265"], ["266", "267"], ["268", "269"], "'fireEvent' is defined but never used.", "'AuthProvider' is defined but never used.", ["270", "271"], ["272", "273"], "'updateBlockSchema' is assigned a value but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["274", "275", "276", "277"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'group' and 'loadMessages'. Either include them or remove the dependency array.", "ArrayExpression", ["278"], ["279", "280"], ["281", "282"], "'noteId' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadNote'. Either include it or remove the dependency array.", ["283"], "React Hook useEffect has missing dependencies: 'group' and 'loadNotes'. Either include them or remove the dependency array.", ["284"], "'useEffect' is defined but never used.", ["285", "286"], ["287", "288"], "'error' is defined but never used.", ["289", "290"], {"messageId": "291", "fix": "292", "desc": "293"}, {"messageId": "294", "fix": "295", "desc": "296"}, {"messageId": "291", "fix": "297", "desc": "293"}, {"messageId": "294", "fix": "298", "desc": "296"}, {"messageId": "291", "fix": "299", "desc": "293"}, {"messageId": "294", "fix": "300", "desc": "296"}, {"messageId": "291", "fix": "301", "desc": "293"}, {"messageId": "294", "fix": "302", "desc": "296"}, {"messageId": "291", "fix": "303", "desc": "293"}, {"messageId": "294", "fix": "304", "desc": "296"}, {"messageId": "291", "fix": "305", "desc": "293"}, {"messageId": "294", "fix": "306", "desc": "296"}, {"messageId": "291", "fix": "307", "desc": "293"}, {"messageId": "294", "fix": "308", "desc": "296"}, {"messageId": "309", "data": "310", "fix": "311", "desc": "312"}, {"messageId": "309", "data": "313", "fix": "314", "desc": "315"}, {"messageId": "309", "data": "316", "fix": "317", "desc": "318"}, {"messageId": "309", "data": "319", "fix": "320", "desc": "321"}, {"desc": "322", "fix": "323"}, {"messageId": "291", "fix": "324", "desc": "293"}, {"messageId": "294", "fix": "325", "desc": "296"}, {"messageId": "291", "fix": "326", "desc": "293"}, {"messageId": "294", "fix": "327", "desc": "296"}, {"desc": "328", "fix": "329"}, {"desc": "330", "fix": "331"}, {"messageId": "291", "fix": "332", "desc": "293"}, {"messageId": "294", "fix": "333", "desc": "296"}, {"messageId": "291", "fix": "334", "desc": "293"}, {"messageId": "294", "fix": "335", "desc": "296"}, {"messageId": "291", "fix": "336", "desc": "293"}, {"messageId": "294", "fix": "337", "desc": "296"}, "suggestUnknown", {"range": "338", "text": "339"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "340", "text": "341"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "342", "text": "339"}, {"range": "343", "text": "341"}, {"range": "344", "text": "339"}, {"range": "345", "text": "341"}, {"range": "346", "text": "339"}, {"range": "347", "text": "341"}, {"range": "348", "text": "339"}, {"range": "349", "text": "341"}, {"range": "350", "text": "339"}, {"range": "351", "text": "341"}, {"range": "352", "text": "339"}, {"range": "353", "text": "341"}, "replaceWithAlt", {"alt": "354"}, {"range": "355", "text": "356"}, "Replace with `&apos;`.", {"alt": "357"}, {"range": "358", "text": "359"}, "Replace with `&lsquo;`.", {"alt": "360"}, {"range": "361", "text": "362"}, "Replace with `&#39;`.", {"alt": "363"}, {"range": "364", "text": "365"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [group, group.id, loadMessages]", {"range": "366", "text": "367"}, {"range": "368", "text": "339"}, {"range": "369", "text": "341"}, {"range": "370", "text": "339"}, {"range": "371", "text": "341"}, "Update the dependencies array to be: [initialNote.id, loadNote]", {"range": "372", "text": "373"}, "Update the dependencies array to be: [group, group.id, loadNotes]", {"range": "374", "text": "375"}, {"range": "376", "text": "339"}, {"range": "377", "text": "341"}, {"range": "378", "text": "339"}, {"range": "379", "text": "341"}, {"range": "380", "text": "339"}, {"range": "381", "text": "341"}, [1787, 1790], "unknown", [1787, 1790], "never", [2493, 2496], [2493, 2496], [3272, 3275], [3272, 3275], [3685, 3688], [3685, 3688], [4176, 4179], [4176, 4179], [520, 523], [520, 523], [1729, 1732], [1729, 1732], "&apos;", [3005, 3042], "\n              Don&apos;t have an account?", "&lsquo;", [3005, 3042], "\n              Don&lsquo;t have an account?", "&#39;", [3005, 3042], "\n              Don&#39;t have an account?", "&rsquo;", [3005, 3042], "\n              Don&rsquo;t have an account?", [763, 773], "[group, group.id, loadMessages]", [2206, 2209], [2206, 2209], [2228, 2231], [2228, 2231], [726, 742], "[initialNote.id, loadNote]", [660, 670], "[group, group.id, loadNotes]", [427, 430], [427, 430], [549, 552], [549, 552], [1002, 1005], [1002, 1005]]