(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,30257,e=>{"use strict";e.s(["default",()=>v],30257);var t=e.i(43476),s=e.i(71645),r=e.i(18566),a=e.i(57951);let l=(0,s.createContext)(void 0);function o(e){let{children:r}=e,[a,o]=(0,s.useState)([]),[n,i]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),x=async()=>{try{let e=await fetch("/api/groups");if(e.ok){let t=await e.json();o(t.groups)}else console.error("Failed to fetch groups")}catch(e){console.error("Failed to refresh groups:",e)}},u=async e=>{let t=await fetch("/api/groups",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).error||"Failed to create group");let s=await t.json();return await x(),s.group},m=async(e,t)=>{let s=await fetch("/api/groups/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).error||"Failed to update group");let r=await s.json();return await x(),(null==n?void 0:n.id)===e&&i(r.group),r.group},h=async e=>{let t=await fetch("/api/groups/".concat(e),{method:"DELETE"});if(!t.ok)throw Error((await t.json()).error||"Failed to delete group");(null==n?void 0:n.id)===e&&i(null),await x()},b=async function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"MEMBER",r=await fetch("/api/groups/".concat(e,"/members"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,role:s})});if(!r.ok)throw Error((await r.json()).error||"Failed to add member");if(await x(),(null==n?void 0:n.id)===e){let t=await fetch("/api/groups/".concat(e));t.ok&&i((await t.json()).group)}};return(0,s.useEffect)(()=>{x().finally(()=>d(!1))},[]),(0,t.jsx)(l.Provider,{value:{groups:a,selectedGroup:n,loading:c,createGroup:u,updateGroup:m,deleteGroup:h,selectGroup:e=>{i(e)},refreshGroups:x,addMember:b},children:r})}function n(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useGroups must be used within a GroupProvider");return e}function i(e){let{isOpen:r,onClose:a}=e,[l,o]=(0,s.useState)(""),[i,c]=(0,s.useState)(""),[d,x]=(0,s.useState)(!1),[u,m]=(0,s.useState)(!1),[h,b]=(0,s.useState)(""),{createGroup:p}=n(),g=async e=>{e.preventDefault(),b(""),m(!0);try{await p({name:l,description:i||void 0,isPrivate:d}),o(""),c(""),x(!1),a()}catch(e){b(e instanceof Error?e.message:"Failed to create group")}finally{m(!1)}},y=()=>{u||(o(""),c(""),x(!1),b(""),a())};return r?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Create New Group"}),(0,t.jsx)("button",{onClick:y,disabled:u,className:"text-gray-400 hover:text-gray-600 disabled:opacity-50",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),h&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:h}),(0,t.jsxs)("form",{onSubmit:g,children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Group Name *"}),(0,t.jsx)("input",{id:"name",type:"text",value:l,onChange:e=>o(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter group name",required:!0,maxLength:100})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),(0,t.jsx)("textarea",{id:"description",value:i,onChange:e=>c(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter group description (optional)",rows:3,maxLength:500})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",checked:d,onChange:e=>x(e.target.checked),className:"mr-2"}),(0,t.jsx)("span",{className:"text-gray-700 text-sm",children:"Private Group"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Private groups require invitation to join"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{type:"button",onClick:y,disabled:u,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:u||!l.trim(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Creating...":"Create Group"})]})]})]})}):null}function c(){let{groups:e,selectedGroup:r,selectGroup:l,loading:o}=n(),{user:c}=(0,a.useAuth)(),[d,x]=(0,s.useState)(!1);return o?(0,t.jsx)("div",{className:"w-80 bg-white border-r border-gray-200 p-4",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded"},s))})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"w-80 bg-white border-r border-gray-200 flex flex-col",children:[(0,t.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Groups"}),(0,t.jsx)("button",{onClick:()=>x(!0),className:"bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full",title:"Create new group",children:(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto",children:0===e.length?(0,t.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,t.jsx)("p",{className:"mb-2",children:"No groups yet"}),(0,t.jsx)("button",{onClick:()=>x(!0),className:"text-blue-500 hover:text-blue-600 text-sm",children:"Create your first group"})]}):(0,t.jsx)("div",{className:"p-2",children:e.map(e=>(0,t.jsx)("div",{onClick:()=>l(e),className:"p-3 rounded-lg cursor-pointer mb-2 transition-colors ".concat((null==r?void 0:r.id)===e.id?"bg-blue-50 border border-blue-200":"hover:bg-gray-50"),children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),e.isPrivate&&(0,t.jsx)("svg",{className:"w-3 h-3 text-gray-400 ml-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"})})]}),e.description&&(0,t.jsx)("p",{className:"text-xs text-gray-500 truncate mt-1",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center mt-2 text-xs text-gray-400",children:[(0,t.jsxs)("span",{children:[e._count.members," members"]}),(0,t.jsx)("span",{className:"mx-1",children:"•"}),(0,t.jsxs)("span",{children:[e._count.messages," messages"]}),(0,t.jsx)("span",{className:"mx-1",children:"•"}),(0,t.jsxs)("span",{children:[e._count.notes," notes"]})]})]}),e.ownerId===(null==c?void 0:c.id)&&(0,t.jsx)("div",{className:"ml-2",children:(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Owner"})})]})},e.id))})})]}),(0,t.jsx)(i,{isOpen:d,onClose:()=>x(!1)})]})}let d=(0,s.createContext)(void 0);function x(e){let{children:r}=e,[a,l]=(0,s.useState)([]),[o,n]=(0,s.useState)(!1),[i,c]=(0,s.useState)(!1),[x,u]=(0,s.useState)(!1),[m,h]=(0,s.useState)(1),[b,p]=(0,s.useState)(null),g=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;e!==b&&(l([]),h(1),p(e),t=1),n(!0);try{let s=await fetch("/api/groups/".concat(e,"/messages?page=").concat(t,"&limit=50"));if(s.ok){let e=await s.json();1===t?l(e.messages):l(t=>[...e.messages,...t]),u(e.pagination.hasMore),h(t)}else console.error("Failed to load messages")}catch(e){console.error("Failed to load messages:",e)}finally{n(!1)}},y=async(e,t)=>{c(!0);try{let s=await fetch("/api/groups/".concat(e,"/messages"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:t})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to send message")}let r=await s.json();l(e=>[...e,r.data])}catch(e){throw console.error("Failed to send message:",e),e}finally{c(!1)}};return(0,t.jsx)(d.Provider,{value:{messages:a,loading:o,sending:i,sendMessage:y,loadMessages:g,clearMessages:()=>{l([]),h(1),u(!1),p(null)},hasMore:x,currentPage:m},children:r})}let u=(0,s.createContext)(void 0);function m(e){let{children:r}=e,[a,l]=(0,s.useState)([]),[o,n]=(0,s.useState)(null),[i,c]=(0,s.useState)(!1),[d,x]=(0,s.useState)(!1),m=async e=>{c(!0);try{let t=await fetch("/api/groups/".concat(e,"/notes"));if(t.ok){let e=await t.json();l(e.notes)}else console.error("Failed to load notes")}catch(e){console.error("Failed to load notes:",e)}finally{c(!1)}},h=async e=>{x(!0);try{let t=await fetch("/api/notes/".concat(e));if(t.ok){let e=await t.json();n(e.note)}else console.error("Failed to load note")}catch(e){console.error("Failed to load note:",e)}finally{x(!1)}},b=async(e,t)=>{let s=await fetch("/api/groups/".concat(e,"/notes"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).error||"Failed to create note");let r=await s.json();return await m(e),r.note},p=async(e,t)=>{let s=await fetch("/api/notes/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).error||"Failed to update note");let r=await s.json();return(null==o?void 0:o.id)===e&&n(r.note),l(s=>s.map(s=>s.id===e?{...s,...t}:s)),r.note},g=async e=>{let t=await fetch("/api/notes/".concat(e),{method:"DELETE"});if(!t.ok)throw Error((await t.json()).error||"Failed to delete note");(null==o?void 0:o.id)===e&&n(null),l(t=>t.filter(t=>t.id!==e))},y=async(e,t,s,r)=>{let a=await fetch("/api/notes/".concat(e,"/blocks"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:t,content:s,order:r})});if(!a.ok)throw Error((await a.json()).error||"Failed to create block");let l=await a.json();return(null==o?void 0:o.id)===e&&await h(e),l.block},j=async(e,t)=>{let s=await fetch("/api/blocks/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).error||"Failed to update block");let r=await s.json();return o&&n(s=>s?{...s,blocks:s.blocks.map(s=>s.id===e?{...s,...t}:s)}:null),r.block},f=async e=>{let t=await fetch("/api/blocks/".concat(e),{method:"DELETE"});if(!t.ok)throw Error((await t.json()).error||"Failed to delete block");o&&n(t=>t?{...t,blocks:t.blocks.filter(t=>t.id!==e)}:null)},v=async(e,t)=>{let s=await fetch("/api/notes/".concat(e,"/blocks"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({blocks:t})});if(!s.ok)throw Error((await s.json()).error||"Failed to reorder blocks");(null==o?void 0:o.id)===e&&await h(e)};return(0,t.jsx)(u.Provider,{value:{notes:a,selectedNote:o,loading:i,loadingNote:d,createNote:b,updateNote:p,deleteNote:g,selectNote:e=>{n(e),(null==e?void 0:e.id)&&h(e.id)},loadNotes:m,loadNote:h,clearNotes:()=>{l([]),n(null)},createBlock:y,updateBlock:j,deleteBlock:f,reorderBlocks:v},children:r})}function h(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useNotes must be used within a NotesProvider");return e}function b(e){let{group:r}=e,{messages:l,loading:o,sending:n,sendMessage:i,loadMessages:c,hasMore:x,currentPage:u}=function(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useMessages must be used within a MessageProvider");return e}(),{user:m}=(0,a.useAuth)(),[h,b]=(0,s.useState)(""),[p,g]=(0,s.useState)(""),y=(0,s.useRef)(null),j=(0,s.useRef)(null);(0,s.useEffect)(()=>{r&&c(r.id)},[r.id]),(0,s.useEffect)(()=>{f()},[l]);let f=()=>{var e;null==(e=y.current)||e.scrollIntoView({behavior:"smooth"})},v=async e=>{if(e.preventDefault(),!h.trim()||n)return;g("");let t=h.trim();b("");try{await i(r.id,t)}catch(e){g(e instanceof Error?e.message:"Failed to send message"),b(t)}},N=async()=>{x&&!o&&await c(r.id,u+1)};return(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{ref:j,className:"flex-1 overflow-y-auto p-4 space-y-4",children:[x&&(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("button",{onClick:N,disabled:o,className:"text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50",children:o?"Loading...":"Load older messages"})}),l.map((e,s)=>{var r,a;let o=(r=e,!(a=s>0?l[s-1]:null)||new Date(r.createdAt).toDateString()!==new Date(a.createdAt).toDateString()),n=e.authorId===(null==m?void 0:m.id);return(0,t.jsxs)("div",{children:[o&&(0,t.jsx)("div",{className:"flex items-center justify-center my-4",children:(0,t.jsx)("div",{className:"bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full",children:(e=>{let t=new Date(e),s=new Date,r=new Date(s);return(r.setDate(r.getDate()-1),t.toDateString()===s.toDateString())?"Today":t.toDateString()===r.toDateString()?"Yesterday":t.toLocaleDateString()})(e.createdAt)})}),(0,t.jsx)("div",{className:"flex ".concat(n?"justify-end":"justify-start"),children:(0,t.jsxs)("div",{className:"max-w-xs lg:max-w-md ".concat(n?"order-2":"order-1"),children:[!n&&(0,t.jsx)("div",{className:"text-xs text-gray-700 mb-1 font-medium",children:e.author.name||e.author.username}),(0,t.jsxs)("div",{className:"px-4 py-2 rounded-lg ".concat(n?"bg-blue-500 text-white":"bg-gray-100 text-gray-900 border border-gray-200"),children:[(0,t.jsx)("div",{className:"text-sm whitespace-pre-wrap",children:e.content.includes("```")?(0,t.jsx)("div",{children:e.content.split("```").map((e,s)=>s%2==0?(0,t.jsx)("span",{children:e},s):(0,t.jsx)("code",{className:"block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto",children:e},s))}):e.content}),(0,t.jsx)("div",{className:"text-xs mt-1 ".concat(n?"text-blue-100":"text-gray-600"),children:new Date(e.createdAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]})})]},e.id)}),o&&0===l.length&&(0,t.jsxs)("div",{className:"text-center text-gray-500",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-2",children:"Loading messages..."})]}),!o&&0===l.length&&(0,t.jsx)("div",{className:"text-center text-gray-500 py-8",children:(0,t.jsx)("p",{children:"No messages yet. Start the conversation!"})}),(0,t.jsx)("div",{ref:y})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[p&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm",children:p}),(0,t.jsxs)("form",{onSubmit:v,className:"flex space-x-2",children:[(0,t.jsx)("input",{type:"text",value:h,onChange:e=>b(e.target.value),placeholder:"Type a message...",className:"flex-1 border border-gray-300 rounded-lg px-4 py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500",disabled:n,maxLength:2e3}),(0,t.jsx)("button",{type:"submit",disabled:!h.trim()||n,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:n?"Sending...":"Send"})]})]})]})}function p(e){let{isOpen:r,onClose:a,groupId:l}=e,[o,n]=(0,s.useState)(""),[i,c]=(0,s.useState)(""),[d,x]=(0,s.useState)(!1),[u,m]=(0,s.useState)(""),{createNote:b}=h(),p=async e=>{e.preventDefault(),m(""),x(!0);try{await b(l,{title:o,description:i||void 0}),n(""),c(""),a()}catch(e){m(e instanceof Error?e.message:"Failed to create note")}finally{x(!1)}},g=()=>{d||(n(""),c(""),m(""),a())};return r?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Create New Note"}),(0,t.jsx)("button",{onClick:g,disabled:d,className:"text-gray-400 hover:text-gray-600 disabled:opacity-50",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),u&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:u}),(0,t.jsxs)("form",{onSubmit:p,children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"title",children:"Note Title *"}),(0,t.jsx)("input",{id:"title",type:"text",value:o,onChange:e=>n(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter note title",required:!0,maxLength:200})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),(0,t.jsx)("textarea",{id:"description",value:i,onChange:e=>c(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter note description (optional)",rows:3,maxLength:500})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{type:"button",onClick:g,disabled:d,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:d||!o.trim(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Creating...":"Create Note"})]})]})]})}):null}function g(e){let{block:r,noteId:a,isLast:l,onAddBlock:o}=e,{updateBlock:n,deleteBlock:i}=h(),[c,d]=(0,s.useState)(r.content),[x,u]=(0,s.useState)(!1),[m,b]=(0,s.useState)(!1),p=(0,s.useRef)(null);(0,s.useEffect)(()=>{d(r.content)},[r.content]),(0,s.useEffect)(()=>{x&&p.current&&(p.current.focus(),p.current.style.height="auto",p.current.style.height=p.current.scrollHeight+"px")},[x]);let g=async()=>{try{await n(r.id,{content:c}),u(!1)}catch(e){console.error("Failed to update block:",e),alert("Failed to update block")}},y=e=>{"Enter"!==e.key||e.shiftKey?"Escape"===e.key&&(d(r.content),u(!1)):(e.preventDefault(),l&&c.trim()?o("TEXT"):g())},j=async()=>{if(confirm("Are you sure you want to delete this block?"))try{await i(r.id)}catch(e){console.error("Failed to delete block:",e),alert("Failed to delete block")}},f=async e=>{try{await n(r.id,{type:e}),b(!1)}catch(e){console.error("Failed to update block type:",e),alert("Failed to update block type")}},v=e=>{switch(e){case"HEADING":return"text-xl font-bold text-gray-900";case"CODE":return"code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto";case"QUOTE":return"border-l-4 border-blue-400 pl-4 italic text-gray-700 bg-blue-50 py-2 rounded-r";default:return"text-gray-900"}};return(0,t.jsxs)("div",{className:"group relative flex items-start space-x-2 py-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:()=>b(!m),className:"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity",title:"Change block type",children:(e=>{switch(e){case"HEADING":return"📰";case"BULLET_LIST":return"•";case"NUMBERED_LIST":return"1.";case"CODE":return"💻";case"QUOTE":return"💬";default:return"📝"}})(r.type)}),m&&(0,t.jsx)("div",{className:"absolute left-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10",children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsx)("button",{onClick:()=>f("TEXT"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📝 Text"}),(0,t.jsx)("button",{onClick:()=>f("HEADING"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📰 Heading"}),(0,t.jsx)("button",{onClick:()=>f("BULLET_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"• Bullet List"}),(0,t.jsx)("button",{onClick:()=>f("NUMBERED_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"1. Numbered List"}),(0,t.jsx)("button",{onClick:()=>f("CODE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💻 Code"}),(0,t.jsx)("button",{onClick:()=>f("QUOTE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💬 Quote"})]})})]}),(0,t.jsx)("div",{className:"flex-1 min-w-0",children:(()=>{if(x){let e="CODE"===r.type?"w-full border-none outline-none resize-none code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto":"w-full border-none outline-none resize-none ".concat(v(r.type)," bg-transparent");return(0,t.jsx)("textarea",{ref:p,value:c,onChange:e=>d(e.target.value),onKeyDown:y,onBlur:g,className:e,placeholder:"CODE"===r.type?"Enter your code here...":"Type something...",rows:"CODE"===r.type?3:1})}if(!c.trim())return(0,t.jsx)("div",{onClick:()=>u(!0),className:"text-gray-400 cursor-text py-2",children:"Type something..."});let e="BULLET_LIST"===r.type?c.split("\n").map(e=>e.trim()?"• ".concat(e):e).join("\n"):"NUMBERED_LIST"===r.type?c.split("\n").map((e,t)=>e.trim()?"".concat(t+1,". ").concat(e):e).join("\n"):c;return(0,t.jsx)("div",{onClick:()=>u(!0),className:"cursor-text py-2 whitespace-pre-wrap ".concat(v(r.type)),children:e})})()}),(0,t.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsx)("button",{onClick:j,className:"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500",title:"Delete block",children:(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})})]})}function y(e){let{note:r}=e,{selectedNote:a,updateNote:l,createBlock:o,loadNote:n,loadingNote:i}=h(),[c,d]=(0,s.useState)(!1),[x,u]=(0,s.useState)(r.title),[m,b]=(0,s.useState)(r.description||""),p=a||r;(0,s.useEffect)(()=>{r.id&&n(r.id)},[r.id]);let y=async()=>{try{await l(p.id,{title:x,description:m||void 0}),d(!1)}catch(e){console.error("Failed to update note:",e),alert("Failed to update note")}},j=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"TEXT";try{let t=p.blocks.length;await o(p.id,e,"",t)}catch(e){console.error("Failed to create block:",e),alert("Failed to create block")}};return(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsxs)("div",{className:"bg-white border-b border-gray-200 p-6",children:[c?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("input",{type:"text",value:x,onChange:e=>u(e.target.value),className:"text-2xl font-bold text-gray-900 w-full border-none outline-none bg-transparent",placeholder:"Note title",autoFocus:!0}),(0,t.jsx)("textarea",{value:m,onChange:e=>b(e.target.value),className:"text-gray-600 w-full border-none outline-none bg-transparent resize-none",placeholder:"Add a description...",rows:2}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:y,className:"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm",children:"Save"}),(0,t.jsx)("button",{onClick:()=>{u(p.title),b(p.description||""),d(!1)},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm",children:"Cancel"})]})]}):(0,t.jsxs)("div",{onClick:()=>d(!0),className:"cursor-pointer",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2 hover:bg-gray-50 p-2 rounded",children:p.title}),p.description&&(0,t.jsx)("p",{className:"text-gray-600 hover:bg-gray-50 p-2 rounded",children:p.description})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4 text-sm text-gray-500",children:[(0,t.jsxs)("div",{children:["Created by ",(0,t.jsx)("span",{className:"font-medium",children:p.author.name||p.author.username})]}),(0,t.jsxs)("div",{children:["Last updated ",new Date(p.updatedAt).toLocaleDateString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(i||!p.blocks)&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,t.jsx)("span",{className:"ml-2 text-gray-500",children:"Loading note content..."})]}),p.blocks&&(0,t.jsx)("div",{className:"space-y-2",children:p.blocks.map((e,s)=>(0,t.jsx)(g,{block:e,noteId:p.id,isLast:s===p.blocks.length-1,onAddBlock:j},e.id))}),p.blocks&&(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("button",{onClick:()=>j("TEXT"),className:"flex items-center space-x-2 text-gray-500 hover:text-gray-700 p-2 rounded hover:bg-gray-100",children:[(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,t.jsx)("span",{className:"text-sm",children:"Add block"})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{className:"text-gray-400 hover:text-gray-600 p-1",children:(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})}),(0,t.jsx)("div",{className:"absolute left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10",children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsx)("button",{onClick:()=>j("TEXT"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📝 Text"}),(0,t.jsx)("button",{onClick:()=>j("HEADING"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"📰 Heading"}),(0,t.jsx)("button",{onClick:()=>j("BULLET_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"• Bullet List"}),(0,t.jsx)("button",{onClick:()=>j("NUMBERED_LIST"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"1. Numbered List"}),(0,t.jsx)("button",{onClick:()=>j("CODE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💻 Code"}),(0,t.jsx)("button",{onClick:()=>j("QUOTE"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"💬 Quote"})]})})]})]})})]})})]})}function j(e){let{group:r}=e,{notes:a,selectedNote:l,loading:o,loadNotes:n,selectNote:i,deleteNote:c}=h(),[d,x]=(0,s.useState)(!1),[u,m]=(0,s.useState)(!0);(0,s.useEffect)(()=>{r&&n(r.id)},[r.id]);let b=async e=>{if(confirm("Are you sure you want to delete this note?"))try{await c(e)}catch(e){console.error("Failed to delete note:",e),alert("Failed to delete note")}};return l&&!u?(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200 p-4",children:(0,t.jsx)("button",{onClick:()=>m(!0),className:"text-blue-500 hover:text-blue-600 text-sm mb-2",children:"← Back to Notes"})}),(0,t.jsx)(y,{note:l})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200 p-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Notes"}),(0,t.jsx)("button",{onClick:()=>x(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm",children:"New Note"})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:o?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading notes..."})]}):0===a.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("svg",{className:"w-16 h-16 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Notes Yet"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your first note to start collaborating"}),(0,t.jsx)("button",{onClick:()=>x(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm",children:"Create Note"})]}):(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:a.map(e=>(0,t.jsx)("div",{className:"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200",onClick:()=>{i(e),m(!1)},children:(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 truncate",children:e.title}),(0,t.jsx)("button",{onClick:t=>{t.stopPropagation(),b(e.id)},className:"text-gray-400 hover:text-red-500 p-1",title:"Delete note",children:(0,t.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}),e.description&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"font-medium",children:e.author.name||e.author.username}),(0,t.jsx)("span",{className:"mx-1",children:"•"}),(0,t.jsxs)("span",{children:[e._count.blocks," blocks"]})]}),(0,t.jsx)("span",{children:new Date(e.updatedAt).toLocaleDateString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]})},e.id))})})]}),(0,t.jsx)(p,{isOpen:d,onClose:()=>x(!1),groupId:r.id})]})}function f(){let{selectedGroup:e,addMember:r}=n(),{user:l}=(0,a.useAuth)(),[o,i]=(0,s.useState)(!1),[c,d]=(0,s.useState)(""),[u,h]=(0,s.useState)("MEMBER"),[p,g]=(0,s.useState)(!1),[y,f]=(0,s.useState)(""),[v,N]=(0,s.useState)("chat");if(!e)return(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("svg",{className:"w-16 h-16 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Group"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Choose a group from the sidebar to view details and start chatting"})]})});let w=e.ownerId===(null==l?void 0:l.id)||e.members.some(e=>e.userId===(null==l?void 0:l.id)&&["OWNER","ADMIN"].includes(e.role)),k=async t=>{t.preventDefault(),f(""),g(!0);try{await r(e.id,c,u),d(""),h("MEMBER"),i(!1)}catch(e){f(e instanceof Error?e.message:"Failed to add member")}finally{g(!1)}};return(0,t.jsx)(x,{children:(0,t.jsx)(m,{children:(0,t.jsxs)("div",{className:"flex-1 flex flex-col bg-gray-50",children:[(0,t.jsxs)("div",{className:"bg-white border-b border-gray-200 p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:e.name}),e.isPrivate&&(0,t.jsx)("svg",{className:"w-4 h-4 text-gray-400 ml-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"})})]}),e.description&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]}),w&&(0,t.jsx)("button",{onClick:()=>i(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm",children:"Add Member"})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("nav",{className:"flex space-x-8",children:[(0,t.jsx)("button",{onClick:()=>N("chat"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("chat"===v?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Chat"}),(0,t.jsxs)("button",{onClick:()=>N("members"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("members"===v?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["Members (",e._count.members,")"]}),(0,t.jsxs)("button",{onClick:()=>N("notes"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("notes"===v?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["Notes (",e._count.notes,")"]})]})})]}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:["chat"===v&&(0,t.jsx)(b,{group:e}),"members"===v&&(0,t.jsx)("div",{className:"flex-1 p-6 overflow-y-auto",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-8",children:[(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e._count.members}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Members"})]}),(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e._count.messages}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Messages"})]}),(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e._count.notes}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Notes"})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Members"})}),(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("div",{className:"space-y-3",children:e.members.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:(e.user.name||e.user.username).charAt(0).toUpperCase()})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.user.name||e.user.username}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.user.email})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat("OWNER"===e.role?"bg-green-100 text-green-800":"ADMIN"===e.role?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:e.role})})]},e.id))})})]})]})}),"notes"===v&&(0,t.jsx)(j,{group:e})]}),o&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md mx-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add Member"}),(0,t.jsx)("button",{onClick:()=>i(!1),className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),y&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:y}),(0,t.jsxs)("form",{onSubmit:k,children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Email Address"}),(0,t.jsx)("input",{type:"email",value:c,onChange:e=>d(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter user's email",required:!0})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Role"}),(0,t.jsxs)("select",{value:u,onChange:e=>h(e.target.value),className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",children:[(0,t.jsx)("option",{value:"MEMBER",children:"Member"}),(0,t.jsx)("option",{value:"ADMIN",children:"Admin"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{type:"button",onClick:()=>i(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:p,className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:p?"Adding...":"Add Member"})]})]})]})})]})})})}function v(){let{user:e,loading:l,logout:n}=(0,a.useAuth)(),i=(0,r.useRouter)();if((0,s.useEffect)(()=>{l||e||i.push("/auth")},[e,l,i]),l)return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});if(!e)return null;let d=async()=>{await n(),i.push("/auth")};return(0,t.jsx)(o,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[(0,t.jsx)("header",{className:"bg-white shadow",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"MyBinder"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-700",children:["Welcome, ",(0,t.jsx)("span",{className:"font-medium",children:e.name||e.username})]}),(0,t.jsx)("button",{onClick:d,className:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm",children:"Logout"})]})]})})}),(0,t.jsxs)("div",{className:"flex-1 flex",children:[(0,t.jsx)(c,{}),(0,t.jsx)(f,{})]})]})})}}]);