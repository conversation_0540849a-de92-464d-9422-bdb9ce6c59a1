(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18566,(e,t,a)=>{t.exports=e.r(76562)},16518,e=>{"use strict";e.s(["default",()=>o],16518);var t=e.i(43476),a=e.i(71645),s=e.i(18566),l=e.i(57951);function n(e){let{onSuccess:s,onSwitchToRegister:n}=e,[r,o]=(0,a.useState)(""),[d,i]=(0,a.useState)(""),[c,u]=(0,a.useState)(""),[m,x]=(0,a.useState)(!1),{login:h}=(0,l.useAuth)(),b=async e=>{e.preventDefault(),u(""),x(!0);try{await h(r,d),null==s||s()}catch(e){u(e instanceof Error?e.message:"<PERSON><PERSON> failed")}finally{x(!1)}};return(0,t.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-center text-gray-800 mb-6",children:"Sign In to MyBinder"}),c&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,t.jsxs)("form",{onSubmit:b,children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),(0,t.jsx)("input",{id:"email",type:"email",value:r,onChange:e=>o(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your email",required:!0})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password"}),(0,t.jsx)("input",{id:"password",type:"password",value:d,onChange:e=>i(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your password",required:!0})]}),(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("button",{type:"submit",disabled:m,className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed w-full",children:m?"Signing In...":"Sign In"})})]}),n&&(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)("button",{onClick:n,className:"text-blue-500 hover:text-blue-700 font-medium",children:"Sign up here"})]})}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Demo Accounts:"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"<EMAIL> / demo123"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"<EMAIL> / demo123"})]})]})})}function r(e){let{onSuccess:s,onSwitchToLogin:n}=e,[r,o]=(0,a.useState)(""),[d,i]=(0,a.useState)(""),[c,u]=(0,a.useState)(""),[m,x]=(0,a.useState)(""),[h,b]=(0,a.useState)(""),[p,g]=(0,a.useState)(!1),{register:f}=(0,l.useAuth)(),y=async e=>{e.preventDefault(),b(""),g(!0);try{await f(r,d,c,m||void 0),null==s||s()}catch(e){b(e instanceof Error?e.message:"Registration failed")}finally{g(!1)}};return(0,t.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-center text-gray-800 mb-6",children:"Join MyBinder"}),h&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:h}),(0,t.jsxs)("form",{onSubmit:y,children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email *"}),(0,t.jsx)("input",{id:"email",type:"email",value:r,onChange:e=>o(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your email",required:!0})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"username",children:"Username *"}),(0,t.jsx)("input",{id:"username",type:"text",value:d,onChange:e=>i(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Choose a username",required:!0})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Full Name"}),(0,t.jsx)("input",{id:"name",type:"text",value:m,onChange:e=>x(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Enter your full name (optional)"})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password *"}),(0,t.jsx)("input",{id:"password",type:"password",value:c,onChange:e=>u(e.target.value),className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Create a password (min. 6 characters)",required:!0,minLength:6})]}),(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("button",{type:"submit",disabled:p,className:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed w-full",children:p?"Creating Account...":"Create Account"})})]}),n&&(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",(0,t.jsx)("button",{onClick:n,className:"text-blue-500 hover:text-blue-700 font-medium",children:"Sign in here"})]})})]})})}function o(){let[e,o]=(0,a.useState)(!0),{user:d,loading:i}=(0,l.useAuth)(),c=(0,s.useRouter)();if((0,a.useEffect)(()=>{!i&&d&&c.push("/dashboard")},[d,i,c]),i)return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});if(d)return null;let u=()=>{c.push("/dashboard")};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"MyBinder"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Group Chat with Integrated Note-Taking"})]})}),(0,t.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:e?(0,t.jsx)(n,{onSuccess:u,onSwitchToRegister:()=>o(!1)}):(0,t.jsx)(r,{onSuccess:u,onSwitchToLogin:()=>o(!0)})})]})}}]);