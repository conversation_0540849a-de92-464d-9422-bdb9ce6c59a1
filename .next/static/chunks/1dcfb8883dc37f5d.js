(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,57951,t=>{"use strict";t.s(["AuthProvider",()=>a,"useAuth",()=>i]);var e=t.i(43476),r=t.i(71645);let o=(0,r.createContext)(void 0);function a(t){let{children:a}=t,[i,n]=(0,r.useState)(null),[s,l]=(0,r.useState)(!0),u=async()=>{try{let t=await fetch("/api/auth/me");if(t.ok){let e=await t.json();n(e.user)}else n(null)}catch(t){console.error("Failed to refresh user:",t),n(null)}},h=async(t,e)=>{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:e})});if(!r.ok)throw Error((await r.json()).error||"Login failed");n((await r.json()).user)},c=async(t,e,r,o)=>{let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,username:e,password:r,name:o})});if(!a.ok)throw Error((await a.json()).error||"Registration failed");await h(t,r)},d=async()=>{try{await fetch("/api/auth/logout",{method:"POST"})}catch(t){console.error("Logout error:",t)}finally{n(null)}};return(0,r.useEffect)(()=>{u().finally(()=>l(!1))},[]),(0,e.jsx)(o.Provider,{value:{user:i,loading:s,login:h,register:c,logout:d,refreshUser:u},children:a})}function i(){let t=(0,r.useContext)(o);if(void 0===t)throw Error("useAuth must be used within an AuthProvider");return t}}]);