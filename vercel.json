{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"DATABASE_URL": "@database_url", "JWT_SECRET": "@jwt_secret", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url"}, "build": {"env": {"DATABASE_URL": "@database_url", "JWT_SECRET": "@jwt_secret", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url"}}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "regions": ["sin1"], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}