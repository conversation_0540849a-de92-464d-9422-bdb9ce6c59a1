# Data Structures & Flow Documentation

## 🎯 **Why-How-What Framework**

### **Why**: Alasan <PERSON>han Struktur Data

#### **Business Requirements**
- **Relational Data**: Users, Groups, Messages, Notes memiliki complex relationships
- **Type Safety**: Prevent runtime errors dengan strong typing
- **Data Integrity**: Ensure consistency across all operations
- **Performance**: Efficient data access dan manipulation
- **Scalability**: Support growing data volume dan user base

#### **Technical Justifications**
1. **PostgreSQL**: ACID compliance untuk data consistency
2. **Prisma ORM**: Type-safe database access dengan auto-generated types
3. **TypeScript Interfaces**: Compile-time type checking
4. **Normalized Schema**: Reduce data redundancy dan improve integrity
5. **Indexed Relationships**: Fast query performance

### **How**: Implementasi Detail Struktur Data

#### **1. Database Schema & Relationships**

**Prisma Schema Design**:
```prisma
model User {
  id          String   @id @default(cuid())
  username    String   @unique
  email       String   @unique
  password    String
  name        String?
  avatar      String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  ownedGroups   Group[]        @relation("GroupOwner")
  memberships   GroupMember[]
  messages      Message[]
  notes         Note[]
  noteBlocks    NoteBlock[]

  @@map("users")
}

model Group {
  id          String   @id @default(cuid())
  name        String
  description String?
  avatar      String?
  isPrivate   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign Keys
  ownerId     String

  // Relationships
  owner       User          @relation("GroupOwner", fields: [ownerId], references: [id])
  members     GroupMember[]
  messages    Message[]
  notes       Note[]

  @@map("groups")
}

model Message {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  authorId  String
  groupId   String

  // Relationships
  author    User  @relation(fields: [authorId], references: [id])
  group     Group @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model Note {
  id          String   @id @default(cuid())
  title       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign Keys
  authorId    String
  groupId     String

  // Relationships
  author      User        @relation(fields: [authorId], references: [id])
  group       Group       @relation(fields: [groupId], references: [id], onDelete: Cascade)
  blocks      NoteBlock[]

  @@map("notes")
}

model NoteBlock {
  id        String    @id @default(cuid())
  type      BlockType @default(TEXT)
  content   String
  order     Int
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Foreign Keys
  authorId  String
  noteId    String

  // Relationships
  author    User @relation(fields: [authorId], references: [id])
  note      Note @relation(fields: [noteId], references: [id], onDelete: Cascade)

  @@map("note_blocks")
}

enum BlockType {
  TEXT
  HEADING_1
  HEADING_2
  HEADING_3
  HEADING
  BULLET_LIST
  NUMBERED_LIST
  CODE
  QUOTE
}

enum Role {
  OWNER
  ADMIN
  MEMBER
}
```

**Why this schema design?**
- **Normalized Structure**: Eliminates data duplication
- **Cascade Deletes**: Automatic cleanup saat parent entities dihapus
- **Proper Indexing**: Foreign keys automatically indexed
- **Enum Types**: Type-safe constants untuk predefined values
- **Timestamps**: Audit trail untuk semua entities

#### **2. TypeScript Interface Definitions**

**Core Type Definitions**:
```typescript
// Base Prisma types (auto-generated)
export type { User, Group, Message, Note, NoteBlock, GroupMember, Role, BlockType } from '@prisma/client'

// Extended interfaces untuk UI components
export interface UserWithGroups extends User {
  ownedGroups: Group[]
  memberships: (GroupMember & { group: Group })[]
}

export interface GroupWithMembers extends Group {
  owner: User
  members: (GroupMember & { user: User })[]
  _count: {
    members: number
    messages: number
    notes: number
  }
}

export interface MessageWithAuthor extends Message {
  author: User
}

export interface NoteWithBlocks extends Note {
  author: User
  blocks: NoteBlock[]
}

export interface NoteBlockWithAuthor extends NoteBlock {
  author: User
}

// API Request/Response types
export interface CreateUserData {
  email: string
  password: string
  username: string
  name?: string
}

export interface CreateGroupData {
  name: string
  description?: string
  isPrivate?: boolean
}

export interface CreateMessageData {
  content: string
}

export interface CreateNoteData {
  title: string
  description?: string
  groupId: string
}

export interface CreateNoteBlockData {
  type: BlockType
  content: string
  noteId: string
  order: number
}
```

**Why these interfaces?**
- **Type Safety**: Compile-time error detection
- **IntelliSense**: Better developer experience
- **API Contracts**: Clear contracts antara frontend dan backend
- **Extensibility**: Easy to extend dengan additional properties

#### **3. Component Data Flow**

**React Context Architecture**:
```typescript
// Global State Structure
interface AppState {
  user: UserWithGroups | null
  selectedGroup: GroupWithMembers | null
  messages: MessageWithAuthor[]
  notes: NoteWithBlocks[]
  selectedNote: NoteWithBlocks | null
  loading: {
    user: boolean
    groups: boolean
    messages: boolean
    notes: boolean
    noteDetail: boolean
  }
  errors: {
    [key: string]: string | null
  }
}

// Context Providers Hierarchy
<AuthProvider>
  <GroupProvider>
    <MessagesProvider>
      <NotesProvider>
        <App />
      </NotesProvider>
    </MessagesProvider>
  </GroupProvider>
</AuthProvider>
```

**Data Flow Pattern**:
```
User Action → Context Action → API Call → Database → Response → Context Update → UI Re-render
```

**Example Flow - Send Message**:
```typescript
// 1. User types message dan clicks send
const handleSendMessage = async (content: string) => {
  // 2. Optimistic update
  const tempMessage = createOptimisticMessage(content)
  addMessageToContext(tempMessage)
  
  try {
    // 3. API call
    const response = await fetch(`/api/groups/${groupId}/messages`, {
      method: 'POST',
      body: JSON.stringify({ content })
    })
    
    // 4. Server response
    const { message } = await response.json()
    
    // 5. Replace optimistic dengan real data
    replaceMessageInContext(tempMessage.id, message)
    
  } catch (error) {
    // 6. Rollback on error
    removeMessageFromContext(tempMessage.id)
    showError('Failed to send message')
  }
}
```

#### **4. API Request/Response Structures**

**Standardized API Response Format**:
```typescript
// Success Response
interface ApiSuccessResponse<T> {
  success: true
  data: T
  message?: string
  meta?: {
    pagination?: {
      page: number
      limit: number
      total: number
      hasMore: boolean
    }
    timestamp: string
  }
}

// Error Response
interface ApiErrorResponse {
  success: false
  error: string
  details?: any
  code?: string
  timestamp: string
}

// Example API Responses
// GET /api/groups/[groupId]/messages
{
  "success": true,
  "data": {
    "messages": MessageWithAuthor[],
    "hasMore": boolean
  },
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "hasMore": true
    },
    "timestamp": "2025-09-22T07:41:00.000Z"
  }
}

// POST /api/groups/[groupId]/notes
{
  "success": true,
  "data": {
    "note": NoteWithBlocks
  },
  "message": "Note created successfully",
  "meta": {
    "timestamp": "2025-09-22T07:41:00.000Z"
  }
}
```

**Why standardized responses?**
- **Consistency**: Same structure across all endpoints
- **Error Handling**: Predictable error format
- **Metadata**: Additional info seperti pagination
- **Debugging**: Timestamps untuk request tracking

#### **5. Error Handling Patterns**

**Hierarchical Error Structure**:
```typescript
// Error Types
type ErrorLevel = 'info' | 'warning' | 'error' | 'critical'

interface AppError {
  id: string
  level: ErrorLevel
  message: string
  details?: any
  timestamp: Date
  context?: {
    component?: string
    action?: string
    userId?: string
    groupId?: string
  }
}

// Error Boundary Implementation
class ErrorBoundary extends React.Component<Props, State> {
  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error: {
        id: generateId(),
        level: 'error',
        message: error.message,
        details: error.stack,
        timestamp: new Date(),
        context: {
          component: 'ErrorBoundary'
        }
      }
    }
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to monitoring service
    logError(this.state.error, errorInfo)
  }
}
```

### **What**: Hasil dan Impact

#### **1. Type Safety Achievements**
- **Zero Runtime Type Errors**: Compile-time type checking
- **API Contract Enforcement**: Consistent request/response types
- **Database Type Safety**: Prisma generated types
- **Component Props Validation**: TypeScript interface enforcement

#### **2. Performance Optimizations**
- **Efficient Queries**: Proper indexing dan relationship loading
- **Minimal Data Transfer**: Only fetch required fields
- **Smart Caching**: Context-based state management
- **Optimistic Updates**: Immediate UI feedback

#### **3. Data Integrity Results**
- **ACID Compliance**: Database transaction consistency
- **Foreign Key Constraints**: Referential integrity
- **Validation Layers**: Input validation di multiple levels
- **Audit Trail**: Comprehensive logging dan timestamps

#### **4. Developer Experience**
- **IntelliSense Support**: Auto-completion untuk all types
- **Compile-time Errors**: Early error detection
- **Clear Contracts**: Well-defined interfaces
- **Easy Debugging**: Structured error handling

## 🔧 **Data Flow Optimizations**

### **1. Lazy Loading Strategy**
```typescript
// Lazy load note blocks only when needed
const useNoteBlocks = (noteId: string) => {
  const [blocks, setBlocks] = useState<NoteBlock[]>([])
  const [loading, setLoading] = useState(false)
  
  const loadBlocks = useCallback(async () => {
    if (loading) return
    
    setLoading(true)
    try {
      const response = await fetch(`/api/notes/${noteId}/blocks`)
      const { blocks } = await response.json()
      setBlocks(blocks)
    } finally {
      setLoading(false)
    }
  }, [noteId, loading])
  
  return { blocks, loading, loadBlocks }
}
```

### **2. Pagination Implementation**
```typescript
// Cursor-based pagination untuk better performance
interface PaginationCursor {
  after?: string  // ID of last item
  before?: string // ID of first item
  limit: number
}

// Usage in API
const getMessages = async (groupId: string, cursor: PaginationCursor) => {
  const messages = await prisma.message.findMany({
    where: {
      groupId,
      ...(cursor.after && { id: { gt: cursor.after } }),
      ...(cursor.before && { id: { lt: cursor.before } })
    },
    orderBy: { createdAt: 'desc' },
    take: cursor.limit,
    include: { author: true }
  })
  
  return {
    messages,
    hasMore: messages.length === cursor.limit,
    nextCursor: messages.length > 0 ? messages[messages.length - 1].id : null
  }
}
```

### **3. Real-time Data Synchronization**
```typescript
// Polling-based sync dengan smart intervals
class DataSynchronizer {
  private intervals: Map<string, NodeJS.Timeout> = new Map()
  
  startSync(key: string, fetcher: () => Promise<any>, interval: number = 2000) {
    const intervalId = setInterval(async () => {
      try {
        const data = await fetcher()
        this.updateContext(key, data)
      } catch (error) {
        console.error(`Sync error for ${key}:`, error)
      }
    }, interval)
    
    this.intervals.set(key, intervalId)
  }
  
  stopSync(key: string) {
    const intervalId = this.intervals.get(key)
    if (intervalId) {
      clearInterval(intervalId)
      this.intervals.delete(key)
    }
  }
}
```

## 🚀 **Future Data Structure Enhancements**

1. **Graph Database Integration**: Neo4j untuk complex relationship queries
2. **Event Sourcing**: Immutable event log untuk audit dan replay
3. **CQRS Pattern**: Separate read dan write models
4. **Redis Caching**: In-memory caching untuk frequently accessed data
5. **Vector Database**: Semantic search capabilities dengan embeddings
